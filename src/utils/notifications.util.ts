import { PushNotificationSubscriptionDocument } from '../modules/user/user.schema';
import webPush from 'web-push';
import { Model } from 'mongoose';

export const sendNotification = async (
  destinations: PushNotificationSubscriptionDocument[],
  message: { title: string; message: string; path: string },
  pushNotificationSubscriptionModel?: Model<PushNotificationSubscriptionDocument>,
) => {
  const messages = destinations.map(async (r) => {
    try {
      await webPush.sendNotification(
        {
          endpoint: r.endpoint,
          keys: {
            p256dh: r.public_key,
            auth: r.private_key,
          },
        },
        JSON.stringify({ ...message, data: { path: message.path } }),
      );
      return { status: 'fulfilled' };
    } catch (e) {
      if (e.statusCode === 410) {
        // Subscription is no longer valid, delete it from the database
        await pushNotificationSubscriptionModel?.deleteOne({ _id: r._id }).exec();
      }
      console.debug('Failed sending the notification [' + e + ']');
      return { status: 'rejected', error: e.message };
    }
  });

  return Promise.all(messages);
};
