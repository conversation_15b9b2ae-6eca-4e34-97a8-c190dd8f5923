import { PLAN_TYPE } from '../enums/plan.enum';
import { CURRENCIES } from '../modules/country/country.schema';
import { PAYMENT_FEE_PROFILES } from './constants';

export const transactionFeeCalculator = (amount: number) => {
  if (amount < 100_00) return 25_00;
  else if (amount < 10_000_00) return 50_00;
  else if (amount < 100_000_00) return 75_00;
  else return 100_00;
};
export const inwardZillaFee = (amount: number) => Math.ceil(Math.min(0.005 * amount));
export const zillaFeeCalculator = (amount: number) => Math.ceil(0.055 * amount);
//fee is in naira
export const paystackFeeCalculator = (
  amount: number,
  currency: CURRENCIES = CURRENCIES.NGN,
  type: string = 'internal',
) => {
  if (currency === CURRENCIES.GHC) {
    return (0.3 / 100) * amount; //paystack now adds the fees too
  }

  if (currency === CURRENCIES.ZAR) {
    return (3 / 100) * amount + 1;
  }

  return Math.ceil(Math.min(500, (0.3 / 100) * amount)); //this is low because paystack automatically adds the fees
};
export const monnifyFeeCalculator = (amount: number) => Math.ceil(Math.min(1250, (1.7 / 100) * amount));
export const blocTransferFeeCalculator = (amount: number) => Math.ceil(Math.min(700, (1.45 / 100) * amount));
export const flwOneTimaAccountFeeCalculator = (amount: number) => (1.65 / 100) * amount;
export const kpOneTimeAccountFeeCalculator = (amount: number) => Math.ceil(Math.min(1750, (1.5 / 100) * amount));
export const payazaOneTimaAccountFeeCalculator = (amount: number) => Math.ceil(Math.min(800, (1 / 100) * amount));
export const monoFeeCalculator = (amount: number) =>
  Math.ceil(Math.min(750, (1 / 100) * amount + (amount >= 1500 ? 70 : 0)));
export const startbuttonFeeCalculator = (amount: number, currency: CURRENCIES) => {
  if (currency === CURRENCIES.ZAR) {
    return (4 / 100) * amount + 1;
  }

  return (3 / 100) * amount;
};
export const thePeerFeeCalculator = (amount: number) => Math.ceil(Math.min(1250, (1.5 / 100) * amount));
export const zeepayFeeCalculator = (amount: number) => Math.ceil((1.75 / 100) * amount);
export const zeepayActualCharge = (amount: number) => (1.3 / 100) * amount;
export const stripeFeeCalculator = (amount: number) => Math.ceil((3 / 100) * amount);
export const walletFeeCalculator = (amount: number, currency: CURRENCIES = CURRENCIES.NGN, currentPlan: PLAN_TYPE) => {
  const feeProfile: { percentage: number; cap: number } =
    PAYMENT_FEE_PROFILES[currency][currentPlan ?? PLAN_TYPE.STARTER];

  return Math.min(feeProfile.cap, (feeProfile.percentage * amount) / 100); //amount/100 is weird but we know amount is in kobo & we want to avoid weird javascript float issues
};

export const leatherbackFeeCalculator = (amount: number, currency) => {
  if (currency === CURRENCIES.GBP) {
    return 0.2 * amount;
  }

  return 0.3 * amount;
};

export const conversionFeeCalculator = (amount: number) => (0.5 / 100) * amount;
export const rateMarkdownCalculator = (amount: number) => (1 / 100) * amount;

export const DEFAULT_CHOWBOT_FEE = 5;
export const chowbotMerchantFee = (amount: number, customFee?: number) =>
  Math.ceil(((customFee ?? DEFAULT_CHOWBOT_FEE) / 100) * amount);

const chowbotCustomerFeeCaps = {
  [CURRENCIES.NGN]: 500,
  [CURRENCIES.ZAR]: 8,
  [CURRENCIES.GHC]: 5,
  [CURRENCIES.KES]: 45,
};

export const chowbotCustomerFee = (amount: number, currency: CURRENCIES) =>
  Math.min(Math.ceil((1 / 100) * amount), chowbotCustomerFeeCaps[currency]); //this value is in the higher denomination naira not kobo

export const walletPaymentFeeCalculator = (amount: number) => {
  return Math.ceil(Math.min(1500, (1 / 100) * amount));
};

export const paystackTranferFeeCalculator = (amount: number, currency: CURRENCIES) => {
  if (currency === CURRENCIES.GHC) {
    return 1_00;
  }

  if (currency === CURRENCIES.ZAR) {
    return 3_00;
  }

  if (amount <= 5_000_00) return 10_00;
  else if (amount <= 50_000_00) return 25_00;
  else return 50_00;
};

export const startbuttonWithdrawalFeeCalculator = (amount: number, currency: CURRENCIES) => {
  if (currency === CURRENCIES.ZAR) {
    return 6_00;
  }

  // if (currency === CURRENCIES.KES) {
  //   if (amount <= 1_500_00) return 30_00;
  //   else if (amount <= 20_000_00) return 50_00;
  //   else return 70_00;
  // }

  return 50_00;
};

export const manualWithdrawalFeeCalculator = (amount: number, currency: CURRENCIES) => {
  if (currency === CURRENCIES.KES) {
    return 50_00;
  }

  return 3_00;
};

export const blocWithdrawalFeeCalculator = (amount: number) => {
  if (amount <= 5_000_00) return 10_00;
  else if (amount <= 50_000_00) return 20_00;
  else return 45_00;
};

export const squadWithdrawalFeeCalculator = (amount: number) => {
  return 10;
};

export const squadFeeCalculator = (amount: number) => Math.min(1000_00, (0.1 / 100) * amount);
export const SUBSCRIPTION_CARD_DISCOUNT = 5;
export const SUBSCRIPTION_DD_DISCOUNT = 7;

export const applyDiscount = (amount: number, discount: number) => amount - (amount * discount) / 100;
