import { WITHDRAWAL_PROVIDERS } from '../modules/wallets/wallet.withdrawal.schema';
import {
  startbuttonWithdrawalFeeCalculator,
  paystackTranferFeeCalculator,
  manualWithdrawalFeeCalculator,
} from './fees';
enum CURRENCIES {
  GHC = 'GHS',
  NGN = 'NGN',
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  ZAR = 'ZAR',
  KES = 'KES',
  CAD = 'CAD',
}

const minWithdrawalAmountNGN = 250_00;
const maxWithdrawalAmountNGN = 1000_001_00;

const minWithdrawalAmountGHC = 5_00;
const maxWithdrawalAmountGHC = 150_001_00;

const minWithdrawalAmountZAR = 10_00;
const maxWithdrawalAmountZAR = 150_001_00;

const minWithdrawalAmountKES = 100_00;
const maxWithdrawalAmountKES = 100_001_00;

const maxWithdrawalAmount = {
  [CURRENCIES.GHC]: maxWithdrawalAmountGHC,
  [CURRENCIES.NGN]: maxWithdrawalAmountNGN,
  [CURRENCIES.ZAR]: maxWithdrawalAmountZAR,
  [CURRENCIES.KES]: maxWithdrawalAmountKES,
};

const minWithdrawalAmount = {
  [CURRENCIES.GHC]: minWithdrawalAmountGHC,
  [CURRENCIES.NGN]: minWithdrawalAmountNGN,
  [CURRENCIES.ZAR]: minWithdrawalAmountZAR,
  [CURRENCIES.KES]: minWithdrawalAmountKES,
};

export const limit24Hr = {
  [CURRENCIES.GHC]: 100_000_00,
  [CURRENCIES.ZAR]: 100_000_00,
  [CURRENCIES.KES]: 100_000_00,
  [CURRENCIES.NGN]: 1_000_000_00,
};

export enum WITHDRAWAL_FEE_TYPES {
  'FIXED' = 'FIXED',
  'PERCENTAGE' = 'PERCENTAGE',
  'MIXED' = 'MIXED',
}

const fees = {
  [CURRENCIES.GHC]: [
    {
      floor: minWithdrawalAmountGHC,
      ceil: maxWithdrawalAmountGHC,
      fee: 1_00,
      percentage: 0,
      feeType: WITHDRAWAL_FEE_TYPES.FIXED,
      rangeDesc: `GHS 5 to GHS 150,000`,
      feeDesc: 'GHS 1',
    },
    // {
    //   floor: 20_000_00,
    //   ceil: maxWithdrawalAmountGHC,
    //   fee: 2_00,
    //   percentage: 0.0005,
    //   feeType: WITHDRAWAL_FEE_TYPES.FIXED,
    //   rangeDesc: `GHS 20,000 to GHS 150,000`,
    //   feeDesc: 'GHS 2 + 0.05%',
    // },
  ],
  [CURRENCIES.NGN]: [
    {
      floor: minWithdrawalAmountNGN,
      ceil: maxWithdrawalAmountNGN,
      fee: 10_00,
      percentage: 0,
      feeType: WITHDRAWAL_FEE_TYPES.FIXED,
      rangeDesc: 'NGN 250 to NGN 1M',
      feeDesc: 'NGN 10',
    },
    // {
    //   floor: 1000_00,
    //   ceil: 10_000_00,
    //   fee: 50_00,
    //   percentage: 0,
    //   feeType: WITHDRAWAL_FEE_TYPES.FIXED,
    //   rangeDesc: 'NGN 1,000 to NGN 9999',
    //   feeDesc: 'NGN 50',
    // },
    // {
    //   floor: 10_000_00,
    //   ceil: 45_000_00,
    //   fee: 75_00,
    //   percentage: 0,
    //   feeType: WITHDRAWAL_FEE_TYPES.FIXED,
    //   rangeDesc: 'NGN 10,000 to NGN 44,999',
    //   feeDesc: 'NGN 75',
    // },
    // {
    //   floor: 45_000_00,
    //   ceil: maxWithdrawalAmountNGN,
    //   fee: 0.002,
    //   percentage: 0,
    //   feeType: WITHDRAWAL_FEE_TYPES.PERCENTAGE,
    //   rangeDesc: 'Greater than NGN 45,000',
    //   feeDesc: '0.2%',
    // },
  ],
  [CURRENCIES.ZAR]: [
    {
      floor: minWithdrawalAmountZAR,
      ceil: maxWithdrawalAmountZAR,
      fee: 4_00,
      percentage: 0,
      feeType: WITHDRAWAL_FEE_TYPES.FIXED,
      rangeDesc: 'ZAR 10 to ZAR 150K',
      feeDesc: 'ZAR 4',
    },
  ],
  [CURRENCIES.KES]: [
    {
      floor: minWithdrawalAmountKES,
      ceil: maxWithdrawalAmountKES,
      fee: 50_00,
      percentage: 0,
      feeType: WITHDRAWAL_FEE_TYPES.FIXED,
      rangeDesc: 'KES 100 to KES 100K',
      feeDesc: 'KES 50',
    },
    // {
    //   floor: 1500_00,
    //   ceil: 20_000_00,
    //   fee: 40_00,
    //   percentage: 0,
    //   feeType: WITHDRAWAL_FEE_TYPES.FIXED,
    //   rangeDesc: 'KES 1500 to KES 20K',
    //   feeDesc: 'KES 40',
    // },
    // {
    //   floor: 20_000_00,
    //   ceil: maxWithdrawalAmountKES,
    //   fee: 60_00,
    //   percentage: 0,
    //   feeType: WITHDRAWAL_FEE_TYPES.FIXED,
    //   rangeDesc: 'KES 20K to KES 80K',
    //   feeDesc: 'KES 60',
    // },
  ],
};

const chowbotFees = {
  [CURRENCIES.GHC]: [
    {
      floor: minWithdrawalAmountGHC,
      ceil: 20_000_00,
      fee: 2_00,
      percentage: 0.001,
      feeType: WITHDRAWAL_FEE_TYPES.FIXED,
      rangeDesc: `GHS 5 to GHS 20,000`,
      feeDesc: 'GHS 2 + 0.1%',
    },
    {
      floor: 20_000_00,
      ceil: maxWithdrawalAmountGHC,
      fee: 2_00,
      percentage: 0.0005,
      feeType: WITHDRAWAL_FEE_TYPES.FIXED,
      rangeDesc: `GHS 20,000 to GHS 150,000`,
      feeDesc: 'GHS 2 + 0.05%',
    },
  ],
  [CURRENCIES.NGN]: [
    {
      floor: minWithdrawalAmountNGN,
      ceil: 5000_00,
      fee: 20_00,
      percentage: 0,
      feeType: WITHDRAWAL_FEE_TYPES.FIXED,
      rangeDesc: 'NGN 250 to NGN 4,999',
      feeDesc: 'NGN 20',
    },
    {
      floor: 5000_00,
      ceil: 50_000_00,
      fee: 50_00,
      percentage: 0,
      feeType: WITHDRAWAL_FEE_TYPES.FIXED,
      rangeDesc: 'NGN 5000 to NGN 49,999',
      feeDesc: 'NGN 50',
    },
    {
      floor: 50_000_00,
      ceil: maxWithdrawalAmountNGN,
      fee: 75_00,
      percentage: 0,
      feeType: WITHDRAWAL_FEE_TYPES.FIXED,
      rangeDesc: 'Greater than NGN 50,000',
      feeDesc: 'NGN 75',
    },
  ],
};

const calculateWithdrawalFee = (amount: number, currency: CURRENCIES, usesChowbot: boolean = false) => {
  const feesToUse = usesChowbot ? chowbotFees : fees;
  const feeBand = feesToUse[currency].find((band) => amount >= band.floor && amount < band.ceil);

  const fee = !feeBand
    ? (0.25 / 100) * amount
    : feeBand?.feeType === WITHDRAWAL_FEE_TYPES.FIXED
    ? feeBand?.fee + (feeBand?.percentage ?? 0) * amount
    : feeBand?.fee * amount;

  return fee;
};

export const defaultWithdrawalProvider = {
  [CURRENCIES.GHC]: WITHDRAWAL_PROVIDERS.PAYSTACK,
  [CURRENCIES.NGN]: WITHDRAWAL_PROVIDERS.PAYSTACK,
  [CURRENCIES.ZAR]: WITHDRAWAL_PROVIDERS.MANUAL,
  [CURRENCIES.KES]: WITHDRAWAL_PROVIDERS.STARTBUTTON,
};

export const withdrawalProviders = {
  [CURRENCIES.GHC]: [WITHDRAWAL_PROVIDERS.PAYSTACK],
  [CURRENCIES.NGN]: [WITHDRAWAL_PROVIDERS.PAYSTACK, WITHDRAWAL_PROVIDERS.SQUAD],
  [CURRENCIES.ZAR]: [WITHDRAWAL_PROVIDERS.STARTBUTTON, WITHDRAWAL_PROVIDERS.PAYSTACK, WITHDRAWAL_PROVIDERS.MANUAL],
  [CURRENCIES.KES]: [WITHDRAWAL_PROVIDERS.STARTBUTTON],
};

export const withdrawalFeeCalculators = {
  [WITHDRAWAL_PROVIDERS.STARTBUTTON]: startbuttonWithdrawalFeeCalculator,
  [WITHDRAWAL_PROVIDERS.MANUAL]: manualWithdrawalFeeCalculator,
  [WITHDRAWAL_PROVIDERS.PAYSTACK]: paystackTranferFeeCalculator,
};

export { fees, chowbotFees, calculateWithdrawalFee, minWithdrawalAmount, maxWithdrawalAmount };
