const crypto = require('crypto');
import { Types, isValidObjectId } from 'mongoose';
import { COUNTRY_CODE, CURRENCIES } from '../modules/country/country.schema';
import { Subscription } from '../modules/subscription/subscription.schema';
import { PlanOption } from '../modules/plan/plan-options/plan-options.schema';
import { SUBSCRIPTION_STATUS } from '../enums/payment.enum';
import { currencyPairsToSkip } from './constants';
import { CurrencyRates } from '../modules/wallets/currency-conversion/currency-rate.schema';
import { Item } from '../modules/item/item.schema';

export function arrayToMap<T>(array: T[], prop: string) {
  const map: { [key: string]: T } = {};

  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    const key = String(item[prop]);
    if (key !== undefined) map[key] = { ...item };
  }
  return map;
}

export function millify(num: number, fractionDigits: number = 0) {
  if (!num) return 0;

  // if (num % 1 === 0) {
  //   fractionDigits = 0;
  // }

  function getStr() {
    if (num > 999 && num < 1000000) {
      return (num / 1000).toFixed(0) + 'K'; // convert to K for number from > 1000 < 1 million
    } else if (num >= 1000000) {
      return (num / 1000000).toFixed(fractionDigits) + 'M'; // convert to M for number from > 1 million
    } else if (num < 1000) {
      return num.toFixed(0); // if value < 1000, nothing to do
    }
  }

  switch (Math.sign(num)) {
    case -1:
      num = Math.abs(num);
      return '-' + getStr();
    case 1:
      return getStr();
    default:
      return getStr();
  }
}

export function getCurrencyFromCountryCode(countryCode: COUNTRY_CODE) {
  const currencyMap = {
    [COUNTRY_CODE.NG]: CURRENCIES.NGN,
    [COUNTRY_CODE.GH]: CURRENCIES.GHC,
    [COUNTRY_CODE.KE]: CURRENCIES.KES,
    [COUNTRY_CODE.ZA]: CURRENCIES.ZAR,
  };

  return currencyMap[countryCode];
}

export function toNaira(amount: number) {
  return amount / 100;
}

export function toKobo(amount: number) {
  return amount * 100;
}

export function stripUndefinedAndNull(obj) {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, value]) => value !== undefined && value !== null && value !== ''),
  );
}

export function paramsFromObject(payload: any) {
  const params = new URLSearchParams();
  Object.keys(payload).map((key) => {
    if (payload[key]) {
      if (typeof payload[key] === 'object') {
        Object.keys(payload[key]).map((key2) => {
          params.set(`${key}[${key2}]`, payload[key][key2]);
        });
      } else {
        params.set(key, payload[key]);
      }
    }
  });
  return params;
}

export const stripBase64Strings = (file: string) => file.replace(/^data:image\/[a-zA-Z]+;base64,/, '');

export const getDocId = (doc: any, bypassValidation = false): string => {
  if (doc) {
    if (typeof doc === 'string' && (isValidObjectId(doc) || bypassValidation)) return doc;
    else if (typeof doc === 'object' || doc.id || doc._id) return doc._id ?? doc.id;
  }

  return null;
};

export function to<T>(value: any): T {
  return value;
}

export const cleanString = (text: string, removeSpecialChars: boolean = false) => {
  const emojiRegex = /[\p{Emoji_Presentation}\p{Extended_Pictographic}]/gu;
  const newLineTabRegex = /[\n\t\r]+/g;

  let cleanedString = text.replace(emojiRegex, '');
  cleanedString = cleanedString.replace(newLineTabRegex, ' ');

  if (removeSpecialChars) {
    cleanedString = cleanedString.replace(/[^a-zA-Z\s]/g, '');
  }

  cleanedString = cleanedString.trim();
  return cleanedString;
};

export class DeepSet<T> {
  private entries: T[] = [];
  private hashes: string[];

  constructor(array?: T[]) {
    if (array) this.entries.push(...array);
  }

  add(data: T) {
    if (typeof data === 'object') {
      const hash = JSON.stringify(data);
      if (!this.hashes.includes(hash)) {
        this.hashes.push(hash);
        this.entries.push(data);
      }
      return;
    }

    if (!this.entries.includes(data)) {
      this.entries.push(data);
    }
  }

  getEntries() {
    return this.entries;
  }

  clearHashes() {
    this.hashes.splice(0, this.hashes.length);
  }

  getLength() {
    return this.entries?.length;
  }
}

export function generateOTP(salt) {
  // Get the current time in seconds and round it down to the nearest minute
  const epoch = Math.floor(Date.now() / 1000);
  const timeWindow = Math.floor(epoch / 60); // 1 minute time window

  // Combine the salt with the time window
  const data = salt + timeWindow.toString();

  // Generate an HMAC using SHA-256 with the combined data
  const hmac = crypto.createHmac('sha256', salt);
  hmac.update(data);
  const hash = hmac.digest('hex');

  // Convert the hash to a 6-digit code
  const offset = parseInt(hash.substr(-2), 16) % 8;
  const binaryCode = parseInt(hash.substr(offset * 2, 8), 16);
  const otp = (binaryCode % 1000000).toString().padStart(6, '0');

  return otp;
}

export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const result: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize);
    result.push(chunk);
  }
  return result;
}

export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function calculateSubscriptionAmountLeft(
  subscription: Subscription,
  planOption: PlanOption,
  currentDate: Date = new Date(),
): number {
  // Check if subscription is active
  if (subscription.status !== SUBSCRIPTION_STATUS.ACTIVE) {
    return 0;
  }

  // Ensure necessary dates are available
  if (!subscription.last_payment_reference || !subscription.last_payment_date || !subscription.next_payment_date) {
    return 0;
  }

  const next_payment_date = new Date(subscription.next_payment_date);
  const last_payment_date = new Date(subscription.last_payment_date);
  // Calculate total duration of the subscription in milliseconds
  const totalDurationMs = next_payment_date.getTime() - last_payment_date.getTime();

  // Handle invalid durations
  if (totalDurationMs <= 0) {
    return 0;
  }

  // Calculate remaining time in milliseconds
  const timeLeftMs = next_payment_date.getTime() - currentDate.getTime();

  // If the subscription has expired
  if (timeLeftMs <= 0) {
    return 0;
  }

  // Calculate the proportion of time left
  const proportionLeft = timeLeftMs / totalDurationMs;

  // Calculate the amount left
  const amountLeft = Math.floor(planOption.amount * proportionLeft);

  return amountLeft;
}

export function generateCombinedName(businessName, ownerName) {
  const maxLength = 30;
  const maxOwnerLength = 10;
  const byText = !!ownerName ? ' by ' : '';

  let combinedLength = businessName.length + byText.length + ownerName.length;

  if (combinedLength > maxLength && ownerName.length > maxOwnerLength) {
    ownerName = ownerName.slice(0, maxOwnerLength);
    combinedLength = businessName.length + byText.length + ownerName.length; //compute combinedLength again
  }

  if (combinedLength > maxLength) {
    let excessLength = combinedLength - maxLength;
    businessName = businessName.slice(0, businessName.length - excessLength);
  }

  return `${businessName}${byText}${ownerName}`;
}

export function deepMerge(target, source) {
  if (typeof target !== 'object' || target === null) {
    return source; // If target is not an object, replace it with the source
  }

  if (typeof source !== 'object' || source === null) {
    return target; // If source is not an object, keep the target
  }

  // Iterate through all keys in the source object
  for (const key of Object.keys(source)) {
    if (key in target) {
      // If the key exists in both, merge recursively
      target[key] = deepMerge(target[key], source[key]);
    } else {
      // If the key does not exist in target, copy it directly
      target[key] = source[key];
    }
  }

  return target;
}

export const isPairSupported = (baseCurrency: CURRENCIES, quoteCurrency: CURRENCIES): boolean => {
  if (baseCurrency === quoteCurrency) return false;

  // Skip pairs involving CAD
  if (baseCurrency === CURRENCIES.CAD || quoteCurrency === CURRENCIES.CAD) {
    return false;
  }

  // Skip specific unsupported pairs
  const pair = `${baseCurrency}/${quoteCurrency}`;
  if (currencyPairsToSkip.has(pair)) {
    return false;
  }

  return true;
};

export const toUnixTimestamp = (date) => {
  const dateObject = new Date(date);
  if (isNaN(dateObject.getTime())) {
    throw new Error('Invalid date provided');
  }
  return Math.floor(dateObject.getTime() / 1000);
};

export function createSubdomainURL(baseURL, slug) {
  try {
    if (!slug || typeof slug !== 'string') {
      throw new Error('Invalid slug provided.');
    }

    // Normalize the base URL
    const url = new URL(baseURL);

    // Construct the new URL with the slug as a subdomain
    const subdomainURL = `${url.protocol}//${slug}.${url.hostname}`;
    return subdomainURL;
  } catch (error) {
    console.error(error.message);
    return null;
  }
}

export function dateToYYYYMMDD(input: string | Date): string {
  // Convert input to a Date object if it's a string
  const date = typeof input === 'string' ? new Date(input) : input;

  if (isNaN(date.getTime())) {
    throw new Error('Invalid date input');
  }

  // Get year, month, and day with leading zeroes if needed
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
  const day = date.getDate().toString().padStart(2, '0');

  return `${year}-${month}-${day}`;
}

function computeMissingRates(rates: CurrencyRates['rates'], baseCurrency: CURRENCIES) {
  if (!rates[baseCurrency]) {
    throw new Error(`Base currency ${baseCurrency} does not exist in the rates object.`);
  }

  const usdRate = rates[baseCurrency]['USD'];
  if (!usdRate) {
    throw new Error(`No USD rate available for base currency ${baseCurrency}.`);
  }

  for (const targetCurrency in rates) {
    if (targetCurrency === baseCurrency) continue;
    if (rates[baseCurrency][targetCurrency]) continue;

    const targetRateFromUSD = rates['USD'][targetCurrency];
    if (targetRateFromUSD) {
      rates[baseCurrency][targetCurrency] = usdRate * targetRateFromUSD;
    }
  }

  return rates[baseCurrency];
}

export function getExchangeRates(
  allRates: CurrencyRates['rates'],
  defaultCurrency: CURRENCIES,
  allCurrencies: CURRENCIES[],
) {
  try {
    if (allCurrencies.length > 1 || (allCurrencies.length === 1 && allCurrencies[0] !== defaultCurrency)) {
      const rates = computeMissingRates(allRates, defaultCurrency);

      return rates;
    }

    return { [defaultCurrency]: 1 };
  } catch (error) {
    console.log('Error setting conversion rates', error);

    return null;
  }
}

export function arrayToSentence(arr: string[]): string {
  if (arr.length === 0) {
    return '';
  }
  if (arr.length === 1) {
    return arr[0];
  }
  if (arr.length === 2) {
    return `${arr[0]} and ${arr[1]}`;
  }
  const lastElement = arr.pop();
  return `${arr.join(', ')} and ${lastElement}`;
}

export function objectToQueryParams(obj: Record<string, any>): string {
  const queryParams = new URLSearchParams();

  Object.entries(obj).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });

  return queryParams.toString();
}

export const hashForMeta = (input: string) =>
  crypto.createHash('sha256').update(input.trim().toLowerCase()).digest('hex');


export const getProductItemThumbnail = (product: Item) => {
  const img: string =
    (product?.thumbnail_type ?? "image") == "image"
      ? product?.images?.[product.thumbnail]
      : product?.videos?.[product.thumbnail]?.thumbnail;
  return img;
};