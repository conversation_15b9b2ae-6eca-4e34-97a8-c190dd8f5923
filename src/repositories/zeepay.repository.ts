import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { ConfigService } from '@nestjs/config';
import { Errorable } from '../utils/types';
import { registerErrors } from '../utils/errors.util';
import { extractErrorDetails } from '../utils/axios-errors';
import { ZeepayConfig } from '../config/types/zeepay.config';
import { CacheService } from '../modules/shared/cache/cache.service';

interface ZeepayOAuthTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
}

export interface ZeepayDebitWalletRequest {
  customerName: string;
  mno: string;
  amount: number;
  msisdn: string;
  description: string;
  reference: string;
  callback_url?: string;
}

export interface ZeepayWebhookPayload {
  zeepay_id: number;
  reference: string;
  status: string;
  message: string;
  gateway_id: string;
}

export interface DebitWalletResponse {
  code: number;
  zeepay_id: number;
  amount: number;
  message: string;
}

@Injectable()
export class ZeepayRepository {
  private axios: AxiosInstance;
  private config: ZeepayConfig;
  private readonly tokenCacheKey = 'zeepay_oauth_token'; // Key to store the token in Redis

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: Logger,
    private readonly cacheService: CacheService, // Inject the cache service
  ) {
    this.config = configService.get<ZeepayConfig>('zeepayConfiguration');

    this.axios = Axios.create({
      baseURL: this.config.base_url,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  // Method to get OAuth token
  async getOAuthToken(): Promise<ZeepayOAuthTokenResponse> {
    const payload = {
      grant_type: 'password',
      client_id: this.config.client_id,
      client_secret: this.config.client_secret,
      username: this.config.username,
      password: this.config.password,
    };

    try {
      const res = await this.axios.post<ZeepayOAuthTokenResponse>('/oauth/token', new URLSearchParams(payload), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      return res?.data;
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.log(error);
      registerErrors(error);
      throw new Error(error?.message);
    }
  }

  // Method to get token from cache or auth endpoint
  async getCachedOrNewToken(): Promise<string> {
    // Check if the token exists in the cache
    const cachedToken = await this.cacheService.get<string>(this.tokenCacheKey);

    if (cachedToken) {
      return cachedToken;
    }

    // If no cached token, fetch a new one
    const newTokenResponse = await this.getOAuthToken();

    // Set the token in Redis with TTL matching its expiry time
    await this.cacheService.setWithLock(this.tokenCacheKey, newTokenResponse.access_token, newTokenResponse.expires_in);

    return newTokenResponse.access_token;
  }

  // Debit wallet method with token caching logic
  async debitWallet(info: ZeepayDebitWalletRequest): Promise<Errorable<DebitWalletResponse>> {
    try {
      // Get the OAuth token (either from cache or a new one)
      const accessToken = await this.getCachedOrNewToken();
      info.callback_url = process.env.CATLOG_API + '/webhooks/zeepay';

      // Call the debit wallet API
      const res = await this.axios.post<DebitWalletResponse>(
        '/api/custom/transactions/tech-maxx/wallets/debit-wallet',
        info,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      console.log('ZEEPAY RESPONSE', res?.data);

      return { data: res?.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.log(error);
      registerErrors(error);
      return { error: error?.message };
    }
  }
}
