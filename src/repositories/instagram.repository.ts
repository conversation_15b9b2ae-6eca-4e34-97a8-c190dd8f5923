import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { default as Axios, AxiosInstance, default as axios } from 'axios';
import { BrokerTransportService } from '../broker/broker-transport.service';
import { InstagramConfig } from '../config/types/instagram.config';
import { registerErrors } from '../utils/errors.util';
import { paramsFromObject } from '../utils/functions';
import { Errorable } from '../utils/types';
import { extractErrorDetails } from '../utils/axios-errors';

export interface InstagramMedia {
  id: string;
  media_type: string;
  media_url: string;
  permalink?: string;
  caption?: string;
  thumbnail_url?: string;
}

@Injectable()
export class InstagramRepository {
  private config: InstagramConfig;
  private readonly axios: AxiosInstance;

  constructor(
    private readonly logger: Logger,
    configService: ConfigService,
    private readonly brokerTransport: BrokerTransportService,
  ) {
    this.config = configService.get<InstagramConfig>('instagramConfiguration');
    this.axios = Axios.create({
      baseURL: 'https://graph.instagram.com/',
    });
  }

  async getAccessToken(access_code: string, redirect_uri: string): Errorable<any> {
    try {
      const data = {
        client_id: this.config.app_key,
        client_secret: this.config.app_secret,
        grant_type: 'authorization_code',
        redirect_uri,
        code: access_code,
      };

      const form = new URLSearchParams();
      Object.keys(data).forEach((k) => form.append(k, data[k]));

      const shortLivedTokenRes = await axios.post('https://api.instagram.com/oauth/access_token', form, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      });

      const params = paramsFromObject({
        grant_type: 'ig_exchange_token',
        client_secret: this.config.app_secret,
        access_token: shortLivedTokenRes.data?.access_token,
      });

      const res = await this.axios.get(`access_token?${params}`);

      return res;
    } catch (err) {
      const errorData = extractErrorDetails(err);
      console.log('INSTAGRAM ACCESS TOKEN ERROR');
      console.log(err?.response?.data);
      console.log(errorData?.details);
      // this.logger.error(err);
      // registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async refreshLongLivedAccessToken(access_token: string): Errorable<any> {
    try {
      const params = paramsFromObject({
        grant_type: 'ig_refresh_token',
        access_token,
      });

      const res = await this.axios.get(`refresh_access_token?${params}`);
      return res;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async getMedia(payload: {
    access_token: string;
    pagination?: {
      next?: string;
      previous?: string;
      limit?: number;
    };
  }): Errorable<InstagramMedia[]> {
    try {
      const params = paramsFromObject({
        fields: 'id,caption,media_type,media_url,thumbnail_url,permalink,children',
        access_token: payload.access_token,
        before: payload.pagination?.previous,
        after: payload.pagination?.next,
        limit: payload.pagination?.limit,
      });

      const res = await this.axios.get(`me/media?${params}`);
      return res.data;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async getAlbumMedia(payload: { access_token: string; media_id: string }): Errorable<any> {
    try {
      const params = paramsFromObject({
        fields: 'id,media_type,media_url,thumbnail_url',
        access_token: payload.access_token,
      });

      const res = await this.axios.get(`${payload.media_id}/children?${params}`);
      return res.data;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }

  async getInstagramUser(access_token: string): Errorable<any> {
    try {
      const params = paramsFromObject({
        fields: 'id,media_count,username,account_type',
        access_token: access_token,
      });

      const res = await this.axios.get(`me?${params}`);
      return res.data;
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);
      return { error: err.response?.data?.error_message };
    }
  }
}
