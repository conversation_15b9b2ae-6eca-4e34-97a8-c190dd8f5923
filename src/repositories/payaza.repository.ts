import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { PayazaConfig } from '../config/types/payaza.config';
import { ConfigService } from '@nestjs/config';
import { Errorable } from '../utils/types';
import { extractErrorDetails } from '../utils/axios-errors';

interface ICreateOneTimeVirtualAccountPayload {
  account_name: string;
  account_type: 'Dynamic' | 'Static';
  bank_code: string;
  bvn?: string;
  account_reference: string;
  customer_first_name: string;
  customer_last_name: string;
  customer_email: string;
  customer_phone_number: string;
  transaction_description?: string;
  transaction_amount: number;
  expires_in_minutes?: number;
  webhook_url?: string;
}

interface ICreateOneTimeVirtualAccountResponse {
  message: string;
  data: {
    account_name: string;
    account_number: string;
    account_type: string;
    bank_name: string;
    account_reference: string;
    transaction_id: number;
    transaction_amount_payable: number;
    transaction_reference: string;
    expires_in_minutes: number;
  };
  success: boolean;
}

interface ICreateReservedAccountPayload {
  account_name: string;
  account_type: string;
  bank_code: string;
  bvn?: string;
  account_reference: string;
  customer_first_name?: string;
  customer_last_name?: string;
  customer_email?: string;
  customer_phone_number?: string;
}

interface ICreateReservedAccountResponse {
  message: string;
  data: {
    account_name: string;
    account_number: string;
    account_type: string;
    bank_name: string;
    account_reference: string;
  };
  success: boolean;
}

interface ITransactionStatusData {
  transaction_reference: string;
  amount_received: number;
  transaction_fee: number;
  transaction_status: string;
  sender_name: string;
  sender_account_number: string;
  source_bank_name: string | null;
  initiated_date: string;
  current_status_date: string;
  currency: string;
  session_id: string;
  merchant_transaction_reference: string;
  transaction_type: string;
  virtual_account_number: string;
  status_reason: string;
}

interface IGetTransactionStatusResponse {
  message: string;
  data: ITransactionStatusData;
  success: boolean;
}

@Injectable()
export class PayazaRepository {
  private payazaConfig: PayazaConfig;
  private readonly axios: AxiosInstance;
  private readonly token: string;

  constructor(config: ConfigService, private readonly logger: Logger) {
    this.payazaConfig = config.get<PayazaConfig>('payazaConfiguration');

    const publicKey = this.payazaConfig.publicKey || '';
    const encodedKey = Buffer.from(publicKey).toString('base64');
    this.token = `Payaza ${encodedKey}`;

    this.axios = Axios.create({
      baseURL: 'https://api.payaza.africa/',
      headers: {
        Authorization: this.token,
        'Content-Type': 'application/json',
      },
    });
  }

  async createOneTimeVirtualAccount(
    payload: ICreateOneTimeVirtualAccountPayload,
  ): Errorable<ICreateOneTimeVirtualAccountResponse> {
    try {
      const { data } = await this.axios.post<ICreateOneTimeVirtualAccountResponse>(
        '/live/merchant-collection/merchant/virtual_account/generate_virtual_account/',
        payload,
      );

      this.logger.log(data, 'Payaza create OneTime virtual account success');
      return { data };
    } catch (err) {
      const errorDetails = extractErrorDetails(err);
      this.logger.error(err, 'Payaza create OneTime virtual account failed');
      return {
        error: errorDetails.message,
      };
    }
  }

  async createReservedAccountForCustomers(
    payload: ICreateReservedAccountPayload,
  ): Errorable<ICreateReservedAccountResponse> {
    try {
      const { data } = await this.axios.post<ICreateReservedAccountResponse>(
        '/live/merchant-collection/merchant/virtual_account/generate_virtual_account/',
        payload,
      );

      this.logger.log(data, 'Payaza create reserved account for customers success');
      return { data };
    } catch (err) {
      const errorDetails = extractErrorDetails(err);
      this.logger.error(err, 'Payaza create reserved account for customers failed');
      return {
        error: errorDetails.message,
      };
    }
  }

  async getTransactionStatus(transaction_reference: string): Errorable<IGetTransactionStatusResponse> {
    try {
      const { data } = await this.axios.get<IGetTransactionStatusResponse>(
        '/live/merchant-collection/transfer_notification_controller/transaction-query',
        {
          params: {
            transaction_reference,
          },
        },
      );

      this.logger.log(data, 'Payaza get transaction status success');
      return { data };
    } catch (err) {
      const errorDetails = extractErrorDetails(err);
      this.logger.error(err, 'Payaza get transaction status failed');
      return {
        error: errorDetails.message,
      };
    }
  }
}
