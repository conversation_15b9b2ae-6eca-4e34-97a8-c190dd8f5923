import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CustomerIoConfig } from '../config/types/customer-io.config';
import { TrackClient, RegionUS } from 'customerio-node';
import { User, UserDocument } from '../modules/user/user.schema';
import { BrokerTransportService } from '../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../enums/broker.enum';
import { getAppEnv } from '../utils';

export interface CustomerIoUserObject {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  country: string;
  created_at: number;
  kyc_status: string;
  phone: string;
  referred_by: string;
  bank_account_created: boolean;
  products_count: number;
  no_of_subscription_payments_made: number;
  store_name: string;
  store_link: string;
  store_country: string;
  store_logo: string;
  business_category: string;
  plan: string;
  plan_interval: string;
  store_type: string;
}

interface CustomerIoTrackEvent {
  userId: string;
  name: string;
  data: {
    [key: string]: string | number | boolean;
  };
}

@Injectable()
export class CustomerIoRepository {
  private customerIo;
  private customerIoConfig: CustomerIoConfig;

  constructor(private readonly config: ConfigService, private readonly brokerTransport: BrokerTransportService) {
    this.customerIoConfig = config.get<CustomerIoConfig>('customerIoConfiguration');
    this.customerIo = new TrackClient(this.customerIoConfig.siteId, this.customerIoConfig.apiKey, {
      region: RegionUS,
      timeout: 30000,
    });
  }

  async createOrUpdateUser(data: Partial<CustomerIoUserObject>) {
    if (this.customerIoConfig.isTesting || getAppEnv() !== 'production') return new Promise((res) => res(undefined));

    const { email, id, ...rest } = data;

    try {
      const res = await this.customerIo.identify(id, {
        email,
        ...rest,
      });

      const user = await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.CUSTOMER_IO_STATUS, {
          id,
          status: true,
        })
        .toPromise();

      return { data: true };
    } catch (error) {
      return { error };
    }
  }

  async trackUserEvent(data: CustomerIoTrackEvent) {
    if (this.customerIoConfig.isTesting || getAppEnv() !== 'production') return new Promise((res) => res(undefined));

    try {
      const res = await this.customerIo.track(data.userId, {
        name: data.name,
        data: data.data,
      });

      return { data: true };
    } catch (error) {
      return { error };
    }
  }

  async deleteUser(userId: string) {
    if (this.customerIoConfig.isTesting || getAppEnv() !== 'production') return new Promise((res) => res(undefined));

    try {
      const res = await this.customerIo.destroy(userId);

      const user = await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.CUSTOMER_IO_STATUS, {
          id: userId,
          status: false,
        })
        .toPromise();

      return { data: true };
    } catch (error) {
      return { error };
    }
  }
}
