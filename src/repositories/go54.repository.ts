import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Errorable } from '../utils/types';
import * as crypto from 'crypto';
import { CacheService } from '../modules/shared/cache/cache.service';
import { Go54Config } from '../config/types/go54.config';
import { COUNTRY_CODE } from '../modules/country/country.schema';
import allowedTLDs from '../utils/allowed-tlds.json';

export interface DomainAvailabilityCheckResult {
  available: boolean;
  domain: string;
  price?: number;
  currency?: string;
  message?: string;
  alternatives?: {
    domain: string;
    available: boolean;
    price?: number;
    currency?: string;
  }[];
}

export interface DomainRegistrationResult {
  success: boolean;
  domain: string;
  registrationId: string;
  expiresAt: string;
  message?: string;
}

export interface DomainInfo {
  domain: string;
  registrationId: string;
  status: 'active' | 'pending' | 'failed' | 'expired';
  expiresAt: string;
  registeredAt: string;
  nameservers?: string[];
}

export interface Go54LoginResponse {
  status: boolean;
  code: string;
  message: string;
  data: {
    user: {
      id: number;
      uid: string;
      email: string;
      first_name: string;
      last_name: string;
      phone_number: string;
      full_name: string;
      wallet?: {
        available_balance: string;
      };
      [key: string]: any;
    };
    token: string;
    token_expires: string;
    token_refresh_code: string;
    [key: string]: any;
  };
}

export interface UserInfo {
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
}

export interface Go54ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  companyName?: string;
}

/**
 * Internal structure for the new Go54 /whois endpoint response data part
 */
interface Go54WhoisData {
  availability: boolean;
  domain: string;
  amount?: {
    year_1?: number;
    [key: string]: any;
  };
  [key: string]: any; // Allow other properties
}

/**
 * Request structure for updating DNS record
 */
interface UpdateDnsRecordRequest {
  domain: string;
  ttl: number;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'SRV' | 'CAA'; // Add other relevant types if needed
  name: string;
  ipv4?: string; // For A records
  // Add other potential fields based on record type (e.g., ipv6, content, priority, etc.)
}

/**
 * Response structure for updating DNS record
 */
interface UpdateDnsRecordResponse {
  status: boolean;
  code: string;
  message: string;
}

/**
 * Repository for interacting with Go54 Domain API
 */
@Injectable()
export class Go54Repository {
  private axios: AxiosInstance;
  private readonly baseUrl: string;
  private readonly go54Config: Go54Config;
  private readonly tokenCacheKey = 'go54_auth_token';

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: Logger,
    private readonly cacheService: CacheService,
  ) {
    this.go54Config = this.configService.get<Go54Config>('go54Configuration');

    if (!this.go54Config) {
      throw new Error('Go54 configuration is not defined');
    }

    this.baseUrl = this.go54Config.apiUrl;

    this.initializeAxios();

    this.logger.setContext('Go54Repository');
  }

  /**
   * Initialize Axios instance without auth token
   */
  private initializeAxios(): void {
    this.axios = axios.create({
      baseURL: this.baseUrl,
      // Headers including Authorization will be set per-request
    });
  }

  /**
   * Check domain availability using Go54 API v2 /whois endpoint for multiple TLDs in parallel.
   * @param domain The domain name to check (e.g., "example.com")
   * @returns Result containing availability and pricing information for the requested domain and alternatives.
   */
  async checkDomainAvailability(
    originalDomain: string,
    country: COUNTRY_CODE,
  ): Errorable<DomainAvailabilityCheckResult> {
    const tlds = await this.getTLDs(country);

    if (tlds.length === 0) {
      this.logger.warn('No TLDs configured for parallel domain availability check.');

      // Optionally, perform a single check for the original domain or return an error/empty result
      // Performing a single check for the original domain:
      const singleResult = await this.checkSingleDomainViaGo54Whois(originalDomain);
      if (singleResult.error) {
        return { error: singleResult.error };
      }
      return {
        data: {
          ...singleResult.data,
          alternatives: [], // No alternatives checked
        },
      };
    }

    // Extract base name, e.g., "example" from "example.com"
    // const domainParts = originalDomain.split('.');
    // const baseName = domainParts.length > 1 ? domainParts.slice(0, -1).join('.') : originalDomain;
    // const originalTld = domainParts.length > 1 ? `.${domainParts[domainParts.length - 1]}` : '';

    const { baseName, tld: originalTld } = await getDomainParts(originalDomain);

    // we should check if originalTLD is a vaild tld
    if (!allowedTLDs.includes(originalTld)) {
      return {
        error: `.${originalTld} is not a valid domain extension`,
      };
    }

    // Ensure the original TLD is included if provided, and construct domains to check
    const uniqueTlds = new Set(tlds.map((tld) => (tld.startsWith('.') ? tld : `.${tld}`)));
    if (originalTld) {
      uniqueTlds.add(`.${originalTld}`);
    }

    const domainsToCheck = Array.from(uniqueTlds).map((tld) => `${baseName}${tld}`);

    try {
      this.logger.debug(`Checking availability for domains: ${domainsToCheck.join(', ')}`);
      const results = await Promise.allSettled(domainsToCheck.map((d) => this.checkSingleDomainViaGo54Whois(d)));

      const successfulResults: { domain: string; available: boolean; price?: number; currency?: string }[] = [];
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && !result.value.error) {
          successfulResults.push(result.value.data);
        } else if (result.status === 'rejected') {
          this.logger.error(`Error checking domain ${domainsToCheck[index]}: ${result.reason}`);
        } else if (result.status === 'fulfilled' && result.value.error) {
          this.logger.error(
            `API error checking domain ${domainsToCheck[index]}: ${JSON.stringify(result.value.error)}`,
          );
        }
      });

      const mainResult = successfulResults.find((r) => r.domain.toLowerCase() === originalDomain.toLowerCase());
      const alternatives = successfulResults.filter((r) => r.domain.toLowerCase() !== originalDomain.toLowerCase());

      if (!mainResult) {
        // This case should ideally not happen if the original domain was in domainsToCheck
        // and the API call didn't fail critically, but handle it just in case.
        // It might mean the specific domain failed while others succeeded, or it wasn't available.
        // We can return the first available alternative as the main result, or indicate failure.
        const firstAvailable = alternatives.find((a) => a.available);
        if (firstAvailable) {
          return {
            data: {
              ...firstAvailable,
              message: `Original domain ${originalDomain} check failed or unavailable. Showing alternative.`,
              alternatives: alternatives.filter((a) => a.domain !== firstAvailable.domain),
            },
          };
        }
        // Or return unavailable status for the original domain
        return {
          data: {
            domain: originalDomain,
            available: false,
            message: 'Could not retrieve availability status for the requested domain.',
            alternatives: alternatives,
          },
        };
      }

      return {
        data: {
          ...mainResult,
          alternatives: alternatives,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error during parallel domain availability check for ${originalDomain}: ${error.message}`,
        error.stack,
      );
      return {
        error: `Unable to check availability for ${originalDomain}`,
      };
    }
  }

  /**
   * Checks a single domain using the Go54 /api/v2/domain/whois endpoint.
   * @param domain Fully qualified domain name (e.g., "example.com")
   * @returns Simplified availability result
   */
  private async checkSingleDomainViaGo54Whois(
    domain: string,
  ): Promise<Errorable<{ domain: string; available: boolean; price?: number; currency?: string }>> {
    try {
      const token = await this.getAuthToken();
      const response = await this.axios.post<{ data: Go54WhoisData; message?: string }>(
        '/api/v2/domain/whois',
        { domain },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            Origin: 'https://app.go54.com',
            Referer: 'https://app.go54.com/',
          },
        },
      );

      const responseData = response.data?.data;
      const message = response.data?.message;

      if (!responseData) {
        throw new Error(`Invalid response structure received for domain ${domain}`);
      }

      const price = responseData.amount?.year_1;

      return {
        data: {
          domain: responseData.domain,
          available: responseData.availability,
          price: typeof price === 'number' ? price : undefined,
          currency: typeof price === 'number' ? 'NGN' : undefined, // Assuming NGN based on context
        },
      };
    } catch (error) {
      let errorMessage = `API call failed for ${domain}`;
      if (error.response?.data?.message && typeof error.response.data.message === 'string') {
        errorMessage = error.response.data.message;
      } else if (error.message && typeof error.message === 'string') {
        errorMessage = error.message;
      }
      this.logger.warn(`Failed Go54 whois check for ${domain}: ${errorMessage}`);
      // Return error structure but don't throw, allowing Promise.allSettled to handle
      return {
        error: errorMessage,
      };
    }
  }

  /**
   * Helper function to convert object to form data format for URL encoding
   * NOTE: This might be removable if not used by other Whogohost calls.
   */
  private objectToFormData(obj: any): Record<string, string> {
    const formData: Record<string, string> = {};

    Object.entries(obj).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        // Handle arrays (like tldsToInclude)
        value.forEach((item, index) => {
          formData[`${key}[${index}]`] = item;
        });
      } else if (typeof value === 'boolean') {
        // Handle boolean values
        formData[key] = value ? '1' : '0';
      } else {
        // Handle standard values
        formData[key] = String(value);
      }
    });

    return formData;
  }

  /**
   * Purchase a domain
   * @param domain Domain name to purchase
   * @param contactInfo Contact information for domain registration
   * @param nameservers Optional custom nameservers
   * @returns Result of domain registration
   */
  async purchaseDomain(
    domain: string,
    contactInfo: UserInfo,
    nameservers?: string[],
  ): Errorable<DomainRegistrationResult> {
    try {
      // Prioritize using Whogohost if configuration is available
      if (
        this.go54Config.whogohostEndpoint &&
        this.go54Config.whogohostUsername &&
        this.go54Config.whogohostSecretKey
      ) {
        return this.purchaseDomainViaWhogohost(domain, contactInfo, nameservers);
      }

      // Fall back to original Go54 API if Whogohost config is not available
      // NOTE: This uses the /domain/register endpoint, not the v2 endpoint.
      // Consider if this fallback is still desired or should also use a v2 endpoint if available.
      // We are getting the token here as well, assuming this old endpoint might also need it.
      const token = await this.getAuthToken();
      const response = await axios
        .create({
          baseURL: `${this.baseUrl}/domain`, // Specific base URL for this call
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`, // Use fetched token
            Origin: 'https://app.go54.com',
            Referer: 'https://app.go54.com/',
          },
        })
        .post('/register', {
          domain,
          contactInfo,
        });

      return { data: response.data };
    } catch (error) {
      this.logger.error(`Error purchasing domain ${domain}: ${error.message}`, error.stack);
      return {
        error: error.response?.data || {
          message: error.message,
        },
      };
    }
  }

  /**
   * Purchase a domain using Whogohost Domains Reseller API
   * @param domain Domain name to purchase
   * @param contactInfo Contact information for domain registration
   * @param nameservers Optional custom nameservers
   * @returns Result of domain registration
   */
  private async purchaseDomainViaWhogohost(
    domain: string,
    contactInfo: UserInfo,
    nameservers?: string[],
  ): Errorable<DomainRegistrationResult> {
    try {
      // Generate authentication token to match PHP implementation
      const now = new Date();
      const year = now.getUTCFullYear().toString().slice(-2);
      const month = String(now.getUTCMonth() + 1).padStart(2, '0');
      const day = String(now.getUTCDate()).padStart(2, '0');
      const hour = String(now.getUTCHours()).padStart(2, '0');
      const currentDateHour = `${year}-${month}-${day} ${hour}`;

      const message = `${this.go54Config.whogohostUsername}:${currentDateHour}`;
      const hmacDigest = crypto.createHmac('sha256', message).update(this.go54Config.whogohostSecretKey).digest('hex');
      const token = Buffer.from(hmacDigest).toString('base64');

      // Set up headers for the request
      const headers = {
        username: this.go54Config.whogohostUsername,
        token: token,
        'Content-Type': 'application/x-www-form-urlencoded',
      };

      // Format domain data
      const domainData: Record<string, string> = {
        domain: domain,
        regperiod: '1', // 1 year registration period
      };

      // Add nameservers if provided, otherwise use defaults
      const ns = nameservers && nameservers.length >= 2 ? nameservers : ['nsa1.whogohost.com', 'nsb2.whogohost.com'];
      domainData['nameservers[ns1]'] = ns[0];
      domainData['nameservers[ns2]'] = ns[1];

      // Add additional nameservers if provided
      if (ns.length > 2) {
        for (let i = 2; i < ns.length; i++) {
          domainData[`nameservers[ns${i + 1}]`] = ns[i];
        }
      }

      // Map contact information to Whogohost format for all contact types
      // Registrant contact
      domainData['contacts[registrant][firstname]'] = contactInfo.firstName;
      domainData['contacts[registrant][lastname]'] = contactInfo.lastName;
      domainData['contacts[registrant][fullname]'] = `${contactInfo.firstName} ${contactInfo.lastName}`;
      domainData['contacts[registrant][companyname]'] = catlogInfo.companyName || 'Catlog';
      domainData['contacts[registrant][email]'] = catlogInfo.email;
      domainData['contacts[registrant][address1]'] = catlogInfo.address;
      domainData['contacts[registrant][city]'] = catlogInfo.city;
      domainData['contacts[registrant][state]'] = catlogInfo.state;
      domainData['contacts[registrant][country]'] = catlogInfo.country;
      domainData['contacts[registrant][zipcode]'] = catlogInfo.postalCode;
      domainData['contacts[registrant][phonenumber]'] = catlogInfo.phone;

      // Admin contact info (same as registrant)
      domainData['contacts[admin][firstname]'] = catlogInfo.firstName;
      domainData['contacts[admin][lastname]'] = catlogInfo.lastName;
      domainData['contacts[admin][fullname]'] = `${catlogInfo.firstName} ${catlogInfo.lastName}`;
      domainData['contacts[admin][companyname]'] = catlogInfo.companyName || 'Catlog';
      domainData['contacts[admin][email]'] = catlogInfo.email;
      domainData['contacts[admin][address1]'] = catlogInfo.address;
      domainData['contacts[admin][city]'] = catlogInfo.city;
      domainData['contacts[admin][state]'] = catlogInfo.state;
      domainData['contacts[admin][country]'] = catlogInfo.country;
      domainData['contacts[admin][zipcode]'] = catlogInfo.postalCode;
      domainData['contacts[admin][phonenumber]'] = catlogInfo.phone;

      // Technical contact info (same as registrant)
      domainData['contacts[tech][firstname]'] = catlogInfo.firstName;
      domainData['contacts[tech][lastname]'] = catlogInfo.lastName;
      domainData['contacts[tech][fullname]'] = `${catlogInfo.firstName} ${catlogInfo.lastName}`;
      domainData['contacts[tech][companyname]'] = catlogInfo.companyName || 'Catlog';
      domainData['contacts[tech][email]'] = catlogInfo.email;
      domainData['contacts[tech][address1]'] = catlogInfo.address;
      domainData['contacts[tech][city]'] = catlogInfo.city;
      domainData['contacts[tech][state]'] = catlogInfo.state;
      domainData['contacts[tech][country]'] = catlogInfo.country;
      domainData['contacts[tech][zipcode]'] = catlogInfo.postalCode;
      domainData['contacts[tech][phonenumber]'] = catlogInfo.phone;

      // Billing contact info (same as registrant)
      domainData['contacts[billing][firstname]'] = catlogInfo.firstName;
      domainData['contacts[billing][lastname]'] = catlogInfo.lastName;
      domainData['contacts[billing][fullname]'] = `${catlogInfo.firstName} ${catlogInfo.lastName}`;
      domainData['contacts[billing][companyname]'] = catlogInfo.companyName || 'Catlog';
      domainData['contacts[billing][email]'] = catlogInfo.email;
      domainData['contacts[billing][address1]'] = catlogInfo.address;
      domainData['contacts[billing][city]'] = catlogInfo.city;
      domainData['contacts[billing][state]'] = catlogInfo.state;
      domainData['contacts[billing][country]'] = catlogInfo.country;
      domainData['contacts[billing][zipcode]'] = catlogInfo.postalCode;
      domainData['contacts[billing][phonenumber]'] = catlogInfo.phone;

      // Add empty domainfields parameter
      domainData['domainfields'] = '';

      // Make the request to Whogohost API to register the domain
      const response = await axios.post(
        `${this.go54Config.whogohostEndpoint}/order/domains/register`,
        new URLSearchParams(domainData).toString(),
        { headers },
      );

      const responseData = response.data;
      this.logger.debug(`Whogohost domain registration response for ${domain}: ${JSON.stringify(responseData)}`);

      // Handle the response
      if (responseData.success) {
        return {
          data: {
            success: true,
            domain: domain,
            registrationId: responseData.orderNumber || responseData.id || String(Date.now()),
            expiresAt: this.calculateExpiryDate(1), // 1 year from now
            message: responseData.message || 'Domain registered successfully',
          },
        };
      } else {
        return {
          error: responseData.message || 'Domain registration failed',
        };
      }
    } catch (error) {
      this.logger.error(`Error purchasing domain via Whogohost for ${domain}: ${error.message}`, error.stack);
      return {
        error: error.response?.data || {
          message: error.message,
        },
      };
    }
  }

  /**
   * Calculate domain expiry date based on registration period in years
   */
  private calculateExpiryDate(years: number): string {
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + years);
    return expiryDate.toISOString();
  }

  /**
   * Get information about a domain
   * @param domain Domain name or registration ID
   * @returns Domain information
   */
  async getDomainInfo(domain: string): Errorable<DomainInfo> {
    try {
      const token = await this.getAuthToken();
      // This likely needs to use the v2 endpoint as well, or a specific Go54/Whogohost info endpoint
      const response = await this.axios.get(`/api/v2/domain/info`, {
        // Adjusted path tentatively
        params: { domain },
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          Origin: 'https://app.go54.com',
          Referer: 'https://app.go54.com/',
        },
      });

      return { data: response.data };
    } catch (error) {
      this.logger.error(`Error getting domain info for ${domain}: ${error.message}`, error.stack);
      return {
        error: error.response?.data || {
          message: error.message,
        },
      };
    }
  }

  /**
   * Update nameservers for a domain
   * @param domain Domain name
   * @param nameservers Array of nameserver addresses
   * @returns Result of nameserver update operation
   */
  async updateNameservers(domain: string, nameservers: string[]): Errorable<{ success: boolean; message: string }> {
    try {
      const token = await this.getAuthToken();
      // This likely needs to use a v2 endpoint or a specific Go54/Whogohost nameserver update endpoint
      const response = await this.axios.post(
        `/api/v2/domain/nameservers`,
        {
          // Adjusted path tentatively
          domain,
          nameservers,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            Origin: 'https://app.go54.com',
            Referer: 'https://app.go54.com/',
          },
        },
      );

      return { data: response.data };
    } catch (error) {
      this.logger.error(`Error updating nameservers for ${domain}: ${error.message}`, error.stack);
      return {
        error: error.response?.data || {
          message: error.message,
        },
      };
    }
  }

  /**
   * Retrieves the auth token from cache or logs in to get a new one.
   */
  private async getAuthToken(): Promise<string> {
    // Check cache first
    const cachedToken = await this.cacheService.get<string>(this.tokenCacheKey);
    if (cachedToken) {
      this.logger.debug('Using cached Go54 token.');
      return cachedToken;
    }

    // If not cached, login to get a new token
    this.logger.log('No cached Go54 token found, attempting login...');
    const loginResult = await this.login(); // Call the login method

    if (loginResult.error || !loginResult.data?.data?.token) {
      this.logger.error('Failed to obtain Go54 token via login.', loginResult.error);
      throw new Error('Go54 authentication failed.');
    }

    const token = loginResult.data.data.token;
    const expiresInStr = loginResult.data.data.token_expires;
    let ttl: number | undefined = 60 * 5; // Default TTL: 5 minutes in seconds

    this.logger.log(`Obtained new Go54 token, caching with TTL: ${ttl} seconds.`);
    await this.cacheService.setWithLock(this.tokenCacheKey, token, ttl);

    return token;
  }

  /**
   * Login to Go54 API - This method should generally be called by getAuthToken, not directly.
   * @returns Authentication result with token details.
   */
  async login(): Promise<Errorable<Go54LoginResponse>> {
    try {
      // Ensure email and password are configured
      if (!this.go54Config.email || !this.go54Config.password) {
        return { error: 'Go54 email or password is not configured' };
      }

      const loginUrl = '/api/v1/auth/login';

      console.log({
        email: this.go54Config.email,
        password: this.go54Config.password,
      });

      // Make the login request
      const response = await this.axios.post<Go54LoginResponse>(
        loginUrl,
        {
          email: this.go54Config.email,
          password: this.go54Config.password,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Origin: 'https://app.go54.com',
            Referer: 'https://app.go54.com/',
            'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
            'User-Agent':
              'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
          },
        },
      );

      // IMPORTANT: Do NOT re-initialize the main axios instance here.
      // The token will be fetched by getAuthToken and applied per-request.
      if (response.data?.status && response.data?.data?.token) {
        this.logger.debug(`Go54 login successful for ${this.go54Config.email}.`);
      } else {
        this.logger.warn(
          `Go54 login call succeeded but response lacked token or status. Response: ${JSON.stringify(response.data)}`,
        );
        return { error: response.data?.message || 'Login response missing token or status.' };
      }

      return { data: response.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Unknown Go54 login error';
      this.logger.error(`Error logging in to Go54: ${errorMessage}`, error.stack);
      return {
        error: typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage),
      };
    }
  }

  /**
   * Updates or adds a DNS record for a domain via Go54 API.
   * @param domain The domain name (e.g., "example.com")
   * @param type The DNS record type (e.g., "A", "CNAME")
   * @param name The record name (e.g., "@", "www")
   * @param ttl Time To Live for the record (defaults to 14400)
   * @returns Result of the DNS update operation
   */
  async updateDnsRecord(
    domain: string,
    type: UpdateDnsRecordRequest['type'],
    name: string,
    ttl: number = 14400,
  ): Promise<Errorable<UpdateDnsRecordResponse>> {
    try {
      const token = await this.getAuthToken();
      const serverIp = process.env.SERVER_IP; // Get SERVER_IP from env

      if (!serverIp && type === 'A') {
        throw new Error('SERVER_IP environment variable is not set, but required for A record.');
      }

      // Construct the payload based on the type
      const payload: Partial<UpdateDnsRecordRequest> = {
        domain,
        ttl,
        type,
        name,
      };

      if (type === 'A') {
        payload.ipv4 = serverIp;
      }
      // TODO: Add logic for other record types if needed (e.g., CNAME, TXT)
      // else if (type === 'CNAME') { payload.hostname = 'target.example.com'; }
      // else if (type === 'TXT') { payload.txtdata = 'verification-string' }

      const apiUrl = '/api/v1/user/domain/records/zonerecord'; // Specific v1 path

      const response = await this.axios.post<UpdateDnsRecordResponse>(apiUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          Origin: 'https://app.go54.com',
          Referer: 'https://app.go54.com/',
        },
      });

      if (response.data?.status) {
        return { data: response.data };
      } else {
        return { error: response.data?.message || 'Failed to update DNS record, unknown error.' };
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || error.message || `Unknown error updating DNS record for ${domain}`;
      this.logger.error(`Error updating DNS record for ${domain}: ${errorMessage}`, error.stack);
      return {
        error: typeof errorMessage === 'string' ? errorMessage : JSON.stringify(errorMessage),
      };
    }
  }

  /**
   * Checks a single domain using the Go54 /api/v2/domain/whois endpoint without checking alternatives.
   * @param domain Fully qualified domain name (e.g., "example.com")
   * @returns Simplified availability result
   */
  async checkSingleDomain(
    domain: string,
  ): Promise<Errorable<{ domain: string; available: boolean; price?: number; currency?: string }>> {
    return this.checkSingleDomainViaGo54Whois(domain);
  }

  async getTLDs(country: COUNTRY_CODE): Promise<string[]> {
    return [...genericTLDs, ...(countryTLDs[country] || []), ...businessTLDs];
  }
}

const getDomainParts = async (domain: string) => {
  const domainParts = domain.split('.');
  const baseName = domainParts.splice(0, 1);
  const tld = domainParts.join('.');

  return {
    tld,
    baseName,
  };
};

export const catlogInfo: Go54ContactInfo = {
  firstName: 'Catlog',
  lastName: 'Shop',
  email: '<EMAIL>',
  phone: '+2349042550548',
  address: 'Admiralty Way, Lekki Phase 1',
  city: 'Lekki',
  state: 'Lagos',
  country: 'Nigeria',
  postalCode: '105102',
  companyName: 'Catlog',
};

const genericTLDs = ['.com', '.online', '.org', '.net', '.co', '.me'];

const countryTLDs = {
  NG: ['.ng', '.com.ng'],
  KE: ['.co.ke'],
  ZA: ['.co.za'],
  GH: [],
};

export const businessTLDs = ['.shop', '.store', '.biz', '.restaurant', '.ventures'];
