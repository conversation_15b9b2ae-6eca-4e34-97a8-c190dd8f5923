import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { registerErrors } from '../utils/errors.util';
import { Errorable } from '../utils/types';
import { extractErrorDetails } from '../utils/axios-errors';
import { FezDeliveryConfig } from '../config/types/fez-delivery.config';
import { CacheService } from '../modules/shared/cache/cache.service';
import { ONE_HOUR } from '../utils/constants';

// Define interfaces for request payloads
export interface FezCreateOrder {
  recipientAddress: string;
  recipientState: string;
  recipientName: string;
  recipientPhone: string;
  uniqueID: string;
  BatchID: string;
  CustToken?: string;
  itemDescription?: string;
  additionalDetails?: string;
  valueOfItem: string;
  weight: number;
  pickUpState?: string;
  pickupAddress?: string;
}

export interface FezUpdateOrder {
  recipientAddress?: string;
  recipientState?: string;
  recipientName?: string;
  recipientPhone?: string;
  orderNo: string;
}

export interface FezDeleteOrderPayload {
  orderNo: string;
}

export interface FezResponse {
  status: string;
  description: string;
}

export interface FezOrderResponse extends FezResponse {
  orderNos: Record<string, string>; // A mapping of order identifiers to their corresponding order numbers
}

export interface CostResponse extends FezResponse {
  Cost: CostItem[];
}

interface CostItem {
  state: string;
  cost: string;
}

@Injectable()
export class FezDeliveryRepository {
  private config: FezDeliveryConfig;
  private readonly axios: AxiosInstance;
  private readonly tokenCacheKey = 'fez_delivery_auth_token'; // Key to store the token in Redis

  constructor(
    configService: ConfigService,
    private readonly logger: Logger,
    private readonly cacheService: CacheService, // Inject the cache service
  ) {
    this.config = configService.get<FezDeliveryConfig>('fezDeliveryConfiguration');
    this.axios = Axios.create({
      baseURL: this.config.api_url,
      headers: {
        'Content-Type': 'application/json',
        'secret-key': this.config.secret_key,
      },
    });

    // Axios request interceptor to attach the auth token to each request
    this.axios.interceptors.request.use(
      async (config) => {
        const authToken = await this.getCachedOrNewToken();
        config.headers['Authorization'] = `Bearer ${authToken}`;

        console.log('<=======FEZ HEADERS=======>');
        console.log({ authToken });
        return config;
      },
      (error) => {
        return Promise.reject(error);
      },
    );
  }

  // Method to authenticate and get a new auth token
  async getAuthToken(): Promise<any> {
    const payload = {
      user_id: this.config.user_id,
      password: this.config.password,
    };

    try {
      const res = await Axios.post(`${this.config.api_url}/user/authenticate`, payload, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      return res.data;
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.log(error);
      registerErrors(error);
      throw new Error(error?.message);
    }
  }

  // Method to get token from cache or fetch a new one
  async getCachedOrNewToken(): Promise<string> {
    // Check if the token exists in the cache
    const cachedToken = await this.cacheService.get<string>(this.tokenCacheKey);

    if (cachedToken) {
      return cachedToken;
    }

    // If no cached token, fetch a new one
    const authResponse = await this.getAuthToken();
    const authToken = authResponse?.authDetails?.authToken;

    // Set a fixed TTL of 1 hours (in seconds)
    const ttl = ONE_HOUR / 1000; // 1 hours in seconds

    // Set the token in Redis with TTL
    await this.cacheService.setWithLock(this.tokenCacheKey, authToken, ttl);

    return authToken;
  }

  // Method to create orders
  async createOrder(order: FezCreateOrder): Promise<Errorable<FezOrderResponse>> {
    try {
      console.log('<=======FEZ PAYLOAD=======>');
      console.log({ payload: [order] });
      const res = await this.axios.post('/order', [order]);
      return { error: null, data: res.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error(error);
      registerErrors(error);
      return {
        error:
          error?.details?.data?.description ?? error?.details?.message ?? error?.message ?? 'Something went wrong!',
      };
    }
  }

  // Method to get order cost
  async getOrderCost(payload: {
    state: string;
    pickUpState?: string;
    weight?: number;
  }): Promise<Errorable<CostResponse>> {
    try {
      const res = await this.axios.post('/order/cost', payload);
      return { error: null, data: res.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error(error);
      registerErrors(error);
      return {
        error:
          error?.details?.data?.description ?? error?.details?.message ?? error?.message ?? 'Something went wrong!',
      };
    }
  }

  // Method to update orders
  async updateOrder(order: FezUpdateOrder): Promise<Errorable<any>> {
    try {
      const res = await this.axios.put('/order', [order]);
      return { error: null, data: res.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error(error);
      registerErrors(error);
      return {
        error:
          error?.details?.data?.description ?? error?.details?.message ?? error?.message ?? 'Something went wrong!',
      };
    }
  }

  // Method to cancel orders
  async cancelOrder(orderId: string): Promise<Errorable<FezResponse>> {
    try {
      const res = await this.axios.delete('/order', { data: { orderNo: orderId } });
      return { error: null, data: res.data };
    } catch (err) {
      const error = extractErrorDetails(err);
      this.logger.error(error);
      registerErrors(error);
      return {
        error:
          error?.details?.data?.description ?? error?.details?.message ?? error?.message ?? 'Something went wrong!',
      };
    }
  }
}
