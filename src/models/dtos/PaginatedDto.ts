import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class PaginatedQueryDto {
  @ApiProperty({ required: false })
  @Type(() => Number)
  page: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  per_page: number;

  @ApiProperty({ required: false, type: 'string', enum: ['ASC', 'DESC'] })
  sort?: any;

  filter?: any;
}

export class PaginatedStoreQueryDto {
  @ApiProperty({ required: false })
  @Type(() => Number)
  page: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  per_page: number;

  @ApiProperty({
    required: false,
    type: 'string',
    enum: ['PRODUCTS', 'VISITS', 'DATE_CREATED'],
  })
  sort?: any;
}

export class PaginatedStoreAnalysisQueryDto {
  @ApiProperty({ required: false })
  @Type(() => Number)
  page: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  per_page: number;

  @ApiProperty({ required: false, type: 'string', enum: ['ASC', 'DESC'] })
  sort?: any;
}

export class PaginatedQueryWithDataDto<TData> extends PaginatedQueryDto {
  @ApiProperty()
  data: TData[];

  @ApiProperty()
  next_page?: number;

  @ApiProperty()
  prev_page: number;

  @ApiProperty()
  total_pages: number;

  @ApiProperty()
  total: number;
}

export interface PaginatedStoreItemsBrokerDto {
  storeId: string;
  query: PaginatedQueryDto;
}
