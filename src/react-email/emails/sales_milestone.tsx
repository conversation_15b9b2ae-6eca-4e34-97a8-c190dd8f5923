import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import config from "../tailwind.config";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";
import EmailButton from "./components/button";

interface Props {
  name: string;
  preview_text: string;
  value: string;
  currency: string;
}

const EmailTemplate = (props: Props) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}
        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="sales_milestone">
                You’ve crossed <br />
                {props.value} in sales
              </EmailHeader>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name}
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    You just crossed {props.currency} {props.value} in sales. Looks like you’re making some big money
                    moves, where should we send our account?
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                    Seriously we’re very impressed, and this is us giving you a virtual hi-five, we can’t wait to see
                    your business do more!
                  </Text>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                  We're here to support your journey, so if you ever need a hand or have any questions, we’re just a message away.
                  </Text>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base pt-2.5">
                  Here’s to more milestones 🥂
                  </Text>
                  <EmailButton className="my-7.5" href="https://catlog.shop/app/dashboard">
                    Go to dashboard
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                  Did you know that when you cross 1M, 5M, 10M & 15M in payments processed, we give you some money back?
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  name: "John Doe",
  value: "100000",
  currency: "NGN",
} as Props;

export default EmailTemplate;
