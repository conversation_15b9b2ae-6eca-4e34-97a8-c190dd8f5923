import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PlanOption, PlanOptionDocument } from './plan-options.schema';
import { CreatePlanOptionDto } from '../../../models/dtos/PlanDtos';

@Injectable()
export class PlanOptionService {
  constructor(@InjectModel(PlanOption.name) private planOptionModel: Model<PlanOptionDocument>) {}

  async create(createPlanOptionDto: CreatePlanOptionDto): Promise<PlanOption> {
    const createdPlanOption = new this.planOptionModel(createPlanOptionDto);
    return await createdPlanOption.save();
  }

  async update(id: string, updatePlanOptionDto: CreatePlanOptionDto): Promise<PlanOption> {
    return await this.planOptionModel.findByIdAndUpdate(id, updatePlanOptionDto, { new: true });
  }

  async findAll(): Promise<PlanOption[]> {
    return await this.planOptionModel.find().exec();
  }

  async findOne(id: string): Promise<PlanOption> {
    return await this.planOptionModel.findById(id).exec();
  }

  async remove(id: string): Promise<PlanOption> {
    return await this.planOptionModel.findByIdAndRemove(id).exec();
  }
}
