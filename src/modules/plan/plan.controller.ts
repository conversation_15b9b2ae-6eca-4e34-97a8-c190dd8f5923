import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiExtraModels,
  ApiHeader,
  ApiOkResponse,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { CreatePlanDto, CreatePlanOptionDto, PlanDataDto, UpdatePlanDto } from '../../models/dtos/PlanDtos';
import { PlanService } from './plan.service';
import { Plan, PlanDocument } from './plan.schema';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { PaymentMethod } from '../paymentmethod/paymentmethod.schema';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { COUNTRY_CODE } from '../country/country.schema';

@Controller('plans')
@ApiTags('Plan')
@ApiExtraModels(Plan)
export class PlanController {
  constructor(private readonly planService: PlanService) {}

  @Post('')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiCreatedResponse({
    status: HttpStatus.CREATED,
    type: CreatePlanDto,
  })
  @ApiHeader({ name: 'x-internal-api-key' })
  async create(@Body() planReq: CreatePlanDto) {
    return this.planService.create(planReq);
  }

  @Put(':id')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  @ApiOkResponse({
    status: HttpStatus.OK,
    type: UpdatePlanDto,
  })
  async update(@Param('id') id: string, @Body() planReq: UpdatePlanDto) {
    return this.planService.update(id, planReq);
  }

  @Post('/:id/options')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async createOption(@Param('id') id: string, @Body() planOptionReq: CreatePlanOptionDto) {
    return this.planService.createOption(id, planOptionReq);
  }

  @Put('/:id/options/:optionId')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async updateOption(
    @Param('id') id: string,
    @Param('optionId') optionId: string,
    @Body() planOptionReq: CreatePlanOptionDto,
  ) {
    return this.planService.updateOption(id, optionId, planOptionReq);
  }

  /* @Post('change-plan')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SUBSCRIPTIONS.MANAGE_SUBSCRIPTIONS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOkResponse()
  async changePlan(@Req() req: Request, @Body() planReq: ChangePlanDto) {
    planReq.userId = req.user.id;
    const data = await this.planService.changePlan(planReq);

    return {
      message: 'Plan changed successfully',
      data,
    };
  } */

  @Get('/admin')
  @ApiExcludeEndpoint()
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiOkResponse({
    status: HttpStatus.OK,
    isArray: true,
    type: PlanDataDto,
  })
  async getPlansForAdmin() {
    return {
      message: 'Plans fetched successfully',
      data: await this.planService.getAllForAdmin(),
    };
  }

  @Get('/admin/:id')
  @ApiExcludeEndpoint()
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async getPlanForAdmin(@Param('id') id: string) {
    return {
      message: 'Plan fetched successfully',
      data: await this.planService.getPlanForAdmin(id),
    };
  }

  @Get(':id')
  @ApiSecurity('bearer')
  @ApiOkResponse({
    type: Plan,
  })
  async getPlan(@Param('id') id: string) {
    const plan = await this.planService.getPlanById(id);
    if (!plan) {
      throw new BadRequestException('Plan with id does not exist');
    }
    console.log('plan', plan);

    return {
      message: 'Plan fetched successfully',
      data: plan,
    };
  }

  @Get('')
  @ApiSecurity('bearer')
  @ApiOkResponse({
    status: HttpStatus.OK,
    isArray: true,
    type: PlanDataDto,
  })
  async getPlans(@Query('country') country?: COUNTRY_CODE) {
    let data: {
      plans: PlanDocument[];
      paymentMethods: PaymentMethod[];
    };

    if (country) {
      data = await this.planService.getPlansByCountry(country);
    } else {
      data = await this.planService.getAll();
    }
    return { data };
  }

  @Post('/update-countries')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async updateCountries() {
    await this.planService.updateCountries();

    return {
      message: 'Plans updated successfully',
      data: {},
    };
  }

  @Post('/update-model')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async restructureData() {
    await this.planService.restructurePlans();

    return {
      message: 'Plans updated successfully',
      data: {},
    };
  }

  @Post('/migrate-plans')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migratePlans() {
    const data = await this.planService.migratePlans();
    return {
      message: 'Migration completed successfully',
      data,
    };
  }

  @Post('/create-sa-and-ke-plans')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async createSAandKEPlans() {
    const data = await this.planService.addSAandKEPlans();
    return {
      message: 'Migration completed successfully',
      data,
    };
  }

  @Post('/create-kitchen-plans')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async createKitchenPlans() {
    const data = await this.planService.createKitchenPlans();
    return {
      message: 'Migration completed successfully',
      data,
    };
  }
}
