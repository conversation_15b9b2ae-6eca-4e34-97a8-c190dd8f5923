import { Controller } from '@nestjs/common';
import { PlanService } from './plan.service';
import { MessagePattern } from '@nestjs/microservices';
import mongoose, { Model } from 'mongoose';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { PlanDocument } from './plan.schema';
import { PlanOptionService } from './plan-options/plan-options.service';
import { PlanOption, PlanOptionDocument } from './plan-options/plan-options.schema';
import { InjectModel } from '@nestjs/mongoose';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class PlanBroker {
  constructor(
    private readonly planService: PlanService,
    @InjectModel(PlanOption.name) private planOptionModel: Model<PlanOptionDocument>,
  ) {}

  async getPlanById({ id }) {
    return this.planService.getPlanById(id);
  }

  @MessagePattern(BROKER_PATTERNS.PLAN.GET_PLAN)
  async getPlan(data: mongoose.FilterQuery<PlanDocument>) {
    return this.planService.getPlan(data);
  }

  @MessagePattern(BROKER_PATTERNS.PLAN.GET_PLANS)
  async getPlans(data: mongoose.FilterQuery<PlanDocument>) {
    return this.planService.getPlans(data);
  }

  @MessagePattern(BROKER_PATTERNS.PLAN.GET_PLANS_LEAN)
  async getPlansLean(data: mongoose.FilterQuery<PlanDocument>) {
    return this.planService.getPlansLean(data);
  }

  @MessagePattern(BROKER_PATTERNS.PLAN.GET_PLAN_OPTION)
  async getPlanOption(data: { id: string }) {
    return this.planOptionModel.findById(data.id);
  }
}
