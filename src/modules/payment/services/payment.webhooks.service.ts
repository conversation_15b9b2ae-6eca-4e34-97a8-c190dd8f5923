import { BadRequestException } from '@nestjs/common';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import { StripeSuccessfulChargeData } from '../../../repositories/stripe.repository';
import { ZeepayWebhookPayload } from '../../../repositories/zeepay.repository';
import { zeepayActualCharge } from '../../../utils/fees';
import { getDocId, to, toKobo } from '../../../utils/functions';
import { LeatherbackWebhookData, PaystackDDAuthorization } from '../payment.broker';
import { PaymentService } from './index.service';
import { User } from '../../user/user.schema';

export class PaymentWebhooksService extends PaymentService {
  async resolvePaystackPayments(body: any) {
    const { data } = body;
    const reference = data.reference;

    const payment = await this.paymentModel.findOne({
      reference: reference,
    });

    if (!payment) {
      this.logger.log(`payment with reference ${data.reference}, does not exist`);
      return;
    }

    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      return;
    }

    switch (payment.type) {
      case PAYMENT_TYPES.SUBSCRIPTION:
        await this.paymentSubscriptionService.paystackWebhook(data, payment);
        break;
      case PAYMENT_TYPES.DELIVERY:
        await this.paymentDeliveriesService.paystackWebhook(data, payment);
        break;
      case PAYMENT_TYPES.TOKEN_PURCHASE:
        await this.paymentTokenService.paystackWebhook(data, payment);
        break;
      case PAYMENT_TYPES.DOMAIN_PURCHASE:
        await this.paymentDomainPurchaseService.paystackWebhook(data, payment);
        break;
      default:
        await this.processPublicPayment(
          data.reference,
          { amount_settled: Number(data.amount) - Number(data.fees), fee: Number(data.fees) },
          data,
        );
        break;
    }
  }

  async savePaystackDDAuth(body: PaystackDDAuthorization) {
    let ddAuth = await this.ddTokenModel.findOne({ authorization: body.authorization_code });

    if (ddAuth) {
      return;
    }

    if (!body.customer.email) {
      throw new BadRequestException('No Email in Data provided');
    }

    const payment = await this.paymentModel.findOne({
      reference: body?.reference,
    });

    if (!payment) {
      throw new BadRequestException('Payment for this auth not found');
    }

    ddAuth = await this.ddTokenModel.create({
      authorization: body.authorization_code,
      user: payment.owner,
      last4Digits: body.last4,
      bank: body.bank,
      account_name: body.account_name,
      signature: body.signature,
      processor: PAYMENT_METHODS.PAYSTACK,
    });

    return ddAuth;
  }

  async resolveZeepayPayments(data: ZeepayWebhookPayload) {
    const reference = data.reference;

    const payment = await this.paymentModel.findOne({
      reference,
    });

    if (!payment) {
      this.logger.log(`payment with reference ${data.reference}, does not exist`);
      return;
    }

    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      return;
    }

    switch (payment.type) {
      case PAYMENT_TYPES.SUBSCRIPTION:
        await this.paymentSubscriptionService.zeepayWebhook(data, payment);
        break;
      default:
        await this.processPublicPayment(
          data.reference,
          {
            amount_settled: Number(payment.amount_with_charge) - zeepayActualCharge(payment.amount),
            fee: zeepayActualCharge(payment.amount),
          },
          data,
          null,
          false,
          { zeepay: { zeepay_id: data?.zeepay_id, gateway_id: data?.gateway_id } },
        );
        break;
    }
  }

  async publicMonnifyBankTransfer(body: any) {
    await this.processPublicPayment(
      body.paymentReference,
      {
        amount_settled: Number(body.settlementAmount) * 100,
        fee: body.amountPaid * 100 - body.settlementAmount * 100,
      },
      body,
    );
  }

  async publicZillaPayment(body: any) {
    await this.processPublicPayment(
      body.clientOrderReference,
      {
        amount_settled: Number(body.amountSettled) * 100,
        fee: Number(body.fee) * 100,
      },
      body,
    );
  }

  async publicMonoPayment(body: any) {
    await this.processPublicPayment(
      body.reference,
      {
        amount_settled: body.amount - body.fee,
        fee: body.fee,
      },
      body,
    );
  }

  async publicThepeerPayment(body: any) {
    await this.processPublicPayment(
      body.transaction.meta.reference,
      {
        amount_settled: body.transaction.amount - body.transaction.charge,
        fee: body.transaction.charge,
      },
      body,
    );
  }

  async publicBlocPayment(body: any) {
    await this.processPublicPayment(
      body.account_id,
      {
        amount_settled: body.amount,
        fee: body.fee,
      },
      body,
      undefined,
      true,
    );
  }

  async publicFlwPayment(body: any) {
    const actualRef = (body?.tx_ref ?? body?.txRef).replace(/_PMCKD.*/, '');
    await this.processPublicPayment(
      actualRef,
      {
        amount_settled: (Number(body.charged_amount) - Number(body?.app_fee ?? body?.appfee ?? 0)) * 100,
        fee: Number(body?.app_fee ?? body?.appfee ?? 0) * 100,
      },
      body,
    );
  }

  async publicKorapayPayment(body: any) {
    await this.processPublicPayment(
      body.reference,
      {
        amount_settled: toKobo(body.amount),
        fee: toKobo(body.fee),
      },
      body,
    );
  }

  async publicLeatherbackPayment(body: LeatherbackWebhookData) {
    await this.processPublicPayment(
      body.Reference,
      {
        amount_settled: toKobo(Number(body.Amount)),
        fee: toKobo(Number(body.AppFee)),
      },
      body,
    );
  }

  async publicStripePayment(body: StripeSuccessfulChargeData) {
    await this.processPublicPayment(
      body.reference,
      {
        amount_settled: toKobo(body.amount),
        fee: toKobo(body.fee),
      },
      body,
      null,
      false,
      { stripe_charge_id: body.id },
    );
  }

  async resolvePayazaPayments(body: any) {
    const reference = body.transaction_reference;

    const payment = await this.paymentModel.findOne({
      reference,
    });

    if (!payment) {
      this.brokerTransport.emit(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYAZA_TRANSFER_RECEIVED, body).toPromise();
      return;
    }

    await this.processPublicPayment(
      body.transaction_reference,
      {
        amount_settled: toKobo(Number(body.amount_received) - Number(body.transaction_fee)),
        fee: toKobo(Number(body.transaction_fee)),
      },
      body,
    );
  }

  async startbuttonPaymentReceived(body: any) {
    const data = body?.data?.transaction;
    const reference = data?.userTransactionReference;

    const payment = await this.paymentModel.findOne({
      reference: reference,
    });

    if (!payment) {
      this.logger.log(`payment with reference ${data.reference}, does not exist`);
      return;
    }

    if (payment.status !== PAYMENT_STATUS.PENDING) {
      return;
    }

    switch (payment.type) {
      case PAYMENT_TYPES.SUBSCRIPTION:
        await this.paymentSubscriptionService.startbuttonWebhook(data, payment);
        break;
      case PAYMENT_TYPES.DELIVERY:
        // await this.paymentDeliveriesService.paystackWebhook(data, payment);
        break;
      case PAYMENT_TYPES.TOKEN_PURCHASE:
        // await this.paymentTokenService.paystackWebhook(data, payment);
        break;
      default:
        await this.processPublicPayment(
          reference,
          { amount_settled: Number(data.amount) - Number(data.feeAmount), fee: Number(data.feeAmount) },
          data,
        );
        break;
    }
  }
}
