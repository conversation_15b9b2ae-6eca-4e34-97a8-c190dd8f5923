import { ClientSession, Model } from 'mongoose';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES, SUBSCRIPTION_STATUS } from '../../../enums/payment.enum';
import { PLAN_TYPE } from '../../../enums/plan.enum';
import { CREDITS, MAX_REFERRALS_EARNING_MONTHS } from '../../../utils/constants';
import { formatDate, getDateAt } from '../../../utils/time';
import {
  COUNTRY_CODE,
  COUNTRY_CURRENCY_MAP,
  COUNTRY_FLAG_EMOJIS,
  CURRENCIES,
  CURRENCY_COUNTRY_MAP,
} from '../../country/country.schema';
import { Plan } from '../../plan/plan.schema';
import { Subscription } from '../../subscription/subscription.schema';
import { CatlogCredits, CatlogCreditsTransactions } from '../../user/credits/credits.schema';
import { User } from '../../user/user.schema';
import { Card, CardDocument } from '../card.schema';
import { Payment, PaymentDocument } from '../payment.schema';
import { PaymentService } from './index.service';
import { InjectModel, InjectConnection } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { Logger } from '@nestjs/common';
import { deepMerge, getDocId, toKobo, toNaira } from '../../../utils/functions';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';
import { formatPhoneNumber, getAppEnv, toCurrency } from '../../../utils';
import { SlackRepository } from '../../../repositories/slack.respository';
import dayjs from 'dayjs';
import { ZeepayWebhookPayload } from '../../../repositories/zeepay.repository';
import { zeepayActualCharge } from '../../../utils/fees';
import { Referrals } from '../../user/referrals/referrals.schema';
import { MobileMoneyMethod, MobileMoneyMethodDocument } from '../mobile-money-method.schema';
import { ResendRepository } from '../../../repositories/resend.repository';
import { PaymentsResolverService } from './payments.resolver.service';
import { CustomerIoRepository } from '../../../repositories/customer-io.repository';
const planPriority = {
  [PLAN_TYPE.STARTER]: 0,
  [PLAN_TYPE.BASIC]: 1,
  [PLAN_TYPE.BUSINESS_PLUS]: 2,
  [PLAN_TYPE.KITCHEN]: 3,
};

interface SubscriptionWithPlan extends Subscription {
  plan: Plan;
}

export class PaymentSubscriptionService extends PaymentsResolverService {
  constructor(
    protected readonly paymentService: PaymentService,
    @InjectModel(Payment.name)
    public readonly paymentModel: Model<PaymentDocument>,
    @InjectModel(Card.name)
    protected readonly cardModel: Model<CardDocument>,
    @InjectModel(MobileMoneyMethod.name)
    protected readonly mobileMoneyMethodModel: Model<MobileMoneyMethodDocument>,
    public readonly brokerTransport: BrokerTransportService,
    public readonly logger: Logger,
    @InjectConnection()
    protected readonly connection: mongoose.Connection,
    private readonly slack: SlackRepository,
    private readonly resend: ResendRepository, // public readonly sseService: SSEService,
    public readonly customerIo: CustomerIoRepository,
  ) {
    super(paymentModel, cardModel, brokerTransport, logger, customerIo);
  }

  async paystackWebhook(data: any, payment: PaymentDocument) {
    const { authorization } = data;

    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      this.logger.log(`this payment is already successful, payment: ${payment}, data: ${data}`);
      return;
    }

    let card: Card;

    if (authorization.channel === 'card') {
      card = await this.cardModel
        .findOneAndUpdate(
          {
            user: payment.owner,
            processor: PAYMENT_METHODS.PAYSTACK,
          },
          {
            authorization: authorization.authorization_code,
            first: authorization.bin,
            last: authorization.last4,
            type: authorization.brand,
          },
        )
        .exec();

      if (card) {
        await this.cardModel.deleteOne({
          user: payment.owner,
          processor: PAYMENT_METHODS.PAYSTACK,
        });
      }

      card = await this.cardModel.create({
        first: authorization.bin,
        last: authorization.last4,
        user: payment.owner,
        authorization: authorization.authorization_code,
        type: authorization.brand,
        processor: PAYMENT_METHODS.PAYSTACK,
      });
    }

    this.handleSuccessfulSubscriptionPayments(payment, { amount: data.amount, fees: data.fees }, card);
  }

  async startbuttonWebhook(data: any, payment: PaymentDocument) {
    const { authorization } = data;

    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      this.logger.log(`this payment is already successful, payment: ${payment}, data: ${data}`);
      return;
    }

    // let card: Card;

    // if (authorization.channel === 'card') {
    //   card = await this.cardModel
    //     .findOneAndUpdate(
    //       {
    //         user: payment.owner,
    //         processor: PAYMENT_METHODS.PAYSTACK,
    //       },
    //       {
    //         authorization: authorization.authorization_code,
    //         first: authorization.bin,
    //         last: authorization.last4,
    //         type: authorization.brand,
    //       },
    //     )
    //     .exec();

    //   if (card) {
    //     await this.cardModel.deleteOne({
    //       user: payment.owner,
    //       processor: PAYMENT_METHODS.PAYSTACK,
    //     });
    //   }

    //   card = await this.cardModel.create({
    //     first: authorization.bin,
    //     last: authorization.last4,
    //     user: payment.owner,
    //     authorization: authorization.authorization_code,
    //     type: authorization.brand,
    //     processor: PAYMENT_METHODS.PAYSTACK,
    //   });
    // }

    this.handleSuccessfulSubscriptionPayments(
      payment,
      { amount: Number(data.amount), fees: Number(data.feeAmount) },
      null,
    );
  }

  async zeepayWebhook(data: ZeepayWebhookPayload, payment: PaymentDocument) {
    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      this.logger.log(`this payment is already successful, payment: ${payment}, data: ${data}`);
      return;
    }

    const zeepayFees = zeepayActualCharge(payment.amount);

    const mno = payment?.meta?.zeepay?.mno;
    const msisdn = payment?.meta?.zeepay?.msisdn;

    if (mno && msisdn) {
      const mobileMoneyMethod = await this.mobileMoneyMethodModel.findOne({ mno, msisdn });

      if (!mobileMoneyMethod) {
        await this.mobileMoneyMethodModel.create({ mno, msisdn, owner: getDocId(payment.owner) });
      }
    }

    this.handleSuccessfulSubscriptionPayments(
      payment,
      {
        amount: payment.amount_with_charge - zeepayFees,
        fees: zeepayFees,
      },
      null,
      { zeepay: { zeepay_id: data?.zeepay_id, gateway_id: data?.gateway_id } },
    );
  }

  async handleSuccessfulSubscriptionPayments(
    payment: PaymentDocument,
    gatewayPaymentInfo: { amount: number; fees: number },
    card?: Card,
    meta: any = {},
  ) {
    let oldSubscriptionType: PLAN_TYPE;
    let newSubscriptionType: PLAN_TYPE;

    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: payment.owner })
      .toPromise();

    const plan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { _id: payment.meta.plan })
      .toPromise();

    const planOption = plan.options.find(
      (option: PlanOption & { _id?: string }) => option._id.toString() === payment.meta.plan_option.toString(),
    );

    let subscription: SubscriptionWithPlan;
    if (payment?.meta?.is_upfront && !payment?.meta?.subscription) {
      subscription = await this.brokerTransport
        .send<SubscriptionWithPlan>(BROKER_PATTERNS.PAYMENT.CREATE_NEW_SUBSCRIPTION, {
          data: {
            owner: payment.owner,
            store: payment.meta.store,
            plan: payment.meta.plan,
            plan_option: payment.meta.plan_option,
            last_payment_reference: payment.reference,
          },
          user,
          upfrontPaymentAmount: payment.amount / 100,
        })
        .toPromise();
    } else {
      subscription = await this.resolveSubscriptionWithPlan(payment);
    }

    oldSubscriptionType = subscription.plan.type;
    newSubscriptionType = plan.type;

    const isNewSubscription = !(
      (subscription.plan as Plan).id === String(plan.id) && subscription.plan_option === payment.meta.plan_option
    );

    const userPayments = await this.paymentModel.countDocuments({
      owner: payment.owner,
      type: PAYMENT_TYPES.SUBSCRIPTION,
      status: PAYMENT_STATUS.SUCCESS,
    });

    try {
      const session = await this.connection.startSession();
      session.startTransaction();

      meta = { ...meta, subscription: getDocId(subscription) };
      const mergedMeta = deepMerge(payment?.meta, meta);
      const resolvedPayment = await this.resolveAndUpdatePayment(payment, gatewayPaymentInfo, session, mergedMeta);

      this.handleUpgradesAndDowngrades(payment, oldSubscriptionType, newSubscriptionType, planOption);

      await this.customerIo.trackUserEvent({
        userId: getDocId(user).toString(),
        name: 'subscription_started',
        data: {
          email: user.email,
          name: user.name,
          phone: user.phone.split('-').join(''),
        },
      });

      await session.commitTransaction(async () => {
        subscription = await this.updateSubscriptionWithNewPlan(
          subscription,
          isNewSubscription,
          plan,
          planOption,
          getDocId(payment),
          payment.meta.store,
          userPayments === 0,
          payment?.meta?.is_upfront,
        );

        if (resolvedPayment?.partner_payment) {
          await this.updateCreditPayment(resolvedPayment, `Payment for ${plan.name} plan subscription`);
        }

        if (user?.referred_by && !payment?.meta?.is_upfront) {
          await this.creditReferral(resolvedPayment, user, planOption.interval);
        }

        await this.sendNotifications(payment, subscription, user, isNewSubscription, plan, planOption);
      });
    } catch (error) {
      this.logger.log(`error occurred in processing payment ${error}`);
    }
  }

  async resolveSubscriptionWithPlan(payment: PaymentDocument) {
    let subscription = await this.brokerTransport
      .send<Subscription>(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
        filter: {
          _id: payment.meta.subscription,
        },
        update: {
          last_payment_reference: payment?.reference,
        },
      })
      .toPromise();

    subscription.plan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, {
        _id: subscription.plan,
      })
      .toPromise();

    return subscription as SubscriptionWithPlan;
  }

  async handleUpgradesAndDowngrades(
    payment: PaymentDocument,
    oldSubscriptionType: PLAN_TYPE,
    newSubscriptionType: PLAN_TYPE,
    planOption: PlanOption,
  ) {
    const isDowngrade = planPriority[oldSubscriptionType] > planPriority[newSubscriptionType];
    const isUpgrade = planPriority[oldSubscriptionType] <= planPriority[newSubscriptionType];

    if (isUpgrade) {
      await this.brokerTransport
        .send(BROKER_PATTERNS.STORE.ENABLE_STORE_FEATURES, {
          filter: { owner: payment.owner },
          isPaidSubscription: true,
          toPlanKey: newSubscriptionType,
          planOption: planOption,
        })
        .toPromise();
    }

    if (isDowngrade) {
      await this.brokerTransport
        .send(BROKER_PATTERNS.STORE.DISABLE_STORE_FEATURES, {
          filter: { owner: payment.owner },
          isPaidSubscription: true,
          toPlanKey: newSubscriptionType,
          planOption: planOption,
        })
        .toPromise();
    }
  }

  async updateSubscriptionWithNewPlan(
    subscription: Subscription,
    isNewSubscription: boolean,
    plan: Plan,
    planOption: PlanOption,
    payment_id: string,
    store: string,
    isFirstPayment: boolean,
    isPaidUpfront: boolean,
  ) {
    const updatedSubscription = await this.brokerTransport
      .send<Subscription>(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
        filter: {
          _id: getDocId(subscription),
        },
        update: {
          last_payment_date: new Date(),
          status: SUBSCRIPTION_STATUS.ACTIVE,
          isFree: false,
          plan: plan.id,
          plan_option: getDocId(planOption),
          next_payment_date: isPaidUpfront ? getDateAt(new Date(), 30) : getDateAt(new Date(), planOption.interval),
          first_payment_date: isFirstPayment ? new Date() : subscription.first_payment_date,
          cancel_at_period_end: false,
          paid_upfront: isPaidUpfront,
        },
        payment_id,
        store,
      })
      .toPromise();

    updatedSubscription.plan = plan;

    return updatedSubscription as SubscriptionWithPlan;
  }

  async creditReferral(payment: PaymentDocument, user: User, planInterval: number) {
    const referrer = await this.brokerTransport
      .send<Referrals>(BROKER_PATTERNS.USER.GET_REFERRALS, user?.referred_by)
      .toPromise(); //returns the user that referred this user

    const referralData = referrer.referrals.find((r) => String(r.user) === String(user.id));
    const timesClaimed = referralData?.times_claimed;

    if (timesClaimed === MAX_REFERRALS_EARNING_MONTHS) {
      return;
    }

    const claimsLeft = MAX_REFERRALS_EARNING_MONTHS - timesClaimed;
    const monthsToEarn = planInterval >= 90 ? claimsLeft : 1;

    const creditWallet = await this.brokerTransport
      .send<CatlogCredits>(BROKER_PATTERNS.USER.CREDITS.GET_CREDITS, {
        user: user?.referred_by,
      })
      .toPromise();

    if (!creditWallet) {
      this.logger.error(`Adding referral credit: User has no credit wallet ${user.referred_by}`);
      return;
    }

    await this.brokerTransport
      .send<CatlogCreditsTransactions>(BROKER_PATTERNS.USER.CREDITS.ADD_CREDITS, {
        user_id: user?.referred_by,
        amount: CREDITS.REFERRAL.COMMISION.SUBSCRIPTION[CURRENCY_COUNTRY_MAP[creditWallet.currency]] * monthsToEarn,
        meta: {
          referred_user: user.id,
          timestamp: Date.now(),
        },
        narration: `${user.name} made a subscription payment`,
      })
      .toPromise();

    await this.brokerTransport
      .send<CatlogCreditsTransactions>(BROKER_PATTERNS.USER.REFERRAL_SUBSCRIBED, {
        user: user?.referred_by,
        referredUser: user.id,
        timesClaimed: monthsToEarn,
      })
      .toPromise();
  }

  async sendNotifications(
    payment: PaymentDocument,
    subscription: Subscription,
    user: User,
    isNewSubscription: boolean,
    plan: Plan,
    planOption: PlanOption,
  ) {
    console.log('<===== BEFORE SENDING SSE EVENT =====>');
    this.sendSSEEvent(payment);

    console.log('<===== PAST SENDING SSE EVENT =====>');

    const primaryStore = user.stores[0];
    const country = planOption.country as COUNTRY_CODE;
    const currency = COUNTRY_CURRENCY_MAP[country];
    const amountString = toCurrency(toNaira(payment.amount_with_charge), currency);

    if (getAppEnv() === 'production') {
      try {
        await this.slack.sendSubscriptionNotification({
          name: user.name,
          country: country + ' ' + COUNTRY_FLAG_EMOJIS[country],
          plan_name: plan.name + ' ' + planOption.interval_text,
          amount: amountString,
          store_name: primaryStore.name,
          store_slug: primaryStore.slug,
          whatsapp: formatPhoneNumber(user.phone).replace('+', ''),
          isNewSubscriber:
            !subscription?.first_payment_date || dayjs(subscription?.first_payment_date).isSame(dayjs(), 'day'),
          first_subscription_date: formatDate(subscription.first_payment_date),
        });
      } catch (e) {
        console.log(e);
        console.log('SENDING SLACK SUBSCRIPTION NOTIFICATION FAILED');
      }
    }

    const commonData = {
      name: user.name.split(' ')[0],
      plan_name: String(plan.name),
      payment_reference: String(payment.reference),
      next_payment_date: formatDate(subscription.next_payment_date),
      interval: planOption.interval_text,
    };

    if (plan.type !== PLAN_TYPE.KITCHEN) {
      const emailData = {
        to: user.email,
        subject: isNewSubscription
          ? 'Your subscription was successful 💳'
          : 'Your subscription renewal was successful 💳',
        data: {
          ...commonData,
          amount: amountString,
          payment_method: payment.payment_method.split('_').join(' '),
        },
      };

      if (isNewSubscription) {
        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.NEW_SUBSCRIPTION, emailData);
      } else {
        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.SUBSCRIPTION_SUCCESSFUL, emailData);
      }
    }

    if (plan.type === PLAN_TYPE.KITCHEN) {
      const emailData = {
        to: user.email,
        subject: isNewSubscription
          ? 'Your subscription was successful 💳'
          : 'Your subscription renewal was successful 💳',
        data: {
          ...commonData,
          price: amountString,
          payment_method: payment.payment_method.split('_').join(' '),
          token_amount: String(planOption.chowbot_tokens),
        },
      };

      if (isNewSubscription) {
        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.CHOWBOT_NEW_SUBSCRIPTION, emailData);
      } else {
        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.CHOWBOT_SUBSCRIPTION_RENEWED, emailData);
      }
    }
  }
}
