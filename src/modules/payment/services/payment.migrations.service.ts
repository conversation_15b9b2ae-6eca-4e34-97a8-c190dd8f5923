import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PAYMENT_METHODS, PAYMENT_PROVIDERS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import { CURRENCIES } from '../../country/country.schema';
import { Invoice } from '../../invoice/invoice.schema';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';
import { Plan } from '../../plan/plan.schema';
import { PaymentService } from './index.service';
import { Subscription } from '../../subscription/subscription.schema';
import { getDocId } from '../../../utils/functions';
export class PaymentMigrationsService extends PaymentService {
  async migratePayments() {
    const payments = await this.paymentModel.find({}).lean();
    const res = [];

    for (const payment of payments) {
      payment.meta = {};
      payment.payment_method = payment.payment_method_type as PAYMENT_METHODS;
      payment.amount_with_charge = payment.amount * 100;
      payment.amount = payment.amount * 100;
      payment.payment_method_type = undefined;

      if (payment.plan) {
        payment.meta.plan = payment.plan;
        payment.plan = undefined;
        payment.type = PAYMENT_TYPES.SUBSCRIPTION;
      } else {
        payment.type = PAYMENT_TYPES.INVOICE;
      }

      if (payment.subscription) {
        payment.meta.subscription = payment.subscription;
        payment.subscription = undefined;
      }

      res.push(this.paymentModel.findByIdAndUpdate(payment._id, { ...payment }, { new: true }).exec());
    }

    const updatedPayments = await Promise.all(res);

    return updatedPayments;
  }

  async addCurrencyToPayments() {
    const payments = await this.paymentModel.find({}).lean();
    const res = [];

    for (const payment of payments) {
      res.push(
        this.paymentModel
          .findByIdAndUpdate(
            payment._id,
            {
              currency: payment?.currency ?? CURRENCIES.NGN,
            },
            { new: true },
          )
          .exec(),
      );
    }

    const updatedPayments = await Promise.all(res);

    return updatedPayments;
  }

  async addProviderToPayments() {
    const payments = await this.paymentModel
      .find({
        payment_method: PAYMENT_METHODS.MONNIFY_TRANSFER,
      })
      .lean();
    const res = [];

    for (const payment of payments) {
      res.push(
        this.paymentModel
          .findByIdAndUpdate(
            payment._id,
            {
              payment_method: PAYMENT_METHODS.TRANSFER,
              provider: payment?.provider ?? PAYMENT_PROVIDERS.MONNIFY,
            },
            { new: true },
          )
          .exec(),
      );
    }

    const updatedPayments = await Promise.all(res);

    return updatedPayments;
  }

  async addPlanOptionToPayments() {
    const allPlans = await this.brokerTransport.send<Plan[]>(BROKER_PATTERNS.PLAN.GET_PLANS, {}).toPromise();

    // Fetch payments without plan_option
    const paymentsWithoutOption = await this.paymentModel
      .find({
        type: PAYMENT_TYPES.SUBSCRIPTION,
        'meta.plan': { $exists: true },
        'meta.plan_option': { $exists: false },
      })
      .limit(2000)
      .lean();

    if (paymentsWithoutOption.length === 0) {
      return {
        message: 'No more payments to update',
        data: [],
      };
    }

    const updates = await Promise.all(
      paymentsWithoutOption.map(async (payment) => {
        const paymentPlan = allPlans.find((plan) => plan.id === payment.meta.plan);

        if (!paymentPlan || typeof paymentPlan !== 'object') {
          return null;
        }

        // Find the new plan where the plan type matches
        const newPlan = allPlans.find(
          (plan) => plan.type === paymentPlan.type && plan.options && plan.options.length > 0,
        );

        if (!newPlan) {
          return null;
        }

        // Find the plan option with matching country & interval
        const planOption = newPlan.options.find(
          (option) => option.country === paymentPlan.country && option.interval === paymentPlan.interval,
        );

        if (!planOption) {
          return null;
        }

        // Update the payment with the new plan's id and plan option's id
        return this.paymentModel.findByIdAndUpdate(payment._id, {
          'meta.plan': newPlan.id,
          'meta.plan_option': planOption.id,
        });
      }),
    );

    return {
      message: `Updated ${updates.length} payments. Trigger the process again to update more payments`,
      data: updates,
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async migratePaymentReceiverDetails(
    batchSize: number = 2000,
    delayMs: number = 5000,
  ): Promise<{ updatedCount: number }> {
    let updatedCount = 0;
    let hasMore = true;
    let skip = 0;

    while (hasMore) {
      const payments = await this.paymentModel
        .find({ type: PAYMENT_TYPES.INVOICE, 'meta.invoice': { $exists: true }, customer: { $exists: false } })
        .skip(skip)
        .limit(batchSize)
        .exec();
      if (payments.length === 0) {
        hasMore = false;
        continue;
      }

      const updatePromises = payments.map(async (payment) => {
        const invoice = await this.brokerTransport
          .send<Invoice>(BROKER_PATTERNS.INVOICE.GET_INVOICE, {
            invoice_id: payment?.meta?.invoice,
          })
          .toPromise();

        if (invoice && invoice.receiver) {
          payment.customer = {
            id: invoice.receiver.id,
            name: invoice.receiver.name,
            phone: invoice.receiver.phone,
            email: invoice.receiver.email,
          };
          await payment.save();
          updatedCount++;
        }
      });

      await Promise.all(updatePromises);

      skip += batchSize;

      if (hasMore) {
        await this.delay(delayMs);
      }
    }

    return { updatedCount };
  }

  async migrateUpfrontPaymentSubscriptions() {
    const payments = await this.paymentModel.find({
      type: PAYMENT_TYPES.SUBSCRIPTION,
      'meta.is_upfront': true,
      status: PAYMENT_STATUS.SUCCESS,
    });

    for (const payment of payments) {
      const subscription = await this.brokerTransport
        .send<Subscription>(BROKER_PATTERNS.PAYMENT.BASIC_SUBSCRIPTION_UPDATE, {
          filter: { owner: payment.owner },
          update: { last_payment_reference: payment.reference, paid_upfront: true },
        })
        .toPromise();

      if (subscription) {
        await this.paymentModel.findByIdAndUpdate(getDocId(payment), {
          'meta.subscription': getDocId(subscription),
        });
      }
    }

    return {
      message: `Migrated ${payments.length} payments`,
      data: payments,
    };
  }
}
