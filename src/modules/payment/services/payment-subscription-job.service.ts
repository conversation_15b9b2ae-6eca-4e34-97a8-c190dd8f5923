import { PaymentService } from './index.service';
import { Plan } from '../../plan/plan.schema';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PLAN_TYPE } from '../../../enums/plan.enum';
import { Subscription, SubscriptionDocument } from '../../subscription/subscription.schema';
import { DocumentQuery, Types } from 'mongoose';
import { BadRequestException, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { getDateDiffInDays, getDateDiffInHours } from '../../../utils/time';
import moment from 'moment';
import { User, UserDocument } from '../../user/user.schema';
import {
  PAYMENT_METHODS,
  PAYMENT_STATUS,
  SUBSCRIPTION_STATUS,
  PAYMENT_TYPES,
  MOBILE_MONEY_NETWORK,
} from '../../../enums/payment.enum';
import { Payment, PaymentData } from '../payment.schema';
import { registerErrors } from '../../../utils/errors.util';
import { formatPhoneNumber, getAppEnv, isUsingProdDBLocally } from '../../../utils';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InternalApiKeyGuard } from '../../../guards/internal-api-key.guard';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { InternalApiJWTGuard } from '../../../guards/api.guard';
import { Mutex } from 'async-mutex';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, COUNTRY_FLAG_EMOJIS, CURRENCIES } from '../../country/country.schema';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';
import { getDocId, toKobo } from '../../../utils/functions';
import { walletPaymentFeeCalculator } from '../../../utils/fees';
import { TRANSACTION_CHANNELS, Wallet } from '../../wallets/wallet.schema';
import { ApiExcludeEndpoint, ApiSecurity } from '@nestjs/swagger';
import { CardDocument } from '../card.schema';
import dayjs from 'dayjs';
import { Transaction } from '@sentry/types';
import { Store } from '../../store/store.schema';
import { PaymentGettersService } from './payment.getters.service';

const mutex = new Mutex();
@ApiSecurity('bearer')
@Controller('payment-service')
export class PaymentSubscriptionJob extends PaymentGettersService {
  @Get('')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @Cron(CronExpression.EVERY_DAY_AT_2PM, { name: 'subscriptionservice' })
  async renewSubscription() {
    if (mutex.isLocked() || isUsingProdDBLocally()) {
      this.logger.warn('Subscription service is already running. Exiting this run.');
      return {};
    }

    this.logger.log('Starting subscription renewal process...');
    let subscriptionData = [];

    mutex.acquire().then(async (release) => {
      try {
        this.logger.log('Acquired mutex lock for subscription renewal.');

        const premiumPlans = await this.brokerTransport
          .send<Plan[]>(BROKER_PATTERNS.PLAN.GET_PLANS, { is_paid_plan: true })
          .toPromise();

        this.logger.log(`Fetched premium plans: ${JSON.stringify(premiumPlans)}`);
        this.logger.log(`Plan IDs being used: ${premiumPlans.map((pp) => pp.id).join(', ')}`);

        const subscriptionsCount = await this.brokerTransport
          .send(BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_DOCUMENTS, {
            plan: { $in: premiumPlans.map((pp) => pp.id) },
            data: { $ne: true },
            next_payment_date: { $lte: new Date() },
          })
          .toPromise();

        this.logger.log(`Total subscriptions to process: ${subscriptionsCount}`);

        let iterationCount = 1;
        while (subscriptionData.length < subscriptionsCount) {
          const paginationQuery = {
            per_page: 250,
            page: iterationCount,
          };

          const subscriptionQuery = (await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.AGGREGATE_SUBSCRIPTION, {
              aggregations: premiumPlans.map((pp) => pp.id),
              paginationQuery,
            })
            .toPromise()) as Subscription[];

          subscriptionData = [...subscriptionData, ...subscriptionQuery];
          this.logger.log(`Fetched ${subscriptionQuery.length} subscriptions in iteration ${iterationCount}.`);
          iterationCount += 1;
        }

        this.logger.log(`Total subscriptions to process after fetching: ${subscriptionData.length}`);

        for await (const subscription of subscriptionData) {
          try {
            const subscriptionActive =
              getDateDiffInDays(moment(subscription.next_payment_date).toDate(), new Date()) > 0;

            if (subscriptionActive || !subscription.next_payment_date) {
              this.logger.log(
                `Skipping subscription ID: ${subscription._id} as it is active or missing a payment date.`,
              );
              continue;
            }

            if (subscription.cancel_at_period_end && !subscriptionActive) {
              await this.revertSubscription(subscription);
              this.logger.log(`Reverted subscription ID: ${subscription._id} to free plan.`);
              continue;
            }

            const user = subscription?.owner as UserDocument;

            if (!user || !user?.email || user?.reference) {
              this.logger.warn(`Skipping subscription ID: ${subscription._id} due to missing user details.`);
              continue;
            }

            this.logger.log(['SUBSCRIPTION JOB', user.email, subscription].map((_) => JSON.stringify(_)).join(', '));
            this.logger.log(`Processing subscription ID: ${subscription._id} for user email: ${user.email}`);

            const mobileMoneyMethods = await this.mobileMoneyMethodModel.find({
              owner: getDocId(subscription.owner),
            });

            const cards = await this.cardModel.find({
              user: getDocId(subscription.owner),
            });

            const ddAuthTokens = await this.ddTokenModel.find({
              user: getDocId(subscription.owner),
            });

            if (cards.length === 0 && ddAuthTokens.length === 0) {
              this.logger.warn(`No cards or dd auth found for user: ${user.email}. Attempting to debit wallets.`);
              await this.attemptToDebitWallets(user, subscription, null);
              continue;
            }

            let chargeSuccess = false;
            let payment: PaymentData;

            // if (mobileMoneyMethods.length > 0) {
            //   for (const method of mobileMoneyMethods) {
            //     const result = await this.initiateMobileMoneyDebit(subscription, payment, user, {
            //       network: method.mno as MOBILE_MONEY_NETWORK,
            //       phone: method.msisdn,
            //     });

            //     if (result.success) {
            //       chargeSuccess = true;
            //       break;
            //     }
            //   }
            // }

            if (!chargeSuccess && cards.length > 0) {
              for (const card of cards) {
                if (card.processor === PAYMENT_METHODS.PAYSTACK) {
                  const result = await this.initiatePaystackAutoDebit(subscription, payment, user, card.authorization);
                  if (result.success) {
                    chargeSuccess = true;
                    break;
                  }
                }
              }
            }

            if (!chargeSuccess && ddAuthTokens.length > 0) {
              for (const ddAuth of ddAuthTokens) {
                if (ddAuth.processor === PAYMENT_METHODS.PAYSTACK) {
                  const result = await this.initiatePaystackAutoDebit(
                    subscription,
                    payment,
                    user,
                    ddAuth.authorization,
                  );
                  if (result.success) {
                    chargeSuccess = true;
                    break;
                  }
                }
              }
            }

            if (!chargeSuccess) {
              this.logger.warn(`Charge failed for subscription ID: ${subscription._id}. Attempting wallet debit.`);
              await this.attemptToDebitWallets(user, subscription, payment);
            }
          } catch (err) {
            this.logger.error(`Error processing subscription ID: ${subscription._id}. Error: ${err.message}`);
          }
        }
      } catch (err) {
        this.logger.error(`Error in subscription renewal process: ${err.message}`);
      } finally {
        release();
        this.logger.log('Released mutex lock for subscription renewal.');
      }
    });

    return {};
  }

  @Post('/revert-expired')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async revertExpiredSubscriptions() {
    const freePlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { type: PLAN_TYPE.STARTER })
      .toPromise();

    const startDate = dayjs('2025-02-01').startOf('day').toDate();
    const endDate = dayjs().endOf('day').toDate();

    const expiredSubscriptions = await this.brokerTransport
      .send<Subscription[]>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTIONS_LEAN, {
        filter: {
          plan: { $eq: getDocId(freePlan) },
          data: { $ne: true },
          next_payment_date: {
            $gte: startDate,
            $lte: endDate,
          },
        },
        select: '_id owner plan plan_option',
      })
      .toPromise();

    console.log(`Found ${expiredSubscriptions.length} expired subscriptions to revert.`);

    for await (const subscription of expiredSubscriptions) {
      if (!subscription) continue;

      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { owner: subscription.owner } })
        .toPromise();

      if (!store) continue;

      if (!store.disabled || store.current_plan.plan_type !== PLAN_TYPE.STARTER) {
        console.log(
          `Reverting subscription ID: ${subscription._id} to free plan, store name: ${store.name}`.toLocaleUpperCase(),
        );
        this.revertSubscription(subscription);
      }
    }

    return {
      message: `Reverted ${expiredSubscriptions.length} expired subscriptions to free plan.`,
      data: expiredSubscriptions,
    };
  }

  @Post('/enable-subscriptions')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async reEnableSubscriptions() {
    const freePlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { type: PLAN_TYPE.STARTER })
      .toPromise();

    const startDate = dayjs('2025-02-01').startOf('day').toDate();
    const endDate = dayjs().endOf('day').toDate();

    const updatedAtStart = dayjs('2025-04-07T23:15:00').toDate();
    const updatedAtEnd = dayjs('2025-04-07T23:50:00').toDate();

    const revertedSubscriptions = await this.brokerTransport
      .send<Subscription[]>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTIONS_LEAN, {
        filter: {
          plan: { $eq: getDocId(freePlan) },
          data: { $ne: true },
          next_payment_date: {
            $gte: startDate,
            $lte: endDate,
          },
          updated_at: {
            $gte: updatedAtStart,
            $lte: updatedAtEnd,
          },
        },
      })
      .toPromise();

    console.log(`Found ${revertedSubscriptions.length} expired subscriptions to enable.`);

    for await (const subscription of revertedSubscriptions) {
      if (!subscription) continue;

      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { owner: subscription.owner } })
        .toPromise();

      if (!store) continue;

      let lastSubscriptionPlan: Plan;
      let lastSubscriptionPlanOption: PlanOption;

      if (!subscription.last_payment_reference) {
        const lastSubscriptionPlanType = subscription.initial_plan;
        lastSubscriptionPlan = await this.brokerTransport
          .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { type: lastSubscriptionPlanType })
          .toPromise();
        lastSubscriptionPlanOption = lastSubscriptionPlan.options.find(
          (option) => option.country === store.country && option.interval === 30,
        );
      } else {
        const payment = await this.brokerTransport
          .send<Payment>(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, {
            reference: subscription.last_payment_reference,
          })
          .toPromise();

        lastSubscriptionPlan = await this.brokerTransport
          .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, {
            _id: payment.meta.plan,
          })
          .toPromise();

        lastSubscriptionPlanOption = lastSubscriptionPlan.options.find(
          (option: PlanOption & { _id?: string }) => option._id === payment.meta.plan_option,
        );
      }

      if (!lastSubscriptionPlan || !lastSubscriptionPlanOption) continue;
      await this.brokerTransport
        .send(BROKER_PATTERNS.STORE.ENABLE_STORE_FEATURES, {
          filter: { owner: subscription.owner },
          isPaidSubscription: true,
          toPlanKey: lastSubscriptionPlan.type,
          planOption: lastSubscriptionPlanOption,
        })
        .toPromise();

      const subscriptionIsInactive = dayjs(subscription.next_payment_date).isBefore(dayjs().startOf('day'));
      await this.brokerTransport
        .send(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
          filter: {
            _id: subscription._id,
          },
          update: {
            status: subscriptionIsInactive ? SUBSCRIPTION_STATUS.IN_ACTIVE : SUBSCRIPTION_STATUS.ACTIVE,
            plan: lastSubscriptionPlan.id,
            plan_option: lastSubscriptionPlanOption.id,
          },
        })
        .toPromise();
    }

    return {
      message: `Reenabled ${revertedSubscriptions.length} expired subscriptions to initial plan.`,
      data: revertedSubscriptions,
    };
  }

  async initiateMobileMoneyDebit(
    subscription: SubscriptionDocument,
    payment: PaymentData,
    user: UserDocument,
    momo: { network: MOBILE_MONEY_NETWORK; phone: string },
  ) {
    const country = (subscription.plan_option as PlanOption).country as COUNTRY_CODE;
    const currency = COUNTRY_CURRENCY_MAP[country];

    payment = await this.createPaymentV2(
      {
        type: PAYMENT_TYPES.SUBSCRIPTION,
        payment_methods: [PAYMENT_METHODS.MOMO],
        plan: getDocId(subscription.plan),
        plan_option: getDocId(subscription.plan_option),
        subscription: subscription._id,
        currency,
      },
      (user.stores[0] as unknown) as string,
    );

    if (!payment?.reference) {
      this.logger.warn(`Payment creation failed for subscription ID: ${subscription._id}`);
      return { success: false };
    }

    try {
      const result = await this.ZeepayWalletDebitPayment({
        payment_reference: payment.reference,
        network: momo.network,
        msisdn: momo.phone,
      });

      if (result.initated) {
        return { success: true };
      }
    } catch (err) {
      this.logger.error(`Error processing subscription ID: ${subscription._id}. Error: ${err.message}`);
      return { success: false };
    }
  }

  async initiatePaystackAutoDebit(
    subscription: SubscriptionDocument,
    payment: PaymentData,
    user: UserDocument,
    auth: string,
  ) {
    const country = (subscription.plan_option as PlanOption).country as COUNTRY_CODE;
    const currency = COUNTRY_CURRENCY_MAP[country];

    payment = await this.createPaymentV2(
      {
        type: PAYMENT_TYPES.SUBSCRIPTION,
        payment_methods: [PAYMENT_METHODS.PAYSTACK],
        plan: getDocId(subscription.plan),
        plan_option: getDocId(subscription.plan_option),
        subscription: subscription._id,
        currency,
      },
      (user.stores[0] as unknown) as string,
    );

    if (!payment?.reference) {
      this.logger.warn(`Payment creation failed for subscription ID: ${subscription._id}`);
      return { success: false };
    }

    let chargeSuccess = await this.chargeCard({
      user,
      reference: payment.reference,
      authorization: auth,
      total_amount: payment?.amount,
      currency,
    });

    if (chargeSuccess) {
      return { success: true };
    }

    return { success: false };

    // if (!chargeSuccess) {
    //   this.logger.warn(`Card charge failed for subscription ID: ${subscription._id}. Attempting wallet debit.`);
    //   await this.attemptToDebitWallets(user, subscription, payment);
    // } else {
    // this.logger.log(`Card charge successful for subscription ID: ${subscription._id}.`);
    // retr
    // }
  }

  async attemptToDebitWallets(user: UserDocument, subscription: SubscriptionDocument, payment: PaymentData = null) {
    try {
      if (!user.auto_debit_wallets) {
        this.logger.warn(`User auto-debit is disabled for user ID: ${user._id}. Marking as charge failed.`);
        await this.chargeFailed(subscription, payment);
        return;
      }

      const planOption = subscription.plan_option as PlanOption;
      const totalPayable = toKobo(planOption.amount + walletPaymentFeeCalculator(planOption.amount));
      const storeWallets = await this.brokerTransport
        .send<Wallet[]>(BROKER_PATTERNS.WALLET.GET_WALLETS, { store: { $in: user.stores } })
        .toPromise();

      if (storeWallets.length === 0) {
        this.logger.warn(`No wallets found for user ID: ${user._id}. Marking as charge failed.`);
        await this.chargeFailed(subscription, payment);
        return;
      }

      let hasPaidFromWallet = false;

      const currency = COUNTRY_CURRENCY_MAP[planOption.country as COUNTRY_CODE];

      if (currency) {
        //fix for job failing locally because of new country data that hasn't been added to the COUNTRY_CURRENCY_MAP
        for await (const wallet of storeWallets) {
          const canPayFromWallet = wallet.balance >= totalPayable && wallet.currency === currency;

          if (canPayFromWallet) {
            const paymentData = await this.createPaymentV2(
              {
                type: PAYMENT_TYPES.SUBSCRIPTION,
                payment_methods: [PAYMENT_METHODS.WALLET],
                plan: getDocId(subscription.plan),
                plan_option: getDocId(subscription.plan_option),
                subscription: subscription._id,
                currency,
              },
              wallet.store as string,
            );

            if (paymentData.reference) {
              hasPaidFromWallet = true;
              this.logger.log(`Successfully debited wallet ID: ${wallet.id} for user ID: ${user._id}.`);
              break;
            }
          }
        }
      }

      if (!hasPaidFromWallet) {
        this.logger.warn(`Failed to debit wallets for user ID: ${user._id}.`);
        await this.chargeFailed(subscription, payment);
      }
    } catch (err) {
      this.logger.error(`Error attempting to debit wallets for user ID: ${user._id}. Error: ${err.message}`);
      await this.chargeFailed(subscription, payment);
    }
  }

  private getPlanPaymentMethod(plan: Plan) {
    return plan.methods.find((method) => method.type === PAYMENT_METHODS.PAYSTACK);
  }

  private async chargeCard({ total_amount, user, authorization, reference, currency }) {
    return this.paystackRepository.chargeAuthorization(
      {
        amount: total_amount,
        email: user.email,
        authorization_code: authorization,
        reference: reference,
        currency,
      },
      currency,
    );
  }

  private async chargeFailed(subscription: Subscription, payment?: PaymentData) {
    const user = subscription.owner as User;
    const plan = subscription.plan as Plan;
    const isRenewal = !!subscription?.last_payment_reference;

    if (!plan || !user) return;

    const session = await this.connection.startSession();
    session.startTransaction();

    const diffInPaymentHours = Math.abs(getDateDiffInHours(new Date(subscription.next_payment_date)));

    if (payment) {
      this.paymentModel.findOneAndUpdate(
        { reference: payment.reference },
        {
          status: PAYMENT_STATUS.FAILED,
        },
        { session },
      );
    }

    await session
      .commitTransaction()
      .then(async () => {
        await this.brokerTransport
          .send(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
            filter: {
              _id: subscription._id,
            },
            update: {
              status: SUBSCRIPTION_STATUS.IN_ACTIVE,
              last_payment_reference: payment?.reference || subscription.last_payment_reference,
            },
          })
          .toPromise();
      })
      .catch(registerErrors);

    const mailInterval = await (async () => {
      const x = diffInPaymentHours;

      // 143.5 hours === 30 minutes before beginning of 6th day
      // 167.5 hours === 30 minutes before end of 6th day
      if (x >= 143.5 && x <= 167.5) {
        // Send whatsapp message
        await this.whatsapp.sendSubscriptionExpiryMessage(formatPhoneNumber(user.phone), {
          customerName: user.name.split(' ')[0],
          planName: plan.name,
          type: isRenewal ? 'renewal' : 'free_trial',
        });
      }

      // 23.5 hours === 30 minutes before end of first day
      // 71.5 hours === 30 minutes before beginning of 3rd day
      // 85.5 hours === 30 minutes before end of 3rd day
      // 143.5 hours === 30 minutes before beginning of 6th day
      // 167.5 hours === 30 minutes before end of 6th day
      return (x >= 0 && x < 23.5) || (x >= 71.5 && x <= 85.5) || (x >= 143.5 && x <= 167.5);
    })();

    if (mailInterval && !user?.reference) {
      await this.sendRenewalFailedMail(subscription, isRenewal);
    }

    const subExpired = diffInPaymentHours >= 167.5;
    if (subExpired) {
      await this.revertSubscription(subscription);
    }
  }

  async sendRenewalFailedMail(subscription: Subscription, isRenewal: boolean) {
    const plan = subscription.plan as Plan;
    const user = subscription.owner as User;

    if (user?.stores && !user?.reference) {
      const downgradeMessages = {
        [PLAN_TYPE.BASIC]: [
          'Customers will only see 10 items on your store',
          'Your store will no longer be customizable',
          "You won't be able to add options to your products or create coupons",
        ],
        [PLAN_TYPE.BUSINESS_PLUS]: [
          'Customers will only see 10 items on your store',
          'If you had multiple stores, only one will continue to be active',
          'Your store will no longer be customizable',
        ],
        [PLAN_TYPE.KITCHEN]: [
          'Customers will not be able to order from you via Chowbot',
          'If you have multiple stores, only one will continue to be active',
          'You will not be able to book deliveries',
        ],
      };

      const renewalData = {
        to: user.email,
        subject: isRenewal ? 'We could not renew your subscription 😕' : 'Your free trial on Catlog is ending 😕',
        data: {
          name: user.name.split(' ')[0],
          plan_name: plan.name,
          days_left: String(7 - Math.abs(getDateDiffInDays(new Date(subscription.next_payment_date)))),
          main_feature: plan.type === PLAN_TYPE.KITCHEN ? 'Chowbot' : 'Catlog',
          downgrade_message_1: downgradeMessages[plan.type][0],
          downgrade_message_2: downgradeMessages[plan.type][1],
          downgrade_message_3: downgradeMessages[plan.type][2],
          cta_link: `${process.env.CATLOG_DASHBOARD}/dashboard?renew_plan=true`,
        },
      };

      await this.resend.sendEmail(
        isRenewal ? BROKER_PATTERNS.MAIL.RENEW_SUBSCRIPTION : BROKER_PATTERNS.MAIL.TRIAL_ENDED,
        renewalData,
      );

      await this.brokerTransport
        .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
          message: {
            title: isRenewal ? 'Your subscription is expiring 😕' : 'Your free trial on Catlog is ending 😕',
            message: `We're unable to automatically renew your subscription, please click here to renew it.`,
            path: `/dashboard?renew_plan=true`,
          },
          owner_only: true,
          store: user?.stores[0],
        })
        .toPromise();
    }
  }

  async revertSubscription(subscription: Subscription) {
    const planId = getDocId(subscription.plan);
    const userId = getDocId(subscription.owner);

    const plan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, {
        _id: planId,
      })
      .toPromise();

    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        _id: userId,
      })
      .toPromise();

    if (!user || !plan) return null;

    const currentPlanOption = plan.options.find((o) => getDocId(o) === getDocId(subscription.plan_option));

    // Revert to starter plan
    const starterPlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, {
        type: PLAN_TYPE.STARTER,
        options: { $exists: true, $ne: [] },
      })
      .toPromise();

    const planOption = starterPlan.options.find((option) => option.country === currentPlanOption.country);

    await this.brokerTransport
      .send(BROKER_PATTERNS.PAYMENT.UPDATE_SUBSCRIPTION, {
        filter: {
          _id: subscription._id,
        },
        update: {
          plan: starterPlan.id,
          status: SUBSCRIPTION_STATUS.ACTIVE,
          plan_option: getDocId(planOption),
          cancel_at_period_end: false, // Reset the flag
        },
      })
      .toPromise();

    await this.brokerTransport
      .send(BROKER_PATTERNS.STORE.DISABLE_STORE_FEATURES, {
        filter: {
          owner: (user as any)._id || subscription.owner,
        },
        isPaidSubscription: false,
        toPlanKey: PLAN_TYPE.STARTER,
        planOption: planOption,
      })
      .toPromise();

    const store = await this.brokerTransport
      .send(BROKER_PATTERNS.STORE.GET_STORE, {
        owner: (user as any)._id || subscription.owner,
      })
      .toPromise();

    if (store && !user?.reference) {
      const downgradeMessages = {
        [PLAN_TYPE.BASIC]: [
          'Customers can only see 10 items on your store',
          'Your store will no longer be customizable',
          "You won't be able to add options to your products or create coupons",
        ],
        [PLAN_TYPE.BUSINESS_PLUS]: [
          'Customers can only see 10 items on your store',
          'If you had multiple stores, only one will continue to be active',
          'Your store will no longer be customizable',
        ],
        [PLAN_TYPE.KITCHEN]: [
          "Customers can't order from you via Chowbot",
          'If you had multiple stores, only one will continue to be active',
          "You won't be able to book deliveries",
        ],
      };

      const cancellationData = {
        to: user.email,
        subject: 'Your Catlog subscription has been cancelled 🙁',
        data: {
          name: user.name.split(' ')[0],
          plan_name: plan.name,
          new_store_slug: store.slugs[0],
          downgrade_message_1: 'Your store link(s) will no longer be active',
          downgrade_message_2: 'You will not be able to manage products on your store',
          cta_link: `${process.env.CATLOG_DASHBOARD}/my-store/change-plan`,
        },
      };

      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.SUBSCRIPTION_CANCELLED, cancellationData);

      await this.brokerTransport
        .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
          message: {
            title: 'Your subscription has expired 😕',
            message: `We couldn't renew your subscription and you've been rolled back to the starter plan. It's easy to get back though, just click here`,
            path: `/dashboard?renew_plan=true`,
          },
          owner_only: true,
          store: user?.stores[0],
        })
        .toPromise();

      await this.customerIo.trackUserEvent({
        userId: getDocId(user).toString(),
        name: 'subscription_ended',
        data: {
          email: user.email,
          name: user.name,
          phone: user.phone.split('-').join(''),
        },
      });

      const previousPayments = await this.countSubscriptionPayments(getDocId(user));

      if (getAppEnv() === 'production') {
        try {
          await this.slack.sendDropOffNotification({
            name: user.name,
            country: currentPlanOption.country + ' ' + COUNTRY_FLAG_EMOJIS[currentPlanOption.country as COUNTRY_CODE],
            plan_name: plan.name + ' ' + planOption.interval_text,
            store_name: store?.name,
            store_slug: store?.slug,
            whatsapp: formatPhoneNumber(user.phone).replace('+', ''),
            previousSubscriptionPayments: previousPayments.count,
          });
        } catch (e) {
          console.log(e);
          console.log('SENDING SLACK DROPOFF NOTIFICATION FAILED');
        }
      }
    }

    return 'done';
  }

  @Post('/revert-failed-wallet-payments')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async revertFailedWalletPayments() {
    console.log('reverting failed wallet payments');
    const walletPayments = await this.paymentModel
      .find({
        status: PAYMENT_STATUS.SUCCESS,
        payment_method: PAYMENT_METHODS.WALLET,
        'meta.transaction_id': { $exists: false },
        type: PAYMENT_TYPES.SUBSCRIPTION,
      })
      .sort({ created_at: -1 })
      .limit(10);

    let failedPaymentsCount = 0;
    let failedRepayments = 0;
    let succeededRepayments = 0;
    const lastFailedPayments: { [key: string]: Payment } = {};
    const paymentsMarkedAsFailed = [];

    for await (const payment of walletPayments) {
      console.log(`Reviewing payment: ${payment.id}`);
      if (payment.meta?.subscription) {
        const hasTransaction = await this.brokerTransport
          .send<Transaction>(BROKER_PATTERNS.WALLET.GET_TRANSACTION, {
            'meta.payment_id': getDocId(payment),
            type: 'debit',
            channel: 'SUBSCRIPTION_PAYMENT',
          })
          .toPromise();

        if (!hasTransaction) {
          failedPaymentsCount += 1;
          await this.paymentModel.findOneAndUpdate(
            { _id: payment._id },
            { status: PAYMENT_STATUS.FAILED, formerly_succeeded: true },
          );
          paymentsMarkedAsFailed.push(payment.id);
          console.log(`Payment ${payment.id} marked as failed`);

          if (lastFailedPayments[payment.meta.subscription as string]) continue;
          lastFailedPayments[payment.meta.subscription as string] = payment;
          console.log(`Payment ${payment.id} recorded to be retried`);
        } else {
          await this.paymentModel.findOneAndUpdate(
            { _id: payment._id },
            { 'meta.transaction_id': getDocId(hasTransaction) },
          );
        }
      }
    }

    const subscriptionIds = Object.keys(lastFailedPayments);

    for await (const subscriptionId of subscriptionIds) {
      const payment = lastFailedPayments[subscriptionId];
      console.log(`Processing payment ${payment.id} for retry`);
      try {
        const store = await this.brokerTransport
          .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: payment.meta.store as string })
          .toPromise();

        const walletId = store.wallets.find((w) => w.currency === payment.currency)?.id;

        const walletData = await this.brokerTransport
          .send(BROKER_PATTERNS.WALLET.DEBIT_WALLET, {
            walletId: walletId,
            amount: payment.amount,
            channel: TRANSACTION_CHANNELS.SUBSCRIPTION_PAYMENT,
            narration: `Subscription repayment for ${dayjs(payment.created_at).format('MMM/YYYY')}`,
            meta: { payment_id: getDocId(payment) },
            source: { purpose: 'Subscription payment', name: 'Catlog' },
            fee: payment.amount_with_charge - payment.amount,
          })
          .toPromise();

        console.log(`Response from retrying payment ${payment.id}: ${JSON.stringify(walletData ?? {})}`);

        if (!walletData.success) {
          console.error(`Retrying payment ${payment.id} failed`);
          throw new Error('Payment Failed');
        } else {
          succeededRepayments += 1;
          await this.paymentModel.findOneAndUpdate(
            { _id: getDocId(payment) },
            {
              status: PAYMENT_STATUS.SUCCESS,
              formerly_succeeded: true,
              'meta.transaction_id': getDocId(walletData.transaction),
            },
          );
        }
      } catch (e) {
        failedRepayments += 1;
        console.error(`Retrying payment ${payment.id} failed`);
        console.error(`Reverting  subscription ${subscriptionId}`);
        const subscription = await this.brokerTransport
          .send<Subscription>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, { _id: subscriptionId })
          .toPromise();
        this.revertSubscription(subscription);

        console.error(` subscription ${subscriptionId} reverted successfully`);
      }
    }

    return {
      message: 'Reverted failed wallet payments',
      data: lastFailedPayments,
      failedPaymentsCount,
      walletPaymentsCount: walletPayments.length,
      succeededRepayments,
      failedRepayments,
      paymentsMarkedAsFailed,
    };
  }

  // async testCharge() {
  //   const ddAuth = await this.ddTokenModel.findOne({ authorization: { $exists: true } });

  //   if (!ddAuth) throw new BadRequestException('No DD auth found');

  //   let chargeSuccess = await this.chargeCard({
  //     user: { email: '<EMAIL>' },
  //     reference: 'CLGPAY-1799692',
  //     authorization: ddAuth.authorization,
  //     total_amount: 10000,
  //     currency: CURRENCIES.NGN,
  //   });

  //   return chargeSuccess;
  // }

  @Cron(CronExpression.EVERY_DAY_AT_10AM, { name: 'subscriptionreminderservice' })
  async sendSubscriptionReminders() {
    if (mutex.isLocked() || isUsingProdDBLocally()) {
      this.logger.warn('Subscription reminder service is already running. Exiting this run.');
      return {};
    }

    this.logger.log('Starting subscription reminder process...');
    let subscriptionData = [];

    mutex.acquire().then(async (release) => {
      try {
        this.logger.log('Acquired mutex lock for subscription reminders.');

        const premiumPlans = await this.brokerTransport
          .send<Plan[]>(BROKER_PATTERNS.PLAN.GET_PLANS, { is_paid_plan: true })
          .toPromise();

        // Calculate date 15 days from now
        const reminderDate = new Date();
        reminderDate.setDate(reminderDate.getDate() + 15);

        // Get the start and end of the day for the reminder date
        const startOfReminderDay = new Date(reminderDate);
        startOfReminderDay.setHours(0, 0, 0, 0);

        const endOfReminderDay = new Date(reminderDate);
        endOfReminderDay.setHours(23, 59, 59, 999);

        // Count subscriptions to process
        const subscriptionsCount = await this.brokerTransport
          .send(BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_DOCUMENTS, {
            plan: { $in: premiumPlans.map((pp) => pp.id) },
            next_payment_date: {
              $gte: startOfReminderDay,
              $lte: endOfReminderDay,
            },
          })
          .toPromise();

        this.logger.log(`Total subscription reminders to process: ${subscriptionsCount}`);

        let iterationCount = 1;
        while (subscriptionData.length < subscriptionsCount) {
          const paginationQuery = {
            per_page: 250,
            page: iterationCount,
          };

          // Use a new broker method for fetching reminder subscriptions
          const subscriptionQuery = (await this.brokerTransport
            .send(BROKER_PATTERNS.PAYMENT.AGGREGATE_SUBSCRIPTION_REMINDERS, {
              aggregations: premiumPlans.map((pp) => pp.id),
              paginationQuery,
              reminderDateRange: {
                start: startOfReminderDay,
                end: endOfReminderDay,
              },
            })
            .toPromise()) as Subscription[];

          subscriptionData = [...subscriptionData, ...subscriptionQuery];
          this.logger.log(
            `Fetched ${subscriptionQuery.length} subscriptions for reminders in iteration ${iterationCount}.`,
          );

          if (subscriptionQuery.length < paginationQuery.per_page) {
            break; // Break the loop if we've fetched fewer results than the limit
          }

          iterationCount += 1;
        }

        this.logger.log(`Total subscription reminders to process after fetching: ${subscriptionData.length}`);

        for await (const subscription of subscriptionData) {
          try {
            const user = subscription?.owner as UserDocument;

            if (!user || !user?.email || user?.reference) {
              this.logger.warn(
                `Skipping subscription reminder for ID: ${subscription._id} due to missing user details.`,
              );
              continue;
            }

            this.logger.log(
              `Processing subscription reminder for ID: ${subscription._id} for user email: ${user.email}`,
            );

            // Get plan details
            const planId =
              typeof subscription.plan === 'object' && subscription.plan._id
                ? subscription.plan._id.toString()
                : String(subscription.plan);

            const plan = await this.brokerTransport.send(BROKER_PATTERNS.PLAN.GET_PLAN, { _id: planId }).toPromise();

            if (!plan) {
              this.logger.warn(
                `Skipping subscription reminder for ID: ${subscription._id} due to missing plan details.`,
              );
              continue;
            }

            // Use appropriate approach to get plan option id
            const planOptionId =
              typeof subscription.plan_option === 'object' && subscription.plan_option._id
                ? subscription.plan_option._id.toString()
                : String(subscription.plan_option);

            const planOption = plan.options.find((o) => getDocId(o) === planOptionId);

            if (!planOption) {
              this.logger.warn(
                `Skipping subscription reminder for ID: ${subscription._id} due to missing plan option details.`,
              );
              continue;
            }

            // Format the payment date
            const paymentDate = moment(subscription.next_payment_date).format('MMMM DD, YYYY');

            // Format amount with thousand separators
            const amountString = planOption.amount ? planOption.amount.toLocaleString() : '0';

            // Determine the interval
            const interval = planOption.interval ? planOption.interval : 'month';

            if (subscription.cancel_at_period_end) {
              continue;
            }

            // Send email reminder
            const emailData = {
              to: user.email,
              subject: 'Upcoming Catlog Reminder 🔔',
              data: {
                name: user.name,
                preview_text: 'Your subscription will renew soon',
                plan_name: plan.name,
                amount: amountString,
                next_payment_date: paymentDate,
                interval: interval,
                dashboard_link: `${process.env.CATLOG_DASHBOARD}/dashboard`,
                auto_renewal: subscription.cancel_at_period_end ? 'Off' : 'On',
              },
            };

            await this.resend.sendEmail(BROKER_PATTERNS.MAIL.SUBSCRIPTION_REMINDER, emailData);
            this.logger.log(
              `Sent subscription renewal reminder to ${user.email} for subscription ID: ${subscription._id}`,
            );
          } catch (err) {
            this.logger.error(
              `Error processing subscription reminder for ID: ${subscription._id}. Error: ${err.message}`,
            );
          }
        }
      } catch (err) {
        this.logger.error(`Error in subscription reminder process: ${err.message}`);
      } finally {
        release();
        this.logger.log('Released mutex lock for subscription reminders.');
      }
    });

    return {};
  }
}
