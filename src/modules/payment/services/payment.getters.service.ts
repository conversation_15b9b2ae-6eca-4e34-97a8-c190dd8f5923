import { BadRequestException, UnprocessableEntityException } from '@nestjs/common';
import mongoose, { FilterQuery } from 'mongoose';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import { PLAN_TYPE } from '../../../enums/plan.enum';
import { ACCEPTABLE_TOKEN_AMOUNTS, AMOUNT_PER_TOKEN, UPFRONT_SUBSCRIPTION_AMOUNTS } from '../../../utils/constants';
import { SUBSCRIPTION_CARD_DISCOUNT, SUBSCRIPTION_DD_DISCOUNT, walletPaymentFeeCalculator } from '../../../utils/fees';
import { calculateSubscriptionAmountLeft, getDocId, toKob<PERSON>, to<PERSON><PERSON><PERSON> } from '../../../utils/functions';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCIES, CURRENCY_COUNTRY_MAP } from '../../country/country.schema';
import { Delivery } from '../../deliveries/deliveries.schema';
import { Invoice } from '../../invoice/invoice.schema';
import { Customer } from '../../orders/customers/customer.schema';
import { PaymentMethod } from '../../paymentmethod/paymentmethod.schema';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';
import { Plan } from '../../plan/plan.schema';
import { Store } from '../../store/store.schema';
import { Subscription } from '../../subscription/subscription.schema';
import { CatlogCredits } from '../../user/credits/credits.schema';
import { Wallet } from '../../wallets/wallet.schema';
import {
  GetInAppPaymentMethodsDto,
  GetPaymentAnalyticsDto,
  GetPaymentMethodsDto,
  PaymentHistoryQueryDto,
} from '../payment.dto';
import { Payment, PaymentDocument } from '../payment.schema';
import { PaymentService } from './index.service';
import { DOMAIN_PURCHASE_STATUS, DomainPurchase } from '../../store/domains/domain-purchase.schema';

export class PaymentGettersService extends PaymentService {
  async getPayment(filter: FilterQuery<Payment>) {
    return await this.paymentModel.findOne(filter);
  }

  async getFirstPayment(filter: FilterQuery<Payment>) {
    return this.paymentModel.findOne(filter).sort({ created_at: 1 }).lean();
  }

  async getPaymentMethods(data: GetInAppPaymentMethodsDto, storeId: string) {
    const isUpfront = data.upfront === 'true';
    const paymentMethods = await this.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {
        payment_types: { $in: [data.type] },
        currencies: { $in: data.currency },
        enabled: true,
      })
      .toPromise();

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, {
        filter: { _id: storeId },
        select: { owner: 1, plan: 1, flags: 1, subscription: 1 },
      })
      .toPromise();

    let payableAmount = 0,
      filteredPaymentMethods = [];
    let subscriptionLeft = 0;

    if (data.type === PAYMENT_TYPES.SUBSCRIPTION) {
      if (!data.plan) throw new BadRequestException('Please provide a plan to pay for this subscription');

      if (!data.plan_option) throw new BadRequestException('Please provide a plan option to pay for this subscription');

      const plan = await this.brokerTransport
        .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { _id: data?.plan })
        .toPromise();

      if (!plan) throw new BadRequestException('Invalid plan provided');

      const planOption = plan.options.find(
        (option: PlanOption & { _id?: string }) => option._id.toString() === data?.plan_option,
      );

      if (COUNTRY_CURRENCY_MAP[planOption.country as COUNTRY_CODE] !== data.currency) {
        throw new BadRequestException("Currency doesn't match plan selected");
      }

      if (store?.subscription && !isUpfront) {
        const subscription = store?.subscription as Subscription;
        const currentSubscriptionPlanOption = await this.brokerTransport
          .send<PlanOption>(BROKER_PATTERNS.PLAN.GET_PLAN_OPTION, { id: subscription?.plan_option })
          .toPromise();

        subscriptionLeft = calculateSubscriptionAmountLeft(subscription, currentSubscriptionPlanOption);

        payableAmount = planOption.amount - subscriptionLeft;
      } else {
        payableAmount = isUpfront ? toNaira(UPFRONT_SUBSCRIPTION_AMOUNTS[plan.type][data.currency]) : planOption.amount;
      }
    } else if (data.type === PAYMENT_TYPES.DELIVERY) {
      if (!data.delivery) throw new BadRequestException('Please provide a delivery to pay for');

      const delivery = await this.brokerTransport
        .send<Delivery>(BROKER_PATTERNS.DELVERIES.GET_DELIVERY, { _id: data?.delivery })
        .toPromise();

      if (!delivery) throw new BadRequestException('Invalid delivery provided');

      payableAmount = delivery.delivery_amount;
    } else if (data.type === PAYMENT_TYPES.TOKEN_PURCHASE) {
      if (!data.tokens) throw new BadRequestException('Please provide the amount of tokens to be purchased');

      if (!ACCEPTABLE_TOKEN_AMOUNTS.includes(+data.tokens)) {
        throw new BadRequestException('Invalid token amount provided');
      }

      const plan = await this.brokerTransport
        .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, {
          _id: ((store?.subscription as Subscription)?.plan as any)?._id,
        })
        .toPromise();

      if (plan.type !== PLAN_TYPE.KITCHEN || !store?.flags?.uses_chowbot) {
        throw new BadRequestException('Token Purchase is only businesses using chowbot');
      }

      payableAmount = +data.tokens * AMOUNT_PER_TOKEN;
    } else if (data.type === PAYMENT_TYPES.DOMAIN_PURCHASE) {
      if (!data.domain_purchase) throw new BadRequestException('Please provide a domain to pay for');

      const domainPurchase = await this.brokerTransport
        .send<DomainPurchase>(BROKER_PATTERNS.DOMAIN.GET_PURCHASE, { purchaseId: data?.domain_purchase })
        .toPromise();

      if (!domainPurchase) throw new BadRequestException('Invalid domain purchase provided');

      if (domainPurchase.status !== DOMAIN_PURCHASE_STATUS.PENDING) {
        throw new BadRequestException('This domain has already been paid for');
      }

      if (domainPurchase.currency !== data.currency) {
        throw new BadRequestException('Currency does not match domain purchase currency');
      }

      payableAmount = domainPurchase.amount;
    }

    for (let index = 0; index < paymentMethods.length; index++) {
      const method = paymentMethods[index];

      switch (method.type) {
        case PAYMENT_METHODS.PAYSTACK:
          if (data.type === PAYMENT_TYPES.SUBSCRIPTION) {
            const cards = await this.cardModel.find({
              user: store?.owner,
            });

            if (cards.length < 1) {
              filteredPaymentMethods.push({
                ...method,
                type: PAYMENT_METHODS.CARD,
                discount: SUBSCRIPTION_CARD_DISCOUNT,
              });
            }

            //check for dd authorization token
            const ddTokens = await this.ddTokenModel.find({
              user: store?.owner,
            });

            if (ddTokens.length < 1 && data.currency === CURRENCIES.NGN) {
              filteredPaymentMethods.push({
                ...method,
                type: PAYMENT_METHODS.DIRECT_DEBIT,
                discount: SUBSCRIPTION_DD_DISCOUNT,
              });
            }
          }

          filteredPaymentMethods.push({ ...method });
          break;
        case PAYMENT_METHODS.STARTBUTTON:
          filteredPaymentMethods.push({ ...method });
          break;
        case PAYMENT_METHODS.MOMO:
          // if (data.type === PAYMENT_TYPES.SUBSCRIPTION) {
          filteredPaymentMethods.push({ ...method });
          // }
          break;
        case PAYMENT_METHODS.WALLET:
          const wallet = await this.brokerTransport
            .send<Wallet>(BROKER_PATTERNS.WALLET.GET_WALLET, { storeId })
            .toPromise();

          if (!wallet) break;

          const walletBalance = wallet?.balance;
          let totalPayable = payableAmount + walletPaymentFeeCalculator(payableAmount);

          filteredPaymentMethods.push({
            ...method,
            meta: {
              balance: walletBalance,
              enabled: walletBalance >= toKobo(totalPayable),
            },
          });

          break;
        case PAYMENT_METHODS.CATLOG_CREDIT:
          const creditsWallet = await this.brokerTransport
            .send<CatlogCredits>(BROKER_PATTERNS.USER.CREDITS.GET_CREDITS, { user: store?.owner })
            .toPromise();

          if (!creditsWallet) break;

          filteredPaymentMethods.push({
            ...method,
            meta: {
              balance: creditsWallet.balance,
              enabled: creditsWallet.balance > 0,
            },
          });

          break;
        default:
        //do nothing
      }
    }

    return { payment_methods: filteredPaymentMethods, subscription_left: subscriptionLeft };
  }

  async getStorePublicPaymentMethods(body: GetPaymentMethodsDto) {
    // throw new BadRequestException('Something is not implemented');
    const invoice = await this.brokerTransport
      .send<Invoice>(BROKER_PATTERNS.INVOICE.GET_INVOICE, {
        invoice_id: body.invoice_id,
      })
      .toPromise();

    if (!invoice) throw new BadRequestException('Invoice not found');

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: invoice.store })
      .toPromise();

    if (!store) throw new BadRequestException('Store not found');

    const paymentMethods = await this.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {
        payment_types: { $in: [body.type] },
        enabled: true,
      })
      .toPromise();

    const storePaymentMethods = store.payment_options[invoice.currency]
      .filter((m) => m.enabled)
      .map((m) => ({
        ...paymentMethods.filter((pm) => pm.type === m.type)[0],
      }));

    return storePaymentMethods;
  }

  async verifyPayment(ref: string) {
    const payment = await this.paymentModel.findOne({ reference: ref });
    if (!payment) {
      throw new UnprocessableEntityException('Invalid payment reference');
    }

    const verify = await this.paystackRepository.verifyTransaction(ref, payment.currency);

    if (verify.error || !verify.data) {
      throw new BadRequestException('Payment was not successful');
    }
    return;
  }

  async verifyAndCreditPaystackPayment(ref: string) {
    const payment = await this.paymentModel.findOne({ reference: ref });
    if (!payment) {
      throw new UnprocessableEntityException('Invalid payment reference');
    }

    const verify = await this.paystackRepository.verifyTransaction(ref, payment.currency);

    if (verify.error || !verify.data || verify.data.status !== 'success') {
      throw new BadRequestException('Payment was not successful');
    }

    if (verify.data) {
      await this.brokerTransport
        .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.RESOLVE_PAYSTACK_PAYMENTS, verify)
        .toPromise(); // Payment through paystack
    }

    return;
  }

  async verifyAndCreditPayazaPayment(ref: string) {
    const verify = await this.payazaRepository.getTransactionStatus(ref);

    if (verify.error || !verify.data) {
      throw new BadRequestException('Payment was not successful');
    }

    if (verify.data) {
      const data = verify.data.data;
      const transformedData = {
        transaction_reference: data.transaction_reference,
        transaction_status: data.transaction_status,
        virtual_account_number: data.virtual_account_number,
        transaction_fee: data.transaction_fee,
        amount_received: data.amount_received,
        initiated_date: data.initiated_date,
        current_status_date: data.current_status_date,
        received_from: {
          account_name: data.sender_name,
          account_number: data.sender_account_number,
          bank_name: data.source_bank_name || '',
        },
      };

      await this.brokerTransport
        .send<void>(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYAZA_PAYMENT_RECEIVED, transformedData)
        .toPromise();
    }

    return;
  }

  async linkCustomerToPayment(storeId: string, customerId: string, reference: string) {
    const payment = await this.paymentModel.findOne({ reference });
    if (!payment || getDocId(payment.store) !== storeId) throw new BadRequestException('Invalid payment request');

    const customer = await this.brokerTransport
      .send<Customer>(BROKER_PATTERNS.ORDER.GET_CUSTOMER, {
        _id: getDocId(customerId),
      })
      .toPromise();
    if (customer) {
      payment.customer = {
        email: customer.email,
        id: getDocId(customer),
        name: customer.name,
        phone: customer.phone,
      };
      await payment.save();
      return payment;
    } else throw new BadRequestException('Invalid customer');
  }

  async linkInvoiceToPayment(storeId: string, invoiceId: string, reference: string) {
    const payment = await this.paymentModel.findOne({ reference });
    if (!payment || getDocId(payment.store) !== storeId) throw new BadRequestException('Invalid payment request');

    if (payment.status !== PAYMENT_STATUS.SUCCESS) {
      throw new BadRequestException('This payment was not successful');
    }

    if (payment?.meta?.invoice) {
      throw new BadRequestException('This payment has already been linked to an order/invoice');
    }

    const invoice = await this.brokerTransport
      .send<Invoice>(BROKER_PATTERNS.INVOICE.GET_INVOICE, {
        $or: [{ invoice_id: invoiceId }, { invoiceId: invoiceId }],
      })
      .toPromise();

    if (!invoice) {
      throw new BadRequestException("Couldn't find invoice please contact support");
    }

    // const otherLinkedPayments = await this.paymentModel.find({ 'meta.invoice': invoiceId, status: PAYMENT_STATUS.SUCCESS });

    if (toKobo(invoice.total_amount) > payment.amount) {
      throw new BadRequestException('The amount paid is lesser than the order/invoice amount');
    }

    payment.meta = { ...(payment?.meta ?? {}), invoice: invoiceId };
    payment.customer = {
      email: invoice.receiver?.email,
      id: invoice.receiver?.id,
      name: invoice.receiver?.name,
      phone: invoice?.receiver.phone,
    };

    await payment.save();

    //update order & invoice to paid
    const updatedInvoice = await this.brokerTransport
      .send<Invoice>(BROKER_PATTERNS.INVOICE.INVOICE_PAID, {
        id: invoiceId,
        payment_method: payment.payment_method,
        fromOrder: !!invoice?.order,
      })
      .toPromise();

    return payment;
  }

  async countSubscriptionPayments(userId: string) {
    const data = await this.paymentModel.aggregate([
      {
        $match: {
          type: PAYMENT_TYPES.SUBSCRIPTION,
          status: PAYMENT_STATUS.SUCCESS,
          owner: userId,
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$created_at' },
          },
          totalAmount: { $sum: '$amount' },
        },
      },
      {
        $group: {
          _id: null,
          uniqueDates: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
        },
      },
    ]);

    return {
      count: data[0]?.uniqueDates ?? 0,
      total_amount: (data[0]?.totalAmount ?? 0) / 100,
    };
  }

  async cancelPayment(reference: string) {
    const payment = await this.paymentModel.findOne({ reference });
    if (!payment) {
      throw new UnprocessableEntityException('Invalid payment reference');
    }

    if (payment.status !== PAYMENT_STATUS.PENDING) {
      return payment;
    }

    if (payment?.partner_payment) {
      const partnerPayment = await this.paymentModel.findById(payment?.partner_payment);

      if (partnerPayment && partnerPayment?.payment_method) {
        await this.reverseCreditPayment(partnerPayment);
      }
    }

    if (payment?.meta?.coupon) {
      await this.rollBackPaymentCoupon(payment);

      return;
    }

    payment.status = PAYMENT_STATUS.FAILED;
    payment.save();
    return payment;
  }

  async getAllPayments(data: mongoose.FilterQuery<PaymentDocument>) {
    return this.paymentModel.find(data).sort({ created_at: -1 });
  }

  async storesOnPaidPlan() {
    const paidPlans = await this.brokerTransport
      .send<Plan[]>(BROKER_PATTERNS.PLAN.GET_PLANS, {
        type: { $ne: PLAN_TYPE.STARTER },
      })
      .toPromise();

    const paidPlansIds = paidPlans.map((p) => p.id);

    return await this.brokerTransport
      .send<number>(BROKER_PATTERNS.PAYMENT.COUNT_SUBSCRIPTION_DOCUMENTS, {
        plan: { $in: paidPlansIds },
      })
      .toPromise();
  }

  async checkUserCards(user: string) {
    const cards = await this.cardModel.find({
      user,
    });

    return {
      user_has_cards: cards.length > 0,
    };
  }

  async getAnalytics(storeId, query: GetPaymentAnalyticsDto): Promise<{ currencies: string[]; groupedData: {} }> {
    const { from: start_date, to: end_date, status } = query;
    const matchConditions: any = {
      $or: [
        { type: PAYMENT_TYPES.INVOICE },
        { type: PAYMENT_TYPES.WALLET, payment_method: PAYMENT_METHODS.DIRECT_TRANSFER },
      ],
      status: status ? status : PAYMENT_STATUS.SUCCESS,
    };
    if (storeId) matchConditions['store'] = storeId;
    if (start_date) matchConditions['created_at'] = { $gte: new Date(start_date) };
    if (end_date) {
      if (!matchConditions['created_at']) {
        matchConditions['created_at'] = {};
      }
      matchConditions['created_at']['$lte'] = new Date(end_date);
    }

    const payments = await this.paymentModel.find(matchConditions).exec();

    const groupedData = {};
    const currencies = new Set<string>();
    let total_payments = 0;

    payments.forEach((payment) => {
      const currency = payment.currency || 'UNKNOWN';
      currencies.add(currency);

      if (!groupedData[currency]) {
        groupedData[currency] = {
          total_volume: 0,
          total_payments: 0,
          average_daily_volume: 0,
          // total_payment_requests: 0,
          payments: { volumes: [], count: [] },
          days: [],
        };
      }

      const data = groupedData[currency];
      const date = payment.created_at.toISOString().split('T')[0];
      if (!data.days.includes(date)) {
        data.days.push(date);
      }

      // data.daily_volumes[date].volume += payment.amount;
      // data.daily_volumes[date].transaction_count += 1;

      data.payments.volumes.push({ time: payment.created_at, value: toNaira(payment.amount) });
      data.payments.count.push({ time: payment.created_at, value: payment.reference });

      data.total_volume += payment.amount;
      data.total_payments += 1;
      total_payments += 1;
      // data.total_payment_requests += 1;
    });

    Object.keys(groupedData).forEach((currency) => {
      const data = groupedData[currency];
      const daysCount = data.days.length;
      data.average_daily_volume = daysCount ? data.total_volume / daysCount : 0;
      // data.daily_volumes = Object.entries(data.daily_volumes).map(([date, dailyData]) => ({
      //   date,
      //   ...(dailyData as DailyVolume),
      // }));
    });

    return {
      currencies: Array.from(currencies),
      groupedData,
    };
  }

  async getPaymentHistory(storeId: string, query: PaymentHistoryQueryDto) {
    const { status, from: start_date, to: end_date, search, page = 1, per_page = 10, sort = 'DESC' } = query;

    const filters: any = {
      store: storeId,
      $or: [
        { type: PAYMENT_TYPES.INVOICE },
        { type: PAYMENT_TYPES.WALLET, payment_method: PAYMENT_METHODS.DIRECT_TRANSFER },
      ],
    };

    if (status) {
      filters.status = status;
    }

    if (start_date) filters['created_at'] = { $gte: new Date(start_date) };
    if (end_date) {
      if (!filters['created_at']) {
        filters['created_at'] = {};
      }
      filters['created_at']['$lte'] = new Date(end_date);
    }

    if (search) {
      const conditions = [
        { 'customer.name': { $regex: search, $options: 'i' } },
        { reference: { $regex: search, $options: 'i' } },
      ];

      const searchNumber = Number(search);
      if (!isNaN(searchNumber)) {
        conditions.push({ amount: searchNumber } as any);
      }

      filters.$or = conditions;
    }

    const sortOption = sort === 'ASC' ? 1 : -1;

    const skip = (page - 1) * per_page;

    const [total, payments] = await Promise.all([
      this.paymentModel.countDocuments(filters),
      this.paymentModel.aggregate([
        { $match: filters },
        { $sort: { created_at: sortOption } },
        { $skip: skip },
        { $limit: per_page },
        {
          $lookup: {
            from: 'invoices',
            localField: 'meta.invoice',
            foreignField: 'invoice_id',
            as: 'invoice',
            pipeline: [
              { $limit: 1 }, // Ensure only one invoice is returned per payment
            ],
          },
        },
        {
          $unwind: {
            path: '$invoice',
            preserveNullAndEmptyArrays: true, // Keep payments without an invoice
          },
        },
      ]),
    ]);

    const total_pages = Math.ceil(total / per_page);
    const next_page = page < total_pages ? page + 1 : null;
    const prev_page = page > 1 ? page - 1 : null;

    return {
      data: payments,
      page,
      next_page,
      prev_page,
      total,
      total_pages,
      per_page,
    };
  }

  async getRecentPayments(storeId: string, filter: mongoose.FilterQuery<PaymentDocument>) {
    const payments = await this.paymentModel
      .find({ ...filter, store: storeId })
      .sort({ created_at: -1 })
      .limit(100);

    return payments;
  }

  async getTotalPayments(filter: mongoose.FilterQuery<PaymentDocument>) {
    const result = await this.paymentModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
        },
      },
    ]);

    // Return the total amount, or 0 if no payments match the filter
    return result.length > 0 ? result[0].totalAmount : 0;
  }

  async getStoresWithPayments(startDate: Date, endDate: Date): Promise<string[]> {
    const aggregation = [
      {
        $match: {
          status: 'SUCCESS',
          type: {
            $in: ['INVOICE', 'WALLET'],
          },
          created_at: {
            $gte: startDate,
            $lte: endDate,
          },
        },
      },
      {
        $group: {
          _id: '$store',
          total_payments: {
            $sum: 1,
          },
        },
      },
    ];

    const result = await this.paymentModel.aggregate(aggregation).exec();
    return result.map((store) => store._id);
  }

  async getTotalPaymentsByCurrency(
    filter: mongoose.FilterQuery<PaymentDocument>,
  ): Promise<Partial<Record<CURRENCIES, number>>> {
    const result = await this.paymentModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$currency',
          totalAmount: { $sum: '$amount' },
        },
      },
    ]);

    // Initialize a record with zero values for each currency
    const totalsByCurrency: Partial<Record<CURRENCIES, number>> = {};

    // Populate the totalsByCurrency with actual sums from the aggregation result
    result.forEach((item) => {
      if (item._id) {
        totalsByCurrency[item._id as CURRENCIES] = item.totalAmount;
      }
    });

    return totalsByCurrency;
  }

  async getPaymentCount(filter: mongoose.FilterQuery<PaymentDocument>) {
    const result = await this.paymentModel.countDocuments(filter);
    return result;
  }

  async getSubscriptionPayments(userId: string) {
    const data = await this.paymentModel
      .find({ type: PAYMENT_TYPES.SUBSCRIPTION, status: PAYMENT_STATUS.SUCCESS, owner: userId })
      .sort({ created_at: 1 }); // Sort by created_at in ascending order

    return data;
  }

  async getTotalPaymentsByMethod(storeId: string, query?: GetPaymentAnalyticsDto) {
    const matchConditions: any = {
      status: PAYMENT_STATUS.SUCCESS,
      store: storeId,
    };

    if (query?.from) matchConditions['created_at'] = { $gte: new Date(query.from) };
    if (query?.to) {
      if (!matchConditions['created_at']) {
        matchConditions['created_at'] = {};
      }
      matchConditions['created_at']['$lte'] = new Date(query.to);
    }

    const result = await this.paymentModel.aggregate([
      { $match: matchConditions },
      {
        $group: {
          _id: '$payment_method',
          total_amount: { $sum: '$amount' },
          count: { $sum: 1 },
          currencies: {
            $addToSet: '$currency',
          },
        },
      },
      {
        $project: {
          payment_method: '$_id',
          total_amount: 1,
          count: 1,
          currencies: 1,
          _id: 0,
        },
      },
    ]);

    return result;
  }
}
