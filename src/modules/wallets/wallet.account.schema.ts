import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';
import { CURRENCIES } from '../country/country.schema';

export enum ACCOUNT_PROVIDERS {
  SQUAD = 'SQUADCO',
  PAYAZA = 'PAYAZA',
}

export type AccountDocument = Account & Document;

@Schema({ timestamps: true })
export class Account {
  public id: string;

  @ApiProperty()
  @Prop({ type: String })
  account_name: string;

  @ApiProperty()
  @Prop({ type: String })
  bank_code: string;

  @ApiProperty()
  @Prop({ type: String })
  bank_name: string;

  @ApiProperty()
  @Prop({ type: String })
  account_number: string;

  @ApiProperty()
  @Prop({ type: Boolean })
  is_primary: boolean;

  @ApiProperty()
  @Prop({ type: String, unique: true })
  customer_identifier: string;

  @ApiProperty()
  @Prop({ enum: [ACCOUNT_PROVIDERS.SQUAD, ACCOUNT_PROVIDERS.PAYAZA] })
  provider: ACCOUNT_PROVIDERS;

  @ApiProperty()
  @Prop({ type: String })
  wallet: string;

  @ApiProperty()
  @Prop({ type: String })
  currency: CURRENCIES;
}

export const AccountSchema = SchemaFactory.createForClass(Account);
