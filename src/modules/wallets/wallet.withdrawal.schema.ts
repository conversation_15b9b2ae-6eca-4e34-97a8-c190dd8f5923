import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import mongoose, { Document } from 'mongoose';
import { CURRENCIES } from '../country/country.schema';

export enum WITHDRAWAL_STATUSES {
  INITIATED = 'INITIATED',
  PENDING = 'PENDING',
  SUCCESSFUL = 'SUCCESSFUL',
  FAILED = 'FAILED',
}

export enum WITHDRAWAL_PROVIDERS {
  PAYSTACK = 'PAYSTACK',
  BLOCHQ = 'BLOCHQ',
  SQUAD = 'SQUAD',
  STARTBUTTON = 'STARTBUTTON',
  MANUAL = 'MANUAL',
}

export type WithdrawalRequestDocument = WithdrawalRequest & Document;
export type WithdrawalAccountDocument = WithdrawalAccount & Document;

@Schema({ timestamps: true })
export class WithdrawalAccount {
  public id: string;

  @ApiProperty()
  @Prop({ type: String })
  account_name: string;

  @ApiProperty()
  @Prop({ type: String })
  account_number: string;

  @ApiProperty()
  @Prop({ type: String })
  recipient_id: string;

  @ApiProperty()
  @Prop({ type: String })
  bank_name: string;

  @ApiProperty()
  @Prop({ type: String })
  bank_code: string;

  @ApiProperty()
  @Prop({ type: String })
  wallet: string;

  @ApiProperty()
  @Prop({ type: String })
  squad_code?: string;
}

@Schema({ timestamps: true })
export class WithdrawalRequest {
  public id: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  user: string;

  @ApiProperty()
  @Prop({ type: String })
  token: string;

  @ApiProperty()
  @Prop({ type: String })
  currency: CURRENCIES;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'WithdrawalAccount' })
  withdrawal_account: string | WithdrawalAccount;

  @ApiProperty()
  @Prop({ type: Number })
  amount: number;

  @ApiProperty()
  @Prop({ type: Number })
  partial_debit_amount: number; //used when we need to partially debit a user for a withdrawal if they didn't get debitted originally

  @ApiProperty()
  @Prop({ type: Number })
  fee: number;

  @ApiProperty()
  @Prop({ type: Number })
  gateway_fee: number;

  @ApiProperty()
  @Prop({ type: Number })
  expires_in: number;

  @ApiProperty()
  @Prop({
    enum: [
      WITHDRAWAL_STATUSES.PENDING,
      WITHDRAWAL_STATUSES.SUCCESSFUL,
      WITHDRAWAL_STATUSES.INITIATED,
      WITHDRAWAL_STATUSES.FAILED,
    ],
  })
  status: WITHDRAWAL_STATUSES;

  @ApiProperty()
  @Prop({
    enum: [
      WITHDRAWAL_PROVIDERS.PAYSTACK,
      WITHDRAWAL_PROVIDERS.BLOCHQ,
      WITHDRAWAL_PROVIDERS.SQUAD,
      WITHDRAWAL_PROVIDERS.STARTBUTTON,
      WITHDRAWAL_PROVIDERS.MANUAL,
    ],
  })
  provider: WITHDRAWAL_PROVIDERS;

  @ApiProperty()
  @Prop({ type: String })
  vendorReference: string;

  @ApiProperty()
  @Prop({ type: String })
  nipReference?: string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Wallet' })
  wallet: string;

  createdAt?: Date;
  updatedAt?: Date;
}

export const WithdrawalRequestSchema = SchemaFactory.createForClass(WithdrawalRequest);
export const WithdrawalAccountSchema = SchemaFactory.createForClass(WithdrawalAccount);
