import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  PreconditionFailedException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import md5 from 'md5';
import mongoose, { FilterQuery, Model, PaginateModel } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Store, StoreDocument } from '../store/store.schema';
import { Kyc, KycDocument } from '../store/kyc/kyc.schema';
import { ONBOARDING_STEPS_WITH_REWARDS, User } from '../user/user.schema';
import { chargeWallet } from './utils/chargeWallet';
import {
  Transaction,
  TransactionDocument,
  TransactionMeta,
  TransactionSource,
  TRANSACTION_CHANNELS,
  TRANSACTION_TYPE,
  Wallet,
  WalletDocument,
  PaymentStatement,
  PaymentStatementDocument,
  WalletRequest,
  WalletRequestDocument,
  WALLET_REQUEST_STATUS,
} from './wallet.schema';
import { MonoAccountIds, MonoAccountIdsDocument, MonoTransaction, MonoTransactionDocument } from './wallet.mono.schema';
import {
  WithdrawalAccount,
  WithdrawalAccountDocument,
  WithdrawalRequest,
  WithdrawalRequestDocument,
  WITHDRAWAL_PROVIDERS,
  WITHDRAWAL_STATUSES,
} from './wallet.withdrawal.schema';
import { IPaystackBank, IResolveAccountResponse, PaystackRepository } from '../../repositories/paystack.repository';
import {
  CompleteWithdrawalRequestDto,
  CreatePaymentStatementDto,
  CreateWithdrawalAccountDto,
  ResolveAccountDto,
  WithdrawalRequestDto,
} from '../../models/dtos/wallet.dto';
import {
  formatPhoneNumber,
  generateNumber,
  sluggify,
  toCurrency,
  actionIsAllowed,
  mapPaginateQuery,
  mapPaginatedResponse,
  genChars,
  isValidObjectId,
  removeSpecialCharacters,
} from '../../utils';
import {
  ManuallyCreditWalletDto,
  PaginatedQueryDto,
  SEARCH_TRANSACTION_TYPES,
  SearchTransactionsDto,
  ValidateWithdrawalsDto,
} from './dtos/search.dto';
import { PaymentMethod, PaymentMethodDocument } from '../paymentmethod/paymentmethod.schema';
import { PAYMENT_METHODS, PAYMENT_PROVIDERS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../enums/payment.enum';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCIES } from '../country/country.schema';
import {
  blocWithdrawalFeeCalculator,
  paystackTranferFeeCalculator,
  squadFeeCalculator,
  squadWithdrawalFeeCalculator,
  transactionFeeCalculator,
} from '../../utils/fees';
import bankLogos from '../../utils/bank-logos';
import { freemem } from 'os';
import { Account, ACCOUNT_PROVIDERS, AccountDocument, AccountSchema } from './wallet.account.schema';
import { SquadRepository } from '../../repositories/squad.repository';
import { Payment, PaymentDocument } from '../payment/payment.schema';
import logos from '../../utils/bank-logos';
import { SCOPES, STORE_ROLES } from '../../utils/permissions.util';
import {
  calculateWithdrawalFee,
  defaultWithdrawalProvider,
  limit24Hr,
  maxWithdrawalAmount,
  minWithdrawalAmount,
  withdrawalFeeCalculators,
  withdrawalProviders,
} from '../../utils/withdrawal-config';
import { maskEmail } from '../store/kyc/utils/maskDetails';
import { MailchimpRepository } from '../../repositories/mailchimp.repository';
import { InjectQueue } from '@nestjs/bull';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { Queue } from 'bull';
import { BankTransferPayload, WalletJob } from './wallet.queue';
import { BlocHQRepository } from '../../repositories/bloc.repository';
import { ADMIN_CONFIG, AdminConfig } from '../adminconfig/adminconfig.schema';
import { StoreResponseDto } from '../../models/dtos/StoreDtos';
import dayjs from 'dayjs';
import { BrevoRepository } from '../../repositories/brevo.repository';
import { generatePDFFromWebpage } from '../../utils/generate-pdfs';
import { v4 as uuidV4 } from 'uuid';
import {
  generateOTP,
  getDocId,
  toKobo,
  toNaira,
  delay,
  chunkArray,
  generateCombinedName,
  arrayToSentence,
} from '../../utils/functions';
import { Mutex } from 'async-mutex';
import { ConfigService } from '@nestjs/config';
import { ApiGuardConfig } from '../../config/types/api-guard.config';
import { squadBanks } from '../../utils/squad-banks';
import {
  currenciesWithResolveAccount,
  DEFAULT_CURRENCY_MARKUP,
  FREE_PAYMENT_THRESHOLD,
  WALLET_LIMITS,
} from '../../utils/constants';
import { PayazaRepository } from '../../repositories/payaza.repository';
import { CreateInternationalWalletRequestDto } from './dtos/wallet-request.dto';
import { UpdateWalletRequestDto } from './dtos/update-wallet-request.dto';
import { StartbuttonRepository } from '../../repositories/startbutton.repository';
import { CustomerIoRepository } from '../../repositories/customer-io.repository';
import { ResendRepository } from '../../repositories/resend.repository';
import { SlackRepository } from '../../repositories/slack.respository';

const SKIP_ACCOUNT_CREATION = false;

export interface WalletWithAccounts extends Wallet {
  accounts: Account[];
}

@Injectable()
export class WalletService {
  private mutex = new Mutex();

  constructor(
    public readonly logger: Logger,
    public readonly paystack: PaystackRepository,
    public readonly startbutton: StartbuttonRepository,
    public readonly blochq: BlocHQRepository,
    public readonly squad: SquadRepository,
    public readonly payaza: PayazaRepository,
    private readonly brevo: BrevoRepository,
    private readonly customerIo: CustomerIoRepository,
    public readonly resend: ResendRepository,
    public readonly brokerTransport: BrokerTransportService,
    @InjectModel(WithdrawalAccount.name)
    public readonly withdrawalAccountModel: Model<WithdrawalAccountDocument>,
    @InjectModel(WithdrawalRequest.name)
    public readonly withdrawalRequestModel: Model<WithdrawalRequestDocument>,
    @InjectModel(Account.name)
    public readonly walletAccount: Model<AccountDocument>,

    @InjectQueue(QUEUES.WALLET)
    public readonly walletQueue: Queue<WalletJob>,

    @InjectModel(Wallet.name)
    public readonly walletModel: PaginateModel<WalletDocument>,
    @InjectModel(WalletRequest.name)
    public readonly walletRequestModel: PaginateModel<WalletRequestDocument>,
    @InjectModel(Transaction.name)
    public readonly transactionModel: PaginateModel<TransactionDocument>,
    @InjectModel(PaymentStatement.name)
    protected readonly paymentStatementModel: PaginateModel<PaymentStatementDocument>,
    @InjectConnection()
    protected readonly connection: mongoose.Connection,
    private config: ConfigService,
    private readonly slack: SlackRepository,
  ) {}

  async initializeWallet(storeId: string) {
    let wallet: WalletDocument;
    const store = await this.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) return { error: 'Store not found' };

    wallet = new this.walletModel({
      store: getDocId(store),
      limits: WALLET_LIMITS.BEFORE_KYC[store.currencies.default],
      has_completed_kyc: false,
      currency: store.currencies.default,
    });
    await wallet.save();

    const methods = await this.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {})
      .toPromise();

    store.wallet = wallet._id;
    store.wallets = [...(store?.wallets ?? []), { id: getDocId(wallet), currency: wallet.currency }];
    store.payments_enabled = true;
    store.kyc_approved = false;
    store.configuration.direct_checkout_enabled = true;
    store.payment_options = {
      [wallet.currency]: methods
        .filter((m) => m.currencies.includes(wallet.currency) && m.payment_types.includes(PAYMENT_TYPES.INVOICE))
        .map((m) => ({
          type: m.type,
          enabled: m.type === PAYMENT_METHODS.ZILLA ? false : true,
        })),
    };

    await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
        filter: { _id: store.id },
        update: store,
      })
      .toPromise();

    return wallet;
  }

  async createWallet(storeId: string, isAdmin: boolean) {
    let wallet: WalletDocument;
    let squadAccount: AccountDocument | null = null;
    let payazaAccount: AccountDocument | null = null;
    const errors: string[] = [];

    // Fetch the store
    const store = await this.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    // Fetch KYC information
    const kyc = await this.brokerTransport
      .send<Kyc>(BROKER_PATTERNS.KYC.GET_KYC, { _id: store.kyc })
      .toPromise();

    // Validate store and KYC data
    if (!kyc) {
      throw new BadRequestException('KYC info not found! Please reload page');
    }

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    if (kyc.verification_method === 'MANUAL' && !isAdmin) {
      throw new BadRequestException('Unable to create wallet at the moment');
    }

    // Fetch the user
    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    if (!user) {
      throw new BadRequestException('User does not exist');
    }

    // Create or fetch the wallet
    if (store.wallet) {
      wallet = await this.walletModel.findOne({ _id: store.wallet });
    } else {
      wallet = new this.walletModel({ store: store.id });
      wallet.currency = store.currencies.default;
      wallet.accounts = []; // Initialize accounts array
      await wallet.save();

      store.wallet = wallet._id;
      store.wallets = [...(store?.wallets ?? []), { id: getDocId(wallet), currency: wallet.currency }];
      await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
          filter: { _id: store.id },
          update: store,
        })
        .toPromise();
    }

    // Generate accounts if the country is Nigeria
    if (kyc.country === COUNTRY_CODE.NG && !SKIP_ACCOUNT_CREATION) {
      // Fetch existing accounts for the wallet
      const existingAccounts = await this.walletAccount.find({ wallet: wallet._id });

      // Check for existing Squad account
      squadAccount = existingAccounts.find((acc) => acc.provider === ACCOUNT_PROVIDERS.SQUAD);

      // Check for existing Payaza account
      payazaAccount = existingAccounts.find((acc) => acc.provider === ACCOUNT_PROVIDERS.PAYAZA);

      // Create Squad account if it doesn't exist
      if (!squadAccount?.account_number) {
        squadAccount = new this.walletAccount({
          customer_identifier: sluggify(user.name) + '-' + Date.now(),
          wallet: wallet._id,
          provider: ACCOUNT_PROVIDERS.SQUAD,
        });
        await squadAccount.save();
        wallet.accounts.push({ id: getDocId(squadAccount), provider: ACCOUNT_PROVIDERS.SQUAD });
      }

      // Create Payaza account if it doesn't exist
      if (!payazaAccount?.account_number) {
        payazaAccount = new this.walletAccount({
          customer_identifier: sluggify(user.name) + '-' + Date.now(),
          wallet: wallet._id,
          provider: ACCOUNT_PROVIDERS.PAYAZA,
        });
        await payazaAccount.save();
        wallet.accounts.push({ id: getDocId(payazaAccount), provider: ACCOUNT_PROVIDERS.PAYAZA });
      }

      // Save wallet with updated accounts array
      await wallet.save();

      // Ensure wallet exists
      if (!wallet) {
        throw new BadRequestException("Couldn't find wallet! Contact support");
      }

      // Create virtual account with Squad
      if (!squadAccount.account_number || !squadAccount.bank_code) {
        try {
          const squadAccountData = await this.squad.createBusinessAccount({
            name: kyc.first_name,
            store_name: store.name,
            phone: kyc.phone,
            bvn: kyc.bvn,
            customer_identifier: squadAccount.customer_identifier,
          });

          if (squadAccountData.error || !squadAccountData.data) {
            throw new Error(squadAccountData.error || 'Unknown error occurred during Squad account creation');
          }

          squadAccount.account_number = squadAccountData.data.virtual_account_number;
          squadAccount.customer_identifier = squadAccountData.data.customer_identifier;
          squadAccount.bank_code = squadAccountData.data.bank_code;
          squadAccount.account_name = `${squadAccountData.data.first_name} ${squadAccountData.data.last_name}`;
          squadAccount.bank_name = 'Guaranty Trust Bank';

          await squadAccount.save();
        } catch (error) {
          console.error(`Squad account creation failed for store ${store.name}:`, error);
          errors.push(`Squad account creation failed: ${error.message || error}`);
          // Proceed to create Payaza account even if Squad account creation fails
          squadAccount = null; // Reset to null if creation failed
        }
      }

      // Create virtual account with Payaza
      if (!payazaAccount.account_number || !payazaAccount.bank_code) {
        try {
          const fullName = generateCombinedName(store.name, kyc.first_name);
          const firstName = fullName.split(' by ')[0];
          const lastName = fullName.split(' by ')[1];

          const payazaResponse = await this.payaza.createReservedAccountForCustomers({
            customer_first_name: kyc.first_name,
            customer_last_name: kyc.last_name,
            customer_email: user.email,
            customer_phone_number: user.phone,
            bank_code: '117',
            account_name: removeSpecialCharacters(fullName),
            account_reference: sluggify(user.name) + '-' + Date.now(),
            account_type: 'Static',
            bvn: kyc.bvn,
          });

          if (payazaResponse.error || !payazaResponse.data) {
            throw new Error(payazaResponse.error || 'Unknown error occurred during Payaza account creation');
          }

          const accountData = payazaResponse.data.data;

          payazaAccount.account_name = accountData.account_name;
          payazaAccount.bank_code = '117';
          payazaAccount.bank_name = accountData.bank_name;
          payazaAccount.account_number = accountData.account_number;

          await payazaAccount.save();
        } catch (error) {
          console.error(`Payaza account creation failed for store ${store.name}:`, error);
          errors.push(`Payaza account creation failed: ${error.message || error}`);
          payazaAccount = null; // Reset to null if creation failed
        }
      }
    }

    // Update KYC status
    kyc.status = 'APPROVED';
    kyc.approved_at = new Date();
    await this.brokerTransport
      .send<KycDocument>(BROKER_PATTERNS.KYC.UPDATE_KYC, {
        filter: { _id: store.kyc },
        data: kyc,
      })
      .toPromise();

    if (kyc.stores.length === 1 && !user?.onboarding_rewards?.kyc_credits_earned) {
      //GIVE USER CREDITS FOR FINISHING KYC
      await this.brokerTransport
        .send(BROKER_PATTERNS.USER.CREDITS.ADD_CREDITS_FOR_ONBOARDING_STEPS, {
          step: ONBOARDING_STEPS_WITH_REWARDS.COMPLETE_KYC,
          userId: getDocId(store.owner),
        })
        .toPromise();
    }

    // Fetch payment methods
    const methods = await this.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {})
      .toPromise();

    wallet.has_completed_kyc = true;
    wallet.limits = WALLET_LIMITS.AFTER_KYC[COUNTRY_CURRENCY_MAP[store.country as COUNTRY_CODE]];
    await wallet.save();

    // Update store payment options
    store.payments_enabled = true;
    store.kyc_approved = true;
    store.payment_options[wallet.currency] = methods
      .filter((m) => m.currencies.includes(wallet.currency) && m.payment_types.includes(PAYMENT_TYPES.INVOICE))
      .map((m) => ({
        type: m.type,
        enabled: m.type === PAYMENT_METHODS.ZILLA ? false : true,
      }));

    await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
        filter: { _id: store.id },
        update: store,
      })
      .toPromise();

    // Update KYC status via Brevo
    try {
      this.brevo.kycStatusUpdate(user.email, 'APPROVED');
    } catch (error) {
      this.logger.error(error);
    }

    // Send activation email
    if (kyc.country !== COUNTRY_CODE.NG) {
      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.PAYMENTS_ACTIVATED_GH, {
        to: user.email,
        subject: 'Your KYC has been approved! 🎉',
        data: {
          name: user.name,
          store_name: store.name,
          app_link: process.env.CATLOG_APP,
        },
      });
    } else {
      // Prepare account data for email
      const accountData = { squad: null, payaza: null };

      if (squadAccount) {
        accountData.squad = {
          account_name: squadAccount.account_name,
          bank_name: squadAccount.bank_name,
          bank_logo: bankLogos[squadAccount.bank_code] ?? null,
          account_number: squadAccount.account_number,
        };
      }

      if (payazaAccount) {
        accountData.payaza = {
          account_name: payazaAccount.account_name,
          bank_name: payazaAccount.bank_name,
          bank_logo: bankLogos[payazaAccount.bank_code] ?? null,
          account_number: payazaAccount.account_number,
        };
      }

      await this.customerIo.createOrUpdateUser({
        id: getDocId(user),
        email: user?.email,
        kyc_status: 'APPROVED',
        bank_account_created: !!accountData?.payaza || !!accountData?.squad,
      });

      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.PAYMENTS_ACTIVATED, {
        to: user.email,
        subject: 'Your KYC has been approved! 🎉',
        data: {
          name: user.name,
          store_name: store.name,
          app_link: process.env.CATLOG_APP,
          ...(accountData?.payaza ? accountData.payaza : accountData?.squad ? accountData?.squad : {}),
        },
      });
    }

    // Return the result with available accounts and any errors
    return {
      kyc,
      accounts: [squadAccount, payazaAccount].filter(Boolean).map((account) => ({
        ...account.toJSON(),
        image:
          logos[account.bank_code] ??
          'https://res.cloudinary.com/catlog/image/upload/v1667571113/bank-logos/default.svg',
      })),
      errors,
      store,
    };
  }

  async createSquadAccounts() {
    const wallets = await this.walletModel.find({
      currency: CURRENCIES.NGN,
      has_completed_kyc: true,
      account: { $exists: false },
    });
    const chunkedWallets = chunkArray(wallets, 20);

    const accounts = [];

    for (let index = 0; index < chunkedWallets.length; index++) {
      const walletChunk = chunkedWallets[index];

      const createdAccounts = await Promise.all(
        walletChunk.map(async (wallet) => {
          const store = await this.brokerTransport
            .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: wallet.store })
            .toPromise();

          const user = await this.brokerTransport
            .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
            .toPromise();

          // FETCH NEEDED EXISTING DATA

          let kyc = await this.brokerTransport
            .send<Kyc>(BROKER_PATTERNS.KYC.GET_KYC, { _id: store.kyc })
            .toPromise();

          const account = new this.walletAccount({
            customer_identifier: sluggify(`${kyc.first_name} ${kyc.last_name}`) + '-' + Date.now(),
            wallet: wallet._id,
          });
          await account.save();

          const squadAccount = await this.squad.createBusinessAccount({
            name: kyc.first_name,
            store_name: store.name,
            phone: kyc.phone,
            bvn: kyc.bvn,
            customer_identifier: account.customer_identifier,
          });

          if (squadAccount.error || !squadAccount) {
            //error quietly
            console.log(`COULDN'T CREATE ACCOUNT FOR ${store.name}: Wallet ID: ${getDocId(wallet)}`);
            return null;
          }

          account.account_number = squadAccount.data.virtual_account_number;
          account.customer_identifier = squadAccount.data.customer_identifier;
          account.bank_code = squadAccount.data.bank_code;
          account.account_name = squadAccount.data.first_name + ' ' + squadAccount.data.last_name;
          account.bank_name = 'Guaranty Trust Bank';

          await account.save();

          wallet.account = account._id;
          await wallet.save();

          console.log(`Sending account created email to ${user?.email}`);
          await this.resend.sendEmail(BROKER_PATTERNS.MAIL.BUSINESS_ACCOUNT_CREATED, {
            to: user.email,
            subject: 'Your business account is here! 🎉',
            data: {
              name: user.name,
              store_name: store.name,
              account_name: account?.account_name ?? null,
              bank_name: account?.bank_name ?? null,
              bank_logo: bankLogos[account?.bank_code] ?? null,
              account_number: account?.account_number ?? null,
              app_link: process.env.CATLOG_APP,
            },
          });

          return account;
        }),
      );

      accounts.push(...createdAccounts);

      await delay(30000);
    }

    return accounts;
  }

  async createPayazaAccounts() {
    const wallets = await this.walletModel.find({
      currency: CURRENCIES.NGN,
      has_completed_kyc: true,
      auto_create_account: { $ne: false },
      $expr: {
        $lt: [
          {
            $size: {
              $filter: {
                input: '$accounts',
                as: 'account',
                cond: { $eq: ['$$account.provider', ACCOUNT_PROVIDERS.PAYAZA] },
              },
            },
          },
          2, // Ensure the number of PAYAZA accounts is less than 2 (i.e., 0 or 1)
        ],
      },
    });

    const chunkedWallets = chunkArray(wallets, 20);

    const accounts = [];
    const failedWallets = [];

    for (let index = 0; index < chunkedWallets.length; index++) {
      const walletChunk = chunkedWallets[index];

      for (const wallet of walletChunk) {
        try {
          const account = await this.processWalletWithRetry(wallet);
          accounts.push(account);
          // Delay after each wallet to respect rate limits
          await delay(5000);
        } catch (error) {
          console.error(`Failed to process wallet ID ${wallet._id}:`, error);
          failedWallets.push(wallet._id);
        }
      }

      // Delay between chunks
      await delay(5000);
    }

    if (failedWallets.length) {
      console.warn(`Failed to create accounts for the following wallet IDs: ${failedWallets.join(', ')}`);
    }

    return { accounts_created: accounts.length, failedWallets };
  }

  async processWalletWithRetry(wallet, retries = 3) {
    try {
      return await this.processWallet(wallet);
    } catch (error) {
      if (retries > 0) {
        await delay(5000);
        return await this.processWalletWithRetry(wallet, retries - 1);
      } else {
        throw error;
      }
    }
  }

  async processWallet(wallet) {
    try {
      // Fetch related store and user
      const store = await this.brokerTransport
        .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: wallet.store })
        .toPromise();

      const user = await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
        .toPromise();

      const kyc = await this.brokerTransport
        .send<Kyc>(BROKER_PATTERNS.KYC.GET_KYC, { _id: store.kyc })
        .toPromise();

      // Generate full name for the account
      const fullName = generateCombinedName(store.name, kyc.first_name);
      const [firstName, lastName] = fullName.split(' by ');

      // Create Payaza account
      const payazaResponse = await this.payaza.createReservedAccountForCustomers({
        customer_first_name: kyc.first_name,
        customer_last_name: kyc.last_name,
        customer_email: user.email,
        customer_phone_number: user.phone,
        bank_code: '117',
        account_name: removeSpecialCharacters(fullName),
        account_reference: sluggify(user.name) + '-' + Date.now(),
        account_type: 'Static',
        bvn: kyc.bvn,
      });

      if (payazaResponse.error || !payazaResponse.data) {
        console.log(`Failed to create Payaza account for store ${store.name}`);
        return null;
      }

      // Extract account data from Payaza response
      const accountData = payazaResponse.data.data;

      // Create a new wallet account instance
      const account = new this.walletAccount({
        account_name: fullName,
        bank_code: '117',
        bank_name: accountData.bank_name,
        account_number: accountData.account_number,
        customer_identifier: accountData.account_reference,
        provider: ACCOUNT_PROVIDERS.PAYAZA,
        wallet: wallet._id,
      });

      // Save the new account to the database
      await account.save();

      // Update wallet's accounts array
      const walletAccountData = {
        id: getDocId(account),
        provider: ACCOUNT_PROVIDERS.PAYAZA,
      };
      wallet.accounts = wallet.accounts ? [...wallet.accounts, walletAccountData] : [walletAccountData];
      await wallet.save();

      // Send notification email to the user
      console.log(`Sending account created email to ${user.email}`);
      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.BUSINESS_ACCOUNT_CREATED, {
        to: user.email,
        subject: 'Your new Business account is ready!',
        data: {
          name: user.name,
          store_name: store.name,
          account_name: account.account_name,
          bank_name: account.bank_name,
          bank_logo: bankLogos[account.bank_code] ?? null,
          account_number: account.account_number,
          app_link: process.env.CATLOG_APP,
        },
      });

      // Return the created account
      return account;
    } catch (error) {
      console.error(`Error processing wallet ID ${wallet._id}:`, error);
      // Re-throw the error to be handled by the calling function (e.g., retry logic)
      throw error;
    }
  }

  async createReservedPayazaAccount(storeId: string, businessName?: string, name?: string) {
    let payazaAccount: AccountDocument | null = null;
    const errors: string[] = [];

    // Fetch the store
    const store = await this.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    const kyc = await this.brokerTransport
      .send<Kyc>(BROKER_PATTERNS.KYC.GET_KYC, { _id: store.kyc })
      .toPromise();

    // Fetch the user
    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    if (!user) {
      throw new BadRequestException('User does not exist');
    }

    // Fetch or create the wallet
    const wallet = await this.walletModel.findOne({ _id: store.wallet });
    if (!wallet) {
      throw new BadRequestException('Wallet does not exist for the store');
    }

    // Fetch existing accounts for the wallet
    const existingAccounts = await this.walletAccount.find({ wallet: wallet._id });

    try {
      // Generate full name for the account
      const fullName = generateCombinedName(businessName, name);
      const [firstName, lastName] = fullName.split(' by ');
      const payazaResponse = await this.payaza.createReservedAccountForCustomers({
        customer_first_name: kyc.first_name,
        customer_last_name: kyc.last_name,
        customer_email: user.email,
        customer_phone_number: user.phone,
        bank_code: '117',
        account_name: removeSpecialCharacters(fullName),
        account_reference: sluggify(user.name) + '-' + Date.now(),
        account_type: 'Static',
        bvn: kyc.bvn,
      });

      if (payazaResponse.error || !payazaResponse.data) {
        throw new Error(payazaResponse.error || 'Unknown error occurred during Payaza account creation');
      }

      const accountData = payazaResponse.data.data;

      payazaAccount = new this.walletAccount({
        account_name: accountData.account_name,
        bank_code: '117',
        bank_name: accountData.bank_name,
        account_number: accountData.account_number,
        customer_identifier: accountData.account_reference,
        wallet: wallet._id,
        provider: ACCOUNT_PROVIDERS.PAYAZA,
      });

      await payazaAccount.save();

      // Add the new account to the wallet
      wallet.accounts.push({ id: getDocId(payazaAccount), provider: ACCOUNT_PROVIDERS.PAYAZA });
      await wallet.save();
    } catch (error) {
      console.error(`Payaza account creation failed for store ${store.name}:`, error);
      errors.push(`Payaza account creation failed: ${error.message || error}`);
      payazaAccount = null; // Reset to null if creation failed
    }

    // Return the result with the new account and any errors
    return {
      account: payazaAccount ? payazaAccount.toJSON() : null,
      errors,
    };
  }

  async createNewAccount(storeId: string, businessName?: string, name?: string) {
    let kyc = await this.brokerTransport
      .send<Kyc>(BROKER_PATTERNS.KYC.GET_KYC, { stores: { $in: [storeId] } })
      .toPromise();

    if (!kyc || kyc.status !== 'APPROVED') {
      throw new BadRequestException('Kyc not approved');
    }

    const wallet = await this.walletModel.findOne({ store: storeId });

    if (!wallet) {
      throw new BadRequestException('Wallet not found');
    }

    const store = await this.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: wallet.store })
      .toPromise();

    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    const account = new this.walletAccount({
      customer_identifier: sluggify(`${kyc.first_name} ${kyc.last_name}`) + '-' + Date.now(),
      wallet: wallet._id,
    });
    await account.save();

    const squadAccount = await this.squad.createBusinessAccount({
      name: name ?? '',
      store_name: businessName ?? store.name,
      phone: kyc.phone,
      bvn: kyc.bvn,
      customer_identifier: account.customer_identifier,
    });

    if (squadAccount.error || !squadAccount) {
      //error quietly
      console.log(`COULDN'T CREATE ACCOUNT FOR ${store.name}: Wallet ID: ${getDocId(wallet)}`);
      return null;
    }

    account.account_number = squadAccount.data.virtual_account_number;
    account.customer_identifier = squadAccount.data.customer_identifier;
    account.bank_code = squadAccount.data.bank_code;
    account.account_name = squadAccount.data.first_name + ' ' + squadAccount.data.last_name;
    account.bank_name = 'Guaranty Trust Bank';

    await account.save();

    wallet.account = account._id;
    wallet.accounts = [...(wallet?.accounts ?? []), { id: account._id, provider: ACCOUNT_PROVIDERS.SQUAD }];

    await wallet.save();

    console.log(`Sending account created email to ${user?.email}`);
    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.BUSINESS_ACCOUNT_CREATED, {
      to: user.email,
      subject: 'Your business account is here! 🎉',
      data: {
        name: user.name,
        store_name: store.name,
        account_name: account?.account_name ?? null,
        bank_name: account?.bank_name ?? null,
        bank_logo: bankLogos[account?.bank_code] ?? null,
        account_number: account?.account_number ?? null,
        app_link: process.env.CATLOG_APP,
      },
    });

    return account;
  }

  async getAccounts(walletId: string) {
    return await this.walletAccount.find({ wallet: walletId, account_number: { $exists: true } });
  }

  //used in public endpoints
  async getWalletByStoreId(storeId: string) {
    // Fetch the store
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      return null;
    }

    // Fetch the wallet associated with the store
    const wallet = await this.walletModel.findOne({ _id: store.wallet }).select('-meta_ids');

    if (!wallet) {
      return null;
    }

    // Fetch all accounts associated with the wallet
    const accounts = await this.walletAccount.find({ _id: { $in: wallet.accounts.map((acc) => acc.id) } });

    // Convert wallet to JSON
    const walletJson = wallet.toJSON();

    if (store.country === COUNTRY_CODE.NG) {
      // Attach accounts to the wallet JSON
      walletJson.accounts = accounts.map((account) => account.toJSON());
    }

    return walletJson;
  }

  async getWallets(filter: any) {
    return await this.walletModel.find(filter);
  }

  //used in dashboard endpoints
  async getWalletFromStore(storeId: string, userRole: STORE_ROLES) {
    const canViewBalance = actionIsAllowed({
      userRole,
      permission: SCOPES.WALLETS.VIEW_BALANCE,
    });

    // Fetch the store
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    // Fetch KYC information
    const kyc = await this.brokerTransport
      .send<Kyc>(BROKER_PATTERNS.KYC.GET_KYC, { stores: { $in: [store.id] } })
      .toPromise();

    // Initialize data object with KYC status
    let data: any = {
      kyc_status: kyc?.status ?? 'NO_KYC',
    };

    // Fetch the wallet associated with the store
    const wallet = await this.walletModel.findOne({ _id: store.wallet }).select('-meta_ids');

    if (!wallet) {
      data = {
        ...data,
        store: storeId,
        limits: WALLET_LIMITS.BEFORE_KYC[store.currencies.default],
        has_completed_kyc: false,
        currency: store.currencies.default,
      };
    }

    if (wallet) {
      const walletJson = wallet.toJSON();

      // Fetch all accounts associated with the wallet
      const accounts = await this.walletAccount.find({
        _id: { $in: wallet.accounts.map((a) => a.id) },
        account_number: { $exists: true },
      });

      if (accounts && accounts.length > 0) {
        walletJson.accounts = accounts.map((account) => account.toJSON());
      } else {
        walletJson.accounts = [];
      }

      // Adjust balance visibility based on user permissions
      walletJson.balance = canViewBalance ? walletJson.balance : 0;

      // Merge wallet data into the response data
      data = { ...data, ...walletJson };
    }

    return data;
  }

  async getWalletsFromStore(storeId: string, userRole: STORE_ROLES) {
    const canViewBalance = actionIsAllowed({
      userRole,
      permission: SCOPES.WALLETS.VIEW_BALANCE,
    });

    // Fetch the store
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    // Fetch KYC information
    const kyc = await this.brokerTransport
      .send<Kyc>(BROKER_PATTERNS.KYC.GET_KYC, { stores: { $in: [store.id] } })
      .toPromise();

    // Initialize data with KYC status
    let data: any = {
      kyc_status: kyc?.status ?? 'NO_KYC',
      wallets: [],
    };

    // Fetch all wallets associated with the store
    const wallets = await this.walletModel.find({ store: storeId }).select('-meta_ids');

    // If no wallets exist, set default information
    if (!wallets || wallets.length === 0) {
      data.wallets.push({
        store: storeId,
        limits: WALLET_LIMITS.BEFORE_KYC[store.currencies.default],
        has_completed_kyc: false,
        currency: store.currencies.default,
      });
    } else {
      // Process each wallet
      for (const wallet of wallets) {
        const walletJson = wallet.toJSON();

        // Fetch all accounts associated with each wallet
        const accounts = await this.walletAccount.find({
          _id: { $in: wallet.accounts.map((a) => a.id) },
          account_number: { $exists: true },
        });

        walletJson.accounts = accounts && accounts.length > 0 ? accounts.map((a) => a.toJSON()) : [];

        // Adjust balance visibility based on user permissions
        walletJson.balance = canViewBalance ? walletJson.balance : 0;

        // Add wallet data to the response
        data.wallets.push(walletJson);
      }
    }

    return data;
  }

  async getBalance(storeId: string) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    const wallet = await this.walletModel.findOne({ _id: store.wallet }).select('balance');

    return { balance: wallet.balance };
  }

  async getWithdrawalAccountsFromStore(storeId: string, currency: CURRENCIES) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    const currencyWallet = store.wallets.find((w) => w.currency === currency);

    if (!currencyWallet) throw new BadRequestException('Currency wallet does not exist');
    const withdrawalAccounts = await this.withdrawalAccountModel.find({ wallet: currencyWallet.id });

    return withdrawalAccounts.map((w) => w.toJSON()) as WithdrawalAccount[]; //converting to json for controller to add logo images
  }

  async getBanks(storeId: string, currency: CURRENCIES) {
    const wallet = await this.walletModel.findOne({ store: storeId });

    if (!wallet) {
      throw new NotFoundException('Wallet not found!');
    }

    const banks = await this.paystack.getBanks(currency);

    return banks;
  }

  async deleteWithdrawalAccountFromStore(storeId: string, withdrawalAccountId: string) {
    const withdrawalAccount = await this.withdrawalAccountModel.findById(withdrawalAccountId);

    if (!withdrawalAccount) throw new BadRequestException('This account might have been deleted already');

    const wallet = await this.walletModel.findById(withdrawalAccount.wallet);

    if (!wallet) throw new BadRequestException('Wallet does not exist');

    if (String(wallet.store) !== storeId) throw new BadRequestException('Wallet does not belong to this store');

    if (!wallet.withdrawal_accounts.includes(withdrawalAccountId))
      throw new BadRequestException('You cannot delete this withdrawal account');

    await withdrawalAccount.remove();

    wallet.withdrawal_accounts = wallet.withdrawal_accounts.filter((a) => a != withdrawalAccountId);

    await wallet.save();

    return withdrawalAccount.toJSON() as WithdrawalAccount;
  }

  async getWalletTransactionsFromStore(
    storeId: string,
    walletId: string,
    filter: SearchTransactionsDto,
    pagination: PaginatedQueryDto,
    sort: any = { createdAt: -1 },
  ) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    const wallet = await this.walletModel.findOne({ _id: walletId });

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    if (!wallet || String(wallet.store) !== storeId) {
      throw new BadRequestException('Wallet does not exist');
    }

    const compoundFilter: any = {};

    if (filter?.from) {
      compoundFilter.createdAt = {
        $gte: new Date(filter.from),
        $lte: new Date(filter.to),
      };
    }

    if (filter?.search) {
      compoundFilter.narration = { $regex: filter.search, $options: 'i' };
    }

    if (filter?.type && filter.type !== 'all') {
      compoundFilter.type = filter.type;
    }

    const transactions = await this.transactionModel.paginate(
      {
        ...compoundFilter,
        wallet: walletId,
      },
      {
        sort,
        page: pagination.page || 1,
        limit: pagination.per_page || 50,
        lean: true,
        populate: {
          path: 'meta.withdrawal_request',
          model: 'WithdrawalRequest',
          populate: {
            path: 'withdrawal_account',
          },
        },
      },
    );

    return {
      data: {
        store: storeId,
        transactions: transactions.docs,
      },
      page: transactions.page,
      next_page: transactions.nextPage,
      prev_page: transactions.prevPage,
      total: transactions.totalDocs,
      total_pages: transactions.totalPages,
      per_page: transactions.limit,
    };
  }

  async getMilestones(wallet_id: string) {
    const result = await this.transactionModel
      .aggregate([
        {
          $match: {
            type: 'credit',
            $or: [{ wallet: wallet_id }, { wallet: mongoose.Types.ObjectId(wallet_id) }],
          },
        },
        {
          $group: {
            _id: null,
            total: {
              $sum: '$amount',
            },
          },
        },
      ])
      .exec();

    const volume = result[0]?.total as number;

    return {
      volume: volume ?? 0,
    };
  }

  async getTransaction(transactionId: string, storeId: string) {
    const transaction = await this.transactionModel.findById(transactionId).populate({
      path: 'meta.withdrawal_request',
      model: 'WithdrawalRequest',
      populate: {
        path: 'withdrawal_account',
        model: 'WithdrawalAccount',
      },
    });
    if (!transaction) throw new BadRequestException('Transaction does not exist');

    const wallet = await this.walletModel.findById(transaction.wallet);

    if (!wallet) throw new BadRequestException('Wallet does not exist');

    if (String(wallet.store) !== storeId) throw new BadRequestException('Transaction does not belong to this store');

    let payment = null;
    let withdrawal_account: WithdrawalAccount = null;

    if (transaction.meta.payment_id) {
      payment = await this.brokerTransport
        .send<Payment>(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, {
          _id: transaction.meta.payment_id,
        })
        .toPromise();
    }

    const txData = transaction.toJSON();
    const { meta, ...rest } = txData;

    if (meta.withdrawal_request?.withdrawal_account) {
      withdrawal_account = meta.withdrawal_request.withdrawal_account;
      withdrawal_account['image'] =
        bankLogos[withdrawal_account.bank_code] ??
        'https://res.cloudinary.com/catlog/image/upload/v1667571113/bank-logos/default.svg';
    }

    return {
      ...rest,
      payment,
      withdrawal_account,
    };
  }

  async createWithdrawalAccount(storeId: string, account: CreateWithdrawalAccountDto) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: storeId,
      })
      .toPromise();

    const walletId = store?.wallets.find((w) => w.currency === account.currency)?.id;

    if (!walletId) throw new BadRequestException('No wallet found for this currency');

    const wallet = await this.walletModel.findOne({ _id: walletId });

    let withdrawalAccount = await this.withdrawalAccountModel.findOne({
      account_number: account.account_number,
      bank_code: account.bank_code,
      wallet: wallet.id,
    });

    if (withdrawalAccount) {
      throw new HttpException(
        {
          message: 'Withdrawal account already exists',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    const getBanksPromise = new Promise<IPaystackBank[]>((res, rej) => {
      this.paystack.getBanks(account.currency).then((r) => {
        if (r.error) rej(r.error);
        else res(r.data);
      });
    });

    let bank: IPaystackBank;
    let recipient_id: string = null;

    if (currenciesWithResolveAccount.includes(account.currency)) {
      const resolvedAccountPromise = this.resolveAccountNumber({ ...account, currency: account.currency });

      const [resolvedAccount, banks] = await Promise.all([resolvedAccountPromise, getBanksPromise]);
      account.account_name = resolvedAccount.account_name;
      bank = banks.filter((b) => b.code === account.bank_code)[0];
    } else {
      const banks = await getBanksPromise;
      bank = banks.filter((b) => b.code === account.bank_code)[0];
    }

    if (withdrawalProviders[account.currency].includes(WITHDRAWAL_PROVIDERS.PAYSTACK)) {
      const recipient_code = await this.paystack.createRecipient(
        account.account_name,
        account.account_number,
        account.bank_code,
        account.currency || CURRENCIES.NGN,
      );

      if (recipient_code.error) {
        throw new InternalServerErrorException(recipient_code['error']);
      }

      recipient_id = recipient_code?.data;
    }

    withdrawalAccount = new this.withdrawalAccountModel({
      recipient_id,
      account_name: account.account_name,
      account_number: account.account_number,
      bank_code: account.bank_code,
      bank_name: bank.name,
      wallet: wallet._id,
      currency: account.currency,
    });

    wallet.withdrawal_accounts.push(withdrawalAccount._id);

    await withdrawalAccount.save();
    await wallet.save();

    return withdrawalAccount.toJSON() as WithdrawalAccount;
  }

  async autoSendTxToBank(txId: string) {
    const transaction = await this.transactionModel.findOne({ _id: txId });

    if (!transaction || transaction.type !== 'credit') {
      // throw new BadRequestException('Transaction not found');
      return { error: 'Invalid Transaction Provided' };
    }

    const wallet = await this.walletModel.findOne({ _id: transaction.wallet });

    if (!wallet || !wallet.auto_send_funds_to) {
      // throw new BadRequestException('Transaction not found');
      return { error: 'Wallet not found or cannot process' };
    }

    const amountToWithdraw = transaction.amount;
    const withdrawalAccount = await this.withdrawalAccountModel.findOne({
      _id: wallet.auto_send_funds_to,
    });

    if (wallet.balance < amountToWithdraw) {
      return { error: 'Insufficient funds for auto withdrawal' };
    }

    if (!withdrawalAccount) {
      return { error: 'Withdrawal account not found for auto withdrawal' };
    }

    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        stores: { $in: [wallet.store] },
      })
      .toPromise();

    const withdrawalCode = generateNumber(6);
    const reference = uuidV4();
    const provider = await this.getWithdrawalProvider(transaction.currency, wallet.auto_send_funds_to as string);

    const withdrawal = await new this.withdrawalRequestModel({
      user: getDocId(user),
      token: withdrawalCode,
      currency: transaction.currency,
      withdrawal_account: wallet.auto_send_funds_to,
      status: WITHDRAWAL_STATUSES.INITIATED,
      wallet: getDocId(wallet),
      amount: amountToWithdraw, // Amount sent is in Naira, convert to kobo
      expires_in: Date.now() + 1000 * 60 * 10, // ten minutes
      provider,
      vendorReference: reference,
    }).save();

    //process withdrawal
    await this.walletQueue.add(QUEUES.WALLET, {
      type: JOBS.WALLET_WITHDRAWAL,
      payload: {
        user_id: user.id,
        request_id: getDocId(withdrawal),
        withdrawal_fee: 0,
        withdrawal_account: withdrawalAccount,
      },
    });
  }

  async withdrawalRequest(user: User, storeId: string, data: WithdrawalRequestDto) {
    const withdrawalCode = generateNumber(6);
    const reference = uuidV4();
    const amount = data.amount * 100;

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: storeId,
      })
      .toPromise();

    const wallet = await this.walletModel.findOne({ _id: data.wallet });

    if (!wallet) throw new BadRequestException('Wallet not found');

    if (wallet.store.toString() !== storeId)
      throw new BadRequestException('You cannot make withdrawals from this wallet');

    if (!store.kyc_approved || !store.payments_enabled || !wallet.has_completed_kyc) {
      throw new BadRequestException('Please complete KYC to make withdrawals');
    }

    //RUN CHECKS
    const currency = wallet.currency;

    const twentyFourHoursAgo = new Date(new Date().getTime() - 12 * 60 * 60 * 1000);

    const withdrawalsIn24Hhrs = await this.withdrawalRequestModel
      .find({
        wallet: wallet.id,
        createdAt: { $gte: twentyFourHoursAgo },
        status: WITHDRAWAL_STATUSES.SUCCESSFUL,
      })
      .sort({ createdAt: -1 });

    const sumOf24Hrs = withdrawalsIn24Hhrs.reduce((sum, { amount }) => sum + amount, 0);

    const computedDailyLimit = wallet?.daily_withdrawal_limit
      ? wallet?.daily_withdrawal_limit
      : limit24Hr[wallet.currency];

    if (withdrawalsIn24Hhrs.length > 0 && sumOf24Hrs + amount > computedDailyLimit) {
      const timeToWait = Math.abs(dayjs().diff(dayjs(withdrawalsIn24Hhrs[0].createdAt).add(24, 'hours'), 'hour'));
      throw new BadRequestException(`Please wait ${timeToWait} hr(s) to make another withdrawal.`);
    }

    if (amount < minWithdrawalAmount[currency])
      throw new PreconditionFailedException(
        `Withdrawal amount should be at least ${toCurrency(
          (minWithdrawalAmount[currency] / 100).toString(),
          currency,
        )}`,
      );

    if (amount > maxWithdrawalAmount[currency])
      throw new PreconditionFailedException(
        `You cannot withdraw more than ${toCurrency((maxWithdrawalAmount[currency] / 100).toString(), currency)}`,
      );

    const withdrawalFee = calculateWithdrawalFee(amount, currency, store?.flags?.uses_chowbot);

    const total = withdrawalFee + amount;

    if (!wallet) throw new BadRequestException('Something went wrong, please try again');

    if (wallet.balance < amount) {
      throw new PreconditionFailedException('Insufficient balance');
    }

    if (wallet.balance < total) {
      throw new PreconditionFailedException(
        `Insufficient balance for fee of ${toCurrency((withdrawalFee / 100).toString(), currency)}`,
      );
    }

    // CHECKS ENDED
    const provider = await this.getWithdrawalProvider(currency, data.withdrawal_account);

    const storeOwner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        _id: store.owner,
      })
      .toPromise();

    const withdrawal = await new this.withdrawalRequestModel({
      user: user.id,
      token: withdrawalCode,
      currency: currency,
      withdrawal_account: data.withdrawal_account,
      status: WITHDRAWAL_STATUSES.INITIATED,
      wallet: getDocId(wallet),
      amount: amount, // Amount sent is in Naira, convert to kobo
      expires_in: Date.now() + 1000 * 60 * 10, // ten minutes
      provider,
      vendorReference: reference,
    }).save();

    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.VERIFY_WITHDRAWAL, {
      to: storeOwner.email,
      subject: `Your withdrawal OTP code is ${withdrawalCode} 🔒`,
      data: {
        name: storeOwner.name.split(' ')[0],
        code: withdrawalCode,
        amount: toCurrency((withdrawal.amount / 100).toString(), wallet.currency),
      },
    });

    // return withdrawal._id;

    return {
      request: withdrawal._id,
      email: maskEmail(storeOwner.email),
    };
  }

  async resendOTPToken(requestId: string, storeId: string) {
    const withdrawalRequest = await this.withdrawalRequestModel.findOne({ _id: requestId });

    if (!withdrawalRequest) {
      throw new BadRequestException('Invalid withdrawal, please initiate a new withdrwal');
    }

    if (withdrawalRequest.status !== WITHDRAWAL_STATUSES.INITIATED) {
      throw new BadRequestException('Please initaite a new withdrawal');
    }

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: storeId,
      })
      .toPromise();

    const storeOwner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        _id: store.owner,
      })
      .toPromise();

    const withdrawalCode = generateNumber(6);
    withdrawalRequest.token = withdrawalCode;
    withdrawalRequest.expires_in = Date.now() + 1000 * 60 * 10;

    withdrawalRequest.save();

    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.VERIFY_WITHDRAWAL, {
      to: storeOwner.email,
      subject: `Your withdrawal OTP code is ${withdrawalCode} 🔒`,
      data: {
        name: storeOwner.name.split(' ')[0],
        code: withdrawalCode,
        amount: toCurrency((withdrawalRequest.amount / 100).toString(), withdrawalRequest.currency),
      },
    });

    return {
      request: withdrawalRequest.id,
      email: maskEmail(storeOwner.email),
    };
  }

  async completeWithdrawalRequest(user: User, data: CompleteWithdrawalRequestDto, storeId: string) {
    let securityPinMatch: boolean;

    try {
      securityPinMatch = await this.brokerTransport
        .send<boolean>(BROKER_PATTERNS.STORE.VERIFY_SECURITY_PIN, {
          store_id: storeId,
          security_pin: data.security_pin,
        })
        .toPromise();
    } catch (e) {
      throw new BadRequestException(e);
    }

    if (!securityPinMatch) throw new BadRequestException('Invalid security pin');

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, {
        _id: storeId,
      })
      .toPromise();

    const request = await this.withdrawalRequestModel.findOne({
      _id: data.request_id,
      user: user.id,
    });

    //RUN CHECKS

    if (!request) {
      throw new BadRequestException('Invalid withdrawal, please initiate a new withdrwal');
    }

    if (request.status !== WITHDRAWAL_STATUSES.INITIATED) {
      console.log('PROCESSED_WITHDRAWAL', request);
      throw new BadRequestException('Please initaite a new withdrawal');
    }

    if (request.amount < minWithdrawalAmount[request.currency])
      throw new BadRequestException(
        `Withdrawal amount should be at least ${toCurrency(
          (minWithdrawalAmount[request.currency] / 100).toString(),
          request.currency,
        )}`,
      );
    if (request.amount > maxWithdrawalAmount[request.currency])
      throw new BadRequestException(
        `You cannot withdraw more than ${toCurrency(
          (maxWithdrawalAmount[request.currency] / 100).toString(),
          request.currency,
        )}`,
      );

    const match = request.token === data.code;
    const expired = Date.now() > request.expires_in;

    if (!match || expired) {
      throw new PreconditionFailedException('Token is incorrect or has expired');
    }

    //END RUN CHECKS

    const withdrawalAccount = await this.withdrawalAccountModel.findOne({
      _id: request.withdrawal_account,
    });

    if (!withdrawalAccount) {
      throw new BadRequestException('Invalid withdrawal account');
    }

    const withdrawalFee = calculateWithdrawalFee(request.amount, request.currency, store?.flags?.uses_chowbot);

    let wallet;
    try {
      //Try to give user accurate error
      wallet = await this.walletModel.findOne({
        _id: request.wallet,
      });

      if (wallet.balance < request.amount) {
        throw new BadRequestException('Insufficient balance');
      }

      if (wallet.balance < request.amount + withdrawalFee) {
        throw new BadRequestException(
          `Insufficient balance for fee of ${toCurrency((withdrawalFee / 100).toString(), request.currency)}`,
        );
      }
    } catch {
      //Fallback if previous step failed
      throw new BadRequestException('Insufficient balance');
    }

    if (request.provider !== WITHDRAWAL_PROVIDERS.MANUAL) {
      await this.walletQueue.add(QUEUES.WALLET, {
        type: JOBS.WALLET_WITHDRAWAL,
        payload: {
          user_id: user.id,
          request_id: request._id,
          withdrawal_fee: withdrawalFee,
          withdrawal_account: withdrawalAccount,
        },
      });
    } else {
      // Send Slack notification for manual withdrawal
      const storeOwner = await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.GET_USER, {
          _id: store.owner,
        })
        .toPromise();

      // Get WhatsApp number from store's checkout channels
      const whatsappNumber = store.phone;

      try {
        await this.slack.sendManualWithdrawalNotification({
          name: storeOwner.name,
          country: typeof store.country === 'string' ? store.country : store.country.code,
          store_name: store.name,
          store_slug: store.slug,
          whatsapp: whatsappNumber,
          amount: toCurrency((request.amount / 100).toString(), request.currency),
          currency: request.currency,
          account_name: withdrawalAccount.account_name,
          account_number: withdrawalAccount.account_number,
          bank_name: withdrawalAccount.bank_name,
          cta_url: `${process.env.CATLOG_APP}/new-internals/withdrawals/${request._id}`,
          cta_label: 'Process Manual Withdrawal',
        });

        const balanceBefore = wallet.balance;

        wallet.balance -= request.amount + withdrawalFee;
        await wallet.save();

        new this.transactionModel({
          amount: request.amount,
          type: TRANSACTION_TYPE.DEBIT,
          channel: TRANSACTION_CHANNELS.WITHDRAWAL,
          narration: `Withdrawal to ${withdrawalAccount.account_name}`,
          balance_before: balanceBefore,
          reference: request.vendorReference,
          wallet: wallet._id,
          meta: {
            withdrawal_request: request._id,
          },
          source: {
            name: withdrawalAccount.account_name,
            account_number: withdrawalAccount.account_number,
            method: 'Manual',
            purpose: 'Withdrawal',
          },
          fee: withdrawalFee,
          currency: wallet.currency,
        }).save();

        request.fee = withdrawalFee;
        request.status = WITHDRAWAL_STATUSES.PENDING;
        await request.save();
      } catch (error) {
        this.logger.error('Failed to send manual withdrawal notification', error);
        // Mark the withdrawal as failed with internal failure flag
        await this.withdrawalFailed({ reference: request.vendorReference, internalFailure: true });
        throw new BadRequestException('Failed to process manual withdrawal notification');
      }
    }

    return { processed: true };
  }

  async resolveAccountNumber(data: ResolveAccountDto) {
    let res;

    if ([CURRENCIES.NGN, CURRENCIES.GHC, CURRENCIES.ZAR].includes(data.currency)) {
      res = await this.paystack.resolveAccountNumber(data.account_number, data.bank_code, data.currency);
    }

    if ([CURRENCIES.KES].includes(data.currency)) {
      res = await this.startbutton.resolveAccountNumber(data.account_number, data.bank_code);
    }

    if (!res || res.error) {
      throw new BadRequestException(res.error);
    }

    return res.data;
  }

  async withdrawalSuccessful({ reference, nipReference = '' }: { reference: string; nipReference?: string }) {
    const request = await this.withdrawalRequestModel
      .findOne({
        vendorReference: reference,
      })
      .populate('withdrawal_account');

    if (!request) {
      console.log('MISSING_REQUEST', request);
      throw new Error('Withdrawal request not found');
    }

    if (request.status !== WITHDRAWAL_STATUSES.PENDING) {
      console.log('PROCESSED_WITHDRAWAL', request);
      throw new Error('Withdrawal was previously processed');
    }

    const wallet = await this.walletModel.findOne({ _id: request.wallet }).populate('store');

    const storeOwner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        _id: (wallet.store as Store).owner,
      })
      .toPromise();

    if (!request || !wallet || !storeOwner) throw new Error('Welp');

    request.status = WITHDRAWAL_STATUSES.SUCCESSFUL;
    request.gateway_fee = withdrawalFeeCalculators[request.provider](request.amount, request.currency);
    request.nipReference = nipReference ?? '';
    await request.save();

    //notify user that their withdrwal was successful
    this.resend.sendEmail(BROKER_PATTERNS.MAIL.WITHDRAWAL_SUCCESSFUL, {
      to: storeOwner.email,
      subject: `Your withdrawal of ${toCurrency(
        (request.amount / 100).toString(),
        request.currency,
      )} was successful 💸`,
      data: {
        fee: toCurrency((request.fee / 100).toString(), request.currency),
        name: storeOwner.name.split(' ')[0],
        date: new Date().toDateString(),
        amount: toCurrency((request.amount / 100).toString(), request.currency),
        remaining_balance: toCurrency((wallet.balance / 100).toString(), request.currency),
        image_link: bankLogos[(request?.withdrawal_account as WithdrawalAccount).bank_code],
        account_number: (request?.withdrawal_account as WithdrawalAccount).account_number,
        wallet_link: process.env.CATLOG_APP + '/dashboard',
      },
    });

    await this.brokerTransport
      .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        message: {
          title: 'Your withdrawal was successful 💸',
          message: `${toCurrency((request.amount / 100).toString(), request.currency)} has been sent to ${
            (request?.withdrawal_account as WithdrawalAccount).account_number
          } ${(request?.withdrawal_account as WithdrawalAccount).bank_name}`,
          path: `/dashboard`,
        },
        owner_only: true,
        store: (wallet?.store as Store)?._id,
      })
      .toPromise();
  }

  async countCreditPayments(walletId: string) {
    const payments = await this.transactionModel.countDocuments({
      wallet: walletId,
      type: TRANSACTION_TYPE.CREDIT,
      channel: { $ne: TRANSACTION_CHANNELS.REVERSAL },
    });

    if (payments > FREE_PAYMENT_THRESHOLD) {
      await this.walletModel.findByIdAndUpdate(walletId, { $set: { 'meta.has_crossed_free_threshold': true } });
    }

    return payments;
  }

  // async blochqWithdrawal({ reference }) {
  //   const request = await this.withdrawalRequestModel
  //     .findOne({
  //       vendorReference: reference,
  //     })
  //     .populate('withdrawal_account');

  //   if (!request) {
  //     console.log('MISSING_REQUEST', request);
  //     throw new Error('Withdrawal request not found');
  //   }

  //   if (request.status !== WITHDRAWAL_STATUSES.PENDING) {
  //     console.log('PROCESSED_WITHDRAWAL', request);
  //     throw new Error('Withdrawal was previously processed');
  //   }

  //   const wallet = await this.walletModel.findOne({ _id: request.wallet }).populate('store');

  //   const storeOwner = await this.brokerTransport
  //     .send<User>(BROKER_PATTERNS.USER.GET_USER, {
  //       _id: (wallet.store as Store).owner,
  //     })
  //     .toPromise();

  //   if (!request || !wallet || !storeOwner) throw new Error('Welp');

  //   request.status = WITHDRAWAL_STATUSES.SUCCESSFUL;
  //   request.gateway_fee = paystackTranferFeeCalculator(request.amount);
  //   await request.save();

  //   //notify user that their withdrwal was successful
  //   this.resend.sendEmail(BROKER_PATTERNS.MAIL.WITHDRAWAL_SUCCESSFUL, {
  //       to: storeOwner.email,
  //       subject: `Your withdrawal of ${toCurrency((request.amount / 100).toString(), 'NGN')} was successful 💸`,
  //       data: {
  //         fee: toCurrency((request.fee / 100).toString(), 'NGN'),
  //         name: storeOwner.name.split(' ')[0],
  //         date: new Date().toDateString(),
  //         amount: toCurrency((request.amount / 100).toString(), 'NGN'),
  //         remaining_balance: toCurrency((wallet.balance / 100).toString(), 'NGN'),
  //         image_link: bankLogos[(request?.withdrawal_account as WithdrawalAccount).bank_code],
  //         account_number: (request?.withdrawal_account as WithdrawalAccount).account_number,
  //         wallet_link: process.env.CATLOG_APP + '/dashboard',
  //       },
  //     })
  //     .toPromise();

  //   await this.brokerTransport
  //     .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
  //       message: {
  //         title: 'Your withdrawal was successful 💸',
  //         message: `${toCurrency((request.amount / 100).toString(), 'NGN')} has been sent to ${
  //           (request?.withdrawal_account as WithdrawalAccount).account_number
  //         } ${(request?.withdrawal_account as WithdrawalAccount).bank_name}`,
  //         path: `/dashboard`,
  //       },
  //       owner_only: true,
  //       store: (wallet?.store as Store)?._id,
  //     })
  //     .toPromise();
  // }

  async refundPaymentToWallet(storeId: string, paymentReference: string, narration: string, deductAmount = 0) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) return { error: 'Store not found' };

    const payment = await this.brokerTransport
      .send<Payment>(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, {
        reference: paymentReference,
      })
      .toPromise();

    if (!payment) {
      return { error: 'Payment not found' };
    }

    const walletId = store.wallets.find((w) => w.currency === payment.currency)?.id;

    if (!walletId) return { error: 'Wallet not found' };

    const wallet = await this.walletModel.findOne({ _id: walletId }).populate('store');

    if (!payment) {
      return { error: 'Wallet not found' };
    }
    const amountToRefund = payment.amount_with_charge - deductAmount;

    const data = await this.creditHandler(
      getDocId(wallet),
      amountToRefund,
      0,
      TRANSACTION_CHANNELS.REVERSAL,
      narration,
      {
        original_payment_id: getDocId(payment),
      },
      { name: 'Catlog Reversals', account_number: '', purpose: narration, method: 'Wallet' },
    ); // Failed payout, put the money back

    //SEND NOTIFICATION
    const storeOwner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        _id: (wallet.store as Store).owner,
      })
      .toPromise();

    //notify user that their withdrwal failed
    this.resend.sendEmail(BROKER_PATTERNS.MAIL.FUNDS_REVERSED, {
      to: storeOwner.email,
      subject: `We've reversed ${toCurrency((amountToRefund / 100).toString(), wallet.currency)} back to your wallet`,
      data: {
        name: storeOwner.name.split(' ')[0],
        amount: toCurrency((amountToRefund / 100).toString(), wallet.currency),
        remaining_balance: toCurrency(data.wallet.balance, wallet.currency),
        reason: narration,
        wallet_link: process.env.CATLOG_APP + '/dashboard',
      },
    });

    await this.brokerTransport
      .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        message: {
          title: `We've reversed ${toCurrency((amountToRefund / 100).toString(), wallet.currency)} to your wallet`,
          message: `Reason: ${narration}`,
          path: `/dashboard`,
        },
        owner_only: true,
        store: (wallet?.store as Store)?._id,
      })
      .toPromise();

    return {
      message: 'Reversal Complete',
    };
  }

  async withdrawalFailed({ reference, internalFailure = false }) {
    const request = await this.withdrawalRequestModel
      .findOne({
        vendorReference: reference,
      })
      .populate('withdrawal_account');

    if (!request) {
      console.log('MISSING_REQUEST', request);
      throw new Error('Withdrawal request not found');
    }

    if (request.status !== WITHDRAWAL_STATUSES.PENDING) {
      console.log('PROCESSED_WITHDRAWAL', request);
      throw new Error('Withdrawal was previously processed');
    }

    const wallet = await this.walletModel.findOne({ _id: request.wallet }).populate('store');

    const transaction = await this.transactionModel.findOne({
      wallet: wallet?._id,
      'meta.withdrawal_request': request?._id,
    });

    await this.creditHandler(
      wallet._id,
      request.amount + (wallet?.currency === CURRENCIES.ZAR && !internalFailure ? 0 : request.fee), //don't refund fees for ZAR ungodly :(
      0,
      TRANSACTION_CHANNELS.REVERSAL,
      '[REVERSAL FOR] - ' + transaction?.narration,
      {
        withdrawal_request: request._id,
        transfer_reference: reference,
      },
      { name: 'Catlog Reversals', account_number: '', purpose: 'Withdrawal Reversal', method: request.provider },
    ); // Failed payout, put the money back

    request.status = WITHDRAWAL_STATUSES.FAILED;
    await request.save();

    //SEND NOTIFICATION
    await this.sendFailedWithdrawalNotification(wallet, request);

    return {
      message: 'Reversal Complete',
    };
  }

  async sendFailedWithdrawalNotification(wallet: Wallet, request: WithdrawalRequest) {
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: getDocId(wallet.store) })
      .toPromise();

    const storeOwner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        _id: store.owner,
      })
      .toPromise();

    //notify user that their withdrwal failed
    this.resend.sendEmail(BROKER_PATTERNS.MAIL.WITHDRAWAL_FAILED, {
      to: storeOwner.email,
      subject: `Your withdrawal of ${toCurrency((request.amount / 100).toString(), request.currency)} failed ❌`,
      data: {
        name: storeOwner.name.split(' ')[0],
        amount: toCurrency((request.amount / 100).toString(), request.currency),
        remaining_balance: toCurrency(
          ((wallet.balance + request.amount + request.fee) / 100).toString(),
          request.currency,
        ),
        wallet_link: process.env.CATLOG_APP + '/dashboard',
      },
    });

    await this.brokerTransport
      .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        message: {
          title: 'Your withdrawal failed ❌',
          message: `We've reversed ${toCurrency(
            (request.amount / 100).toString(),
            request.currency,
          )} back into your wallet`,
          path: `/dashboard`,
        },
        owner_only: true,
        store: (wallet?.store as Store)?._id,
      })
      .toPromise();
  }

  async debitHandler(
    walletId: string,
    amount: number,
    channel: string,
    narration: string,
    meta: TransactionMeta,
    source: TransactionSource,
    fee: number = 0,
  ) {
    return await chargeWallet({
      walletId,
      walletModel: this.walletModel,
      Transaction: this.transactionModel,
      dbConnection: this.connection,
      amount,
      fee,
      channel,
      narration,
      type: TRANSACTION_TYPE.DEBIT,
      meta,
      source,
      throwErrors: false,
      mutex: this.mutex,
    });
  }

  async creditHandler(
    walletId: string,
    amount: number,
    fee: number,
    channel: string,
    narration: string,
    meta: TransactionMeta,
    source: TransactionSource,
  ) {
    const wallet_data = await chargeWallet({
      walletId,
      walletModel: this.walletModel,
      Transaction: this.transactionModel,
      dbConnection: this.connection,
      amount,
      fee,
      channel,
      narration,
      type: TRANSACTION_TYPE.CREDIT,
      meta,
      source,
      throwErrors: true,
      mutex: this.mutex,
    });

    if (channel !== TRANSACTION_CHANNELS.REVERSAL) {
      await this.walletQueue.add(
        QUEUES.WALLET,
        {
          type: JOBS.PAYMENT_VOLUME_MILESTONE,
          payload: {
            wallet_id: walletId,
            transaction_amount: amount,
          },
        },
        {
          delay: 1000 * 10,
        },
      );
    }

    return wallet_data;
  }

  async manuallyCreditWallet(data: ManuallyCreditWalletDto) {
    const apiGuardCfg = this.config.get<ApiGuardConfig>('apiGuardConfig');
    const otp = generateOTP(apiGuardCfg.otpSalt);
    const { amount, otp: userOtp, narration, walletId } = data;

    if (userOtp !== otp) throw new BadRequestException('GET THE FUCK OFFFFF!!!! CALLING THE POLICE');

    const wallet = await this.walletModel.findById(walletId);

    if (!wallet) throw new BadRequestException('Wallet Not Found');

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: wallet.store })
      .toPromise();

    if (!store) throw new BadRequestException('Store not found');

    const owner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    if (!owner) throw new BadRequestException('User not found');

    const txReference = genChars(16);
    const senderName = 'Catlog';
    const principal_amount = toKobo(amount);

    const newWallet = await this.creditHandler(
      walletId,
      principal_amount,
      0,
      TRANSACTION_CHANNELS.DIRECT_TRANSFER,
      narration,
      {
        manual_credit_reference: txReference,
      },
      { account_number: '', name: senderName, purpose: 'Inward Transfer', method: 'Direct Transfer' },
    );

    if (wallet.balance === newWallet.wallet.balance) return;

    const payment = this.brokerTransport
      .send<Payment>(BROKER_PATTERNS.PAYMENT.MANUALLY_RECORD_PAYMENT, {
        reference: txReference,
        storeId: wallet.store,
        amount: principal_amount,
        gateway_charge: 0,
        gateway_amount_settled: principal_amount,
        currency: wallet.currency,
        provider: PAYMENT_PROVIDERS.SAME_AS_METHOD,
        payment_type: PAYMENT_TYPES.WALLET,
        payment_method: PAYMENT_METHODS.DIRECT_TRANSFER,
        customer: {
          name: 'Catlog',
          email: '',
          phone: '',
          id: '',
        },
      })
      .toPromise();

    await this.transactionModel.findByIdAndUpdate(getDocId(newWallet.transaction), {
      meta: { ...newWallet.transaction.meta, payment_id: getDocId(payment) },
    });

    this.resend.sendEmail(BROKER_PATTERNS.MAIL.TRANSFER_RECEIVED, {
      to: owner.email,
      subject: `${senderName ? senderName : 'Someone'} just paid you ${toCurrency(
        (principal_amount / 100).toString(),
        wallet.currency,
      )} 🤑`,
      data: {
        name: owner.name.split(' ')[0],
        received: toCurrency((principal_amount / 100).toString(), wallet.currency),
        source_name: senderName ? `${senderName}` : 'Someone',
        current_balance: toCurrency((newWallet.wallet.balance / 100).toString(), wallet.currency),
        fee: toCurrency(String(0), wallet.currency),
        credited: toCurrency((principal_amount / 100).toString(), wallet.currency),
        date: new Date().toDateString(),
        funds_link: process.env.CATLOG_APP,
      },
    });

    await this.brokerTransport
      .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        message: {
          title: "You've received some money 🤑",
          message: `${senderName ? senderName : 'Someone'} sent you ${toCurrency(
            (principal_amount / 100).toString(),
            wallet.currency,
          )} via your account`,
          path: `/dashboard`,
        },
        owner_only: false,
        store: store?.id,
      })
      .toPromise();

    return {
      balance: newWallet.wallet.balance,
    };
  }

  async squadTransfer(data) {
    const {
      virtual_account_number,
      principal_amount,
      fee_charged,
      transaction_indicator,
      remarks,
      transaction_reference,
      customer_identifier,
      settled_amount,
      sender_name,
    } = data;
    console.log('ADDING SQUAD PAYMENT TO QUEUE');
    const getSenderName = () => {
      const startString = 'transfer from ';
      let extractName = remarks.toLowerCase().substring(startString.length, remarks.toLowerCase().lastIndexOf(' |'));

      return extractName;
    };

    const senderName =
      sender_name ??
      getSenderName()
        .split(' ')
        .map((s: string) => s.toUpperCase())
        .join(' ');

    const payload: BankTransferPayload = {
      virtual_account_number,
      transaction_indicator,
      transaction_reference,
      customer_identifier,
      principal_amount: toKobo(Number(principal_amount)),
      settled_amount: toKobo(Number(settled_amount)),
      fee_charged: toKobo(Number(fee_charged)),
      source: {
        name: senderName,
        account_number: '',
        bank: '',
        purpose: 'Inward Transfer',
        method: 'Direct Transfer',
      },
      meta: {
        squad_transaction_id: transaction_reference,
      },
      provider: ACCOUNT_PROVIDERS.SQUAD,
    };

    await this.walletQueue.add(
      QUEUES.WALLET,
      {
        type: JOBS.WALLET_BANK_PAYMENT,
        payload,
      },
      {
        delay: 1000 * 10,
      },
    );
  }

  async payazaTransfer(data) {
    const {
      transaction_reference,
      transaction_status,
      virtual_account_number,
      transaction_fee,
      amount_received,
      initiated_date,
      current_status_date,
      received_from,
    } = data;
    console.log('ADDING PAYAZA PAYMENT TO QUEUE');

    // Ensure that only successful transactions are processed
    if (transaction_status !== 'Funds Received' && transaction_status !== 'Completed') return;

    const principal_amount = toKobo(Number(amount_received));
    const fee_charged = toKobo(Number(transaction_fee));
    const settled_amount = toKobo(principal_amount - fee_charged);

    const payload: BankTransferPayload = {
      virtual_account_number,
      transaction_indicator: 'C',
      transaction_reference,
      customer_identifier: '',
      fee_charged,
      principal_amount,
      source: {
        name: received_from?.account_name,
        account_number: received_from?.account_number,
        bank: received_from?.bank_name,
        purpose: 'Inward Transfer',
        method: 'Direct Transfer',
      },
      provider: ACCOUNT_PROVIDERS.PAYAZA,
      settled_amount,
      meta: {
        payaza_transaction_id: transaction_reference,
      },
    };

    await this.walletQueue.add(
      QUEUES.WALLET,
      {
        type: JOBS.WALLET_BANK_PAYMENT,
        payload,
      },
      {
        delay: 1000 * 10,
      },
    );
  }

  // async createTestPayment(storeId: string) {
  //   const wallet = await this.walletModel.findOne({ store: storeId });

  //   if (!wallet) {
  //     throw new NotFoundException('Wallet not found!');
  //   }

  //   const data = await this.brokerTransport
  //     .send(BROKER_PATTERNS.PAYMENT.CREATE_TEST_PAYMENT, { store: storeId, currency: wallet?.currency })
  //     .toPromise();

  //   if (data.error) {
  //     throw new BadRequestException(data.error);
  //   }

  //   return data;
  // }

  async getSetupProgress(walletId: string) {
    const wallet = await this.walletModel.findOne({ _id: walletId });

    if (!wallet) {
      //disable payments for the store
      const store = await this.brokerTransport
        .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, {
          wallet: walletId,
        })
        .toPromise();

      if (store) {
        store.configuration.direct_checkout_enabled = false;
        store.payments_enabled = false;

        await this.brokerTransport
          .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
            filter: { _id: store._id },
            update: store,
          })
          .toPromise();
      }

      throw new UnauthorizedException("Couldn't find a wallet for this store");
    }

    const withdrawalAccountsCount = wallet.withdrawal_accounts.length;

    return {
      has_withdrawal_accounts: withdrawalAccountsCount > 0,
    };
  }

  async addGatewayFeeToTransfers() {
    const transfers = await this.withdrawalRequestModel.find({});
    const promises: Promise<WithdrawalRequestDocument>[] = [];

    transfers.forEach((t) => {
      t.gateway_fee = paystackTranferFeeCalculator(t.amount, t.currency);
      promises.push(t.save());
    });

    return await Promise.all(promises);
  }

  async migrateAllSquadTransactionsToPayments() {
    const squadTxs = await this.transactionModel.find({ channel: TRANSACTION_CHANNELS.DIRECT_TRANSFER });
    const payments: Promise<Payment>[] = [];
    for (const tx of squadTxs) {
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { wallet: tx.wallet })
        .toPromise();

      if (!store) continue;

      const fee_charged = squadFeeCalculator(tx.amount);

      const p = this.brokerTransport
        .send<Payment>(BROKER_PATTERNS.PAYMENT.MANUALLY_RECORD_PAYMENT, {
          reference: tx.meta.squad_transaction_id,
          storeId: store._id,
          amount: tx.amount,
          gateway_charge: fee_charged,
          gateway_amount_settled: tx.amount - fee_charged,
          currency: CURRENCIES.NGN,
          provider: PAYMENT_PROVIDERS.SQUAD,
          payment_type: PAYMENT_TYPES.WALLET,
          payment_method: PAYMENT_METHODS.DIRECT_TRANSFER,
        })
        .toPromise();

      payments.push(p);
    }

    return await Promise.all(payments);
  }

  async migratePayments(
    lastProcessedId: string | null,
  ): Promise<{ migratedCount: number; totalCount: number; lastProcessedId: string | null }> {
    const batchSize = 2000;
    const query: any = { 'meta.squad_transaction_id': { $exists: true } };

    if (lastProcessedId) {
      query._id = { $gt: lastProcessedId }; // Fetch transactions after the last processed ID
    }

    const transactions = await this.transactionModel
      .find(query)
      .sort({ _id: 1 }) // Sort by ID to ensure consistent ordering
      .limit(batchSize)
      .populate('wallet')
      .exec();

    const promises = transactions.map(async (transaction) => {
      const { squad_transaction_id } = transaction.meta;
      const store = (transaction.wallet as any).store;

      const filter = { vendorReference: squad_transaction_id };
      const update = {
        $set: {
          store,
          customer: { name: transaction?.source?.name ?? 'Unkown Customer', email: '', phone: '', id: '' },
        },
      };

      return this.brokerTransport
        .send<Payment>(BROKER_PATTERNS.PAYMENT.UPDATE_PAYMENT, { filter, update })
        .toPromise();
    });

    // Execute all updates concurrently
    await Promise.all(promises);

    const lastId = transactions.length > 0 ? transactions[transactions.length - 1]._id : null;

    return {
      migratedCount: transactions.length,
      totalCount: await this.transactionModel.countDocuments(query).exec(),
      lastProcessedId: lastId,
    };
  }

  async addCurrenciesToTransactions() {
    const transactions = await this.transactionModel.find({}, {}).lean();

    const updates: Promise<TransactionDocument>[] = [];

    for (let tx of transactions) {
      updates.push(
        this.transactionModel
          .findByIdAndUpdate(tx._id, {
            currency: CURRENCIES.NGN,
          })
          .exec(),
      );
    }

    const res = await Promise.all(updates);
    return res;
  }

  async addCurrenciesToWallets() {
    const wallets = await this.walletModel.find({}, {}).lean();

    const updates: Promise<WalletDocument>[] = [];

    for (let wallet of wallets) {
      updates.push(
        this.walletModel
          .findByIdAndUpdate(wallet._id, {
            currency: CURRENCIES.NGN,
          })
          .exec(),
      );
    }

    const res = await Promise.all(updates);
    return res;
  }

  //payment statement service

  async createPaymentStatement(storeId: string, data: CreatePaymentStatementDto) {
    const wallet = await this.walletModel.findOne({ store: storeId, currency: data.currency }).sort({ _id: -1 });

    if (!wallet) throw new PreconditionFailedException('Wallet not found');

    const statement_pin = genChars(6, true, true);
    const statement = new this.paymentStatementModel({
      period: {
        ...data,
      },
      pin: statement_pin,
      wallet: wallet.id,
      store: storeId,
    });

    await statement.save();
    statement.wallet = wallet;

    return statement;
  }

  async getPaginatedStatements(
    { ...filterQuery }: FilterQuery<PaymentStatement> & { store: any },
    paginationQuery: PaginatedQueryDto,
  ) {
    if (filterQuery.search && isValidObjectId(filterQuery.search)) {
      filterQuery._id = filterQuery.search;
    }
    delete filterQuery.search;

    if (paginationQuery.sort === 'ASC') {
      paginationQuery.sort = { created_at: 1 };
    }

    if (paginationQuery.sort === 'DESC') {
      paginationQuery.sort = { created_at: -1 };
    }

    const result = await this.paymentStatementModel.paginate(filterQuery, {
      ...mapPaginateQuery(paginationQuery),
      populate: {
        path: 'wallet',
        select: 'currency',
      },
    });

    return {
      data: result.docs,
      ...mapPaginatedResponse(result),
    };
  }

  async getPaymentStatement(id: string, pin: string) {
    const statement = await this.paymentStatementModel.findOne({ _id: id, pin });
    if (!statement) {
      throw new BadRequestException('Invalid payment statement Id or pin');
    }
    const wallet = await this.walletModel.findById(statement.wallet);
    if (!wallet) throw new PreconditionFailedException('store does not have wallet');

    const docs = await this.getWalletTransactionsFromStore(
      String(statement.store),
      statement.wallet as string,
      {
        search: null,
        type: SEARCH_TRANSACTION_TYPES.ALL,
        from: statement.period?.start_date,
        to: statement.period?.end_date,
      },
      {
        page: 0,
        per_page: Number.MAX_SAFE_INTEGER,
      },
      {
        createdAt: 1,
      },
    );
    const transactions = docs?.data?.transactions ?? [];
    let opening_balance = 0;
    let closing_balance = 0;
    let totals = { total_credits: 0, total_debits: 0 };

    const txsWithFeeDebits = [];

    if (transactions.length > 0) {
      const lastTransaction = transactions[transactions?.length - 1];
      const operation = lastTransaction?.type === 'credit' ? 1 : -1;

      opening_balance = transactions[0].balance_before;
      closing_balance = lastTransaction?.balance_before + lastTransaction.amount * operation - lastTransaction.fee;

      for (let index = 0; index < transactions.length; index++) {
        const tx = transactions[index];
        txsWithFeeDebits.push(tx);

        if (tx.type === 'credit') {
          totals.total_credits += tx.amount;

          if (tx.fee > 0) {
            totals.total_debits += tx.fee;
            txsWithFeeDebits.push({
              ...tx,
              type: 'debit',
              amount: tx.fee,
              narration: `Fee for ${tx.narration}`,
              fee: 0,
              balance_before: tx.balance_before + tx.amount,
            }); //needed to create a new record for credit fees as transactions
          }
        } else if (tx.type === 'debit') totals.total_debits += tx.amount + tx.fee;
      }
    }

    return {
      ...statement.toJSON(),
      account: wallet?.account ?? null,
      transactions: txsWithFeeDebits,
      stats: {
        opening_balance,
        closing_balance,
        ...totals,
      },
      wallet: {
        id: wallet.id,
        currency: wallet.currency,
      },
    };
  }

  async getStatementsPdfFile(id: string, pin: string) {
    const statement = await this.paymentStatementModel.findOne({ _id: id, pin });

    if (!statement) {
      throw new BadRequestException('Invalid Statement id');
    }

    const statementUrl = process.env.CATLOG_WWW + '/statements/pdf/' + `${id}?pin=${statement.pin}`;
    const data = await generatePDFFromWebpage(statementUrl, 900, 900 * 1.414, { bottom: 15 });

    if (data.pdf) {
      return data.pdf;
    } else {
      throw new BadRequestException("Couldn't generate invoice PDF");
    }
  }

  async migrateSpecificBanksRecipientCodes() {
    const bankCodes = ['MTN', 'ATL', 'VOD'];

    const accountsToUpdate = await this.withdrawalAccountModel.find({ bank_code: { $in: bankCodes } });

    const updatePromises = accountsToUpdate.map(async (account) => {
      const wallet = await this.walletModel.findById(account.wallet);
      if (!wallet) return null;

      const recipient_code = await this.paystack.createRecipient(
        account.account_name,
        account.account_number,
        account.bank_code,
        wallet.currency,
      );

      if (recipient_code.error) {
        throw new InternalServerErrorException(recipient_code['error']);
      }

      account.recipient_id = recipient_code.data;
      await account.save();
      return account;
    });

    const updatedAccounts = await Promise.all(updatePromises);

    return updatedAccounts;
  }

  async testRaceCondition(data: { accountNumber: string; amountOne: number; amountTwo: number }) {
    const reqs = [
      this.squad.mockTransfer({ account_number: data.accountNumber, amount: data.amountOne }),
      this.squad.mockTransfer({ account_number: data.accountNumber, amount: data.amountTwo }),
    ];

    const res = await Promise.all(reqs);

    return res;
  }

  async updateHasCompletedKyc() {
    await this.walletModel.updateMany({ has_completed_kyc: true }, [
      {
        $set: {
          limits: {
            $cond: {
              if: { $eq: ['$currency', 'NGN'] },
              then: WALLET_LIMITS.AFTER_KYC.NGN,
              else: WALLET_LIMITS.AFTER_KYC.GHS,
            },
          },
        },
      },
    ]);

    const wallets = await this.walletModel.find({ has_completed_kyc: false });
    const promises = [];

    for (let index = 0; index < wallets.length; index++) {
      const wallet = wallets[index];
      const store = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { wallet: wallet._id })
        .toPromise();

      if (store.kyc_approved) {
        wallet.has_completed_kyc = true;
        wallet.limits = WALLET_LIMITS.AFTER_KYC[COUNTRY_CURRENCY_MAP[store.country as COUNTRY_CODE]];
        promises.push(wallet.save());
      }
    }

    const res = await Promise.all(promises);
    return res;
  }

  async requestWalletCreation(storeId: String, body: CreateInternationalWalletRequestDto) {
    const store = await this.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    // Fetch KYC information
    const kyc = await this.brokerTransport
      .send<Kyc>(BROKER_PATTERNS.KYC.GET_KYC, { _id: store.kyc })
      .toPromise();

    // Validate store and KYC data
    if (!kyc) {
      throw new BadRequestException('KYC info not found! Please reload page');
    }

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    if (kyc.status !== 'APPROVED') {
      throw new BadRequestException('Please complete KYC to request new wallets');
    }

    const existingWallets = await this.walletModel.find({ store: store.id });
    const existingCurrencies = existingWallets.map((w) => w.currency);

    const newCurrencies = body.requested_currencies.filter((currency) => !existingCurrencies.includes(currency));

    if (newCurrencies.length === 0) {
      throw new BadRequestException('You already have access to the requested currencies');
    }

    const walletRequest = new this.walletRequestModel({
      store: store.id,
      requested_currencies: newCurrencies,
      reason: body.reason,
      collect_payments_from_abroad: body.collect_payments_from_abroad,
      current_payment_method: body.current_payment_method,
      plans_to_get_customers_abroad: body.plans_to_get_customers_abroad,
      status: WALLET_REQUEST_STATUS.PENDING,
    });

    await walletRequest.save();

    // Get user information for notification
    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    // Send notification to Slack
    await this.slack.sendWalletRequestNotification({
      name: user.name,
      country: typeof store.country === 'string' ? store.country : store.country.code,
      store_name: store.name,
      store_slug: store.slug,
      whatsapp: kyc.phone,
      requested_currencies: newCurrencies,
      reason: body.reason,
      collect_payments_from_abroad: body.collect_payments_from_abroad,
      current_payment_method: body.current_payment_method,
      plans_to_get_customers_abroad: body.plans_to_get_customers_abroad,
      cta_url: 'https://app.catlog.shop/new-internals/wallet-requests',
      cta_label: 'Approve or Reject Request',
    });

    return walletRequest;
  }

  async getLatestWalletRequest(storeId: string) {
    const walletRequest = await this.walletRequestModel.findOne({ store: storeId }).sort({ created_at: -1 }).exec();

    return walletRequest;
  }

  async updateWalletRequest(body: UpdateWalletRequestDto) {
    const walletRequest = await this.walletRequestModel.findById(body.wallet_request_id).exec();

    if (!walletRequest) {
      throw new NotFoundException('Wallet request not found');
    }

    if (walletRequest.status !== WALLET_REQUEST_STATUS.PENDING) {
      throw new BadRequestException('Wallet request has already been processed');
    }

    const store = await this.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: getDocId(walletRequest.store) })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    const owner = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: getDocId(store.owner) })
      .toPromise();

    if (body.status === WALLET_REQUEST_STATUS.APPROVED) {
      const existingWallets = await this.walletModel.find({ store: store.id });
      const existingCurrencies = existingWallets.map((w) => w.currency);

      const newCurrencies = walletRequest.requested_currencies.filter(
        (currency) => !existingCurrencies.includes(currency),
      );

      if (newCurrencies.length === 0) {
        return walletRequest;
      }

      const wallets = existingWallets.map((w) => ({ currency: w.currency, id: getDocId(w) }));
      const currencyPaymentMethods = {};

      const paymentMethods = await this.brokerTransport
        .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {})
        .toPromise();

      for (const currency of newCurrencies) {
        const wallet = new this.walletModel({
          store: store.id,
          currency,
          balance: 0,
          limits: WALLET_LIMITS.AFTER_KYC[COUNTRY_CURRENCY_MAP[store.country as COUNTRY_CODE]],
          has_completed_kyc: true,
        });

        await wallet.save();
        wallets.push({ currency, id: wallet.id });

        const currencyMethods = paymentMethods.filter(
          (method) => method.currencies.includes(currency) && method.payment_types.includes(PAYMENT_TYPES.INVOICE),
        );
        currencyPaymentMethods[currency] = currencyMethods.map((method) => ({
          type: method.type,
          enabled: true,
        }));
      }

      walletRequest.status = body.status;
      if (body.admin_notes) {
        walletRequest.admin_notes = body.admin_notes;
      }

      await walletRequest.save();

      store.wallets = wallets;
      store.payment_options = { ...store.payment_options, ...currencyPaymentMethods };
      const newMarkupRates = {};
      for (let index = 0; index < newCurrencies.length; index++) {
        const currency = newCurrencies[index];
        newMarkupRates[currency] = DEFAULT_CURRENCY_MARKUP;
      }
      store.currencies = {
        ...store.currencies,
        storefront: [...store.currencies.storefront, ...newCurrencies],
        rates: { ...store.currencies.rates, ...newMarkupRates },
      };

      await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
          filter: { _id: store.id },
          update: { wallets: store.wallets, payment_options: store.payment_options },
        })
        .toPromise();

      //send notifications
      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.WALLET_REQUEST_APPROVED, {
        to: owner.email,
        subject: `Your wallet request has been approved 🎉`,
        data: {
          name: owner.name.split(' ')[0],
          currencies: arrayToSentence(newCurrencies),
        },
      });

      await this.brokerTransport
        .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
          message: {
            title: `Your wallet request has been approved 🎉`,
            message: `You can now collect payments in ${arrayToSentence(newCurrencies)}`,
            path: `/dashboard`,
          },
          owner_only: true,
          store: store.id,
        })
        .toPromise();
    } else {
      walletRequest.status = body.status;
      if (body.admin_notes) {
        walletRequest.admin_notes = body.admin_notes;
      }

      await walletRequest.save();

      //send notifications
      await this.resend.sendEmail(BROKER_PATTERNS.MAIL.WALLET_REQUEST_FAILED, {
        to: owner.email,
        subject: `Your wallet request failed ❌`,
        data: {
          name: owner.name.split(' ')[0],
          currencies: arrayToSentence(walletRequest.requested_currencies),
          message: body.admin_notes,
        },
      });

      await this.brokerTransport
        .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
          message: {
            title: `Your request for ${arrayToSentence(walletRequest.requested_currencies)} wallets failed ❌`,
            message: `Reason: ${body.admin_notes}`,
            path: `/dashboard`,
          },
          owner_only: true,
          store: store.id,
        })
        .toPromise();
    }

    return walletRequest;
  }

  async getWalletRequestsWithPagination(
    pagination: PaginatedQueryDto,
    filter: Partial<{ status: WALLET_REQUEST_STATUS; search: string }>,
  ) {
    // 1) Handle the "status" match (straightforward)
    const initialMatch: Record<string, any> = {};
    if (filter?.status) {
      initialMatch.status = filter.status;
    }

    // We'll separately handle the "search" portion after we have the store doc.

    // 2) Count total unpaginated requests first (matching only status).
    const totalWalletRequests = await this.walletRequestModel.countDocuments(initialMatch);

    // 3) Build the aggregation pipeline
    const pipeline: any[] = [
      // Match (by status, for instance)
      { $match: initialMatch },

      // Lookup the store *now* so we can search by store name or id
      {
        $lookup: {
          from: 'stores',
          localField: 'store',
          foreignField: '_id',
          as: 'store',
          pipeline: [
            {
              $project: {
                ver: 1,
                slug: 1,
                phone: 1,
                name: 1,
                socials: 1,
              },
            },
          ],
        },
      },

      {
        $unwind: {
          path: '$store',
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    // 4) If there is a search, build an $or array for:
    //    - reason
    //    - store.name
    //    - store._id (if the input is a valid ObjectId)
    if (filter?.search) {
      const orConditions: any[] = [
        { reason: new RegExp(filter.search, 'i') },
        { 'store.name': new RegExp(filter.search, 'i') },
      ];

      try {
        const storeId = new mongoose.Types.ObjectId(filter.search);
        // If parse is successful, we can match on store._id
        orConditions.push({ 'store._id': storeId });
      } catch (err) {
        // Not a valid ObjectId, so skip
      }

      pipeline.push({
        $match: {
          $or: orConditions,
        },
      });
    }

    // 5) Sort by date created (descending or ascending)
    pipeline.push({
      $sort: {
        created_at: pagination?.sort === 'DATE_CREATED' ? -1 : 1,
      },
    });

    // 6) Pagination (skip / limit)
    pipeline.push(
      {
        $skip: (pagination.page - 1 || 0) * (pagination.per_page || 25),
      },
      {
        $limit: +pagination.per_page || 25,
      },
    );

    // 7) Lookup KYC
    pipeline.push({
      $lookup: {
        from: 'kycs',
        localField: 'store._id',
        foreignField: 'store',
        as: 'kycs',
        pipeline: [
          {
            $project: {
              id: 1,
              first_name: 1,
              last_name: 1,
              bvn: 1,
              status: 1,
              country: 1,
              verification_method: 1,
              created_at: 1,
              updated_at: 1,
            },
          },
        ],
      },
    });

    // 8) Re-group results into metadata / data
    pipeline.push({
      $facet: {
        metadata: [
          { $count: 'count' }, // Count how many remain AFTER skip/limit
          {
            $addFields: {
              page: pagination.page || 1,
              // Insert the *unpaginated* total count
              total_wallet_requests: totalWalletRequests,
            },
          },
        ],
        data: [
          {
            $project: {
              id: 1,
              requested_currencies: 1,
              reason: 1,
              collect_payments_from_abroad: 1,
              current_payment_method: 1,
              plans_to_get_customers_abroad: 1,
              status: 1,
              admin_notes: 1,
              created_at: 1,
              updated_at: 1,
              store: 1, // after unwind, this is just an object, not an array
              kycs: 1,
            },
          },
        ],
      },
    });

    try {
      const [result] = await this.walletRequestModel.aggregate(pipeline);

      if (!result) {
        return {
          count: 0,
          page: pagination.page || 1,
          total_wallet_requests: 0,
          data: [],
        };
      }

      // Flatten out the metadata
      const metadata = result.metadata?.[0] || {
        count: 0,
        page: pagination.page || 1,
        total_wallet_requests: totalWalletRequests,
      };

      // data is already flattened (because of $unwind)
      return {
        ...metadata, // { count, page, total_wallet_requests }
        data: result.data,
      };
    } catch (error) {
      // Handle error as needed
      return {
        count: 0,
        page: pagination.page || 1,
        total_wallet_requests: 0,
        data: [],
      };
    }
  }

  async getWithdrawalProvider(currency: CURRENCIES, withdrawal_account: string) {
    const config = await this.brokerTransport
      .send<AdminConfig | undefined>(BROKER_PATTERNS.CONFIG.GET_CONFIG, ADMIN_CONFIG.WITHDRAWAL_PROVIDER)
      .toPromise();

    const provider = config?.value && currency === CURRENCIES.NGN ? config?.value : defaultWithdrawalProvider[currency];

    if (provider === WITHDRAWAL_PROVIDERS.SQUAD && currency === CURRENCIES.NGN) {
      const withdrawalAccount = await this.withdrawalAccountModel.findOne({ _id: withdrawal_account });
      const squadBank =
        squadBanks.find((b) => b.nipCode === withdrawalAccount.bank_code) ??
        squadBanks.find((b) => b.code === withdrawalAccount.bank_code);

      if (!squadBank) throw new PreconditionFailedException(`Withdrawal to this bank is unavailable at the moment`);

      const resolvedSquadAccount = await this.squad.lookupAccount({
        account_number: withdrawalAccount.account_number,
        bank_code: squadBank.code,
      });

      if (resolvedSquadAccount.error) {
        throw new PreconditionFailedException(`Withdrawal to this bank is unavailable at the moment`);
      }

      withdrawalAccount.squad_code = squadBank.code;
      await withdrawalAccount.save();
    }

    return provider as WITHDRAWAL_PROVIDERS;
  }

  async getWithdrawalRequestDetails(id: string) {
    const request = await this.withdrawalRequestModel
      .findById(id)
      .populate('withdrawal_account')
      .populate({
        path: 'wallet',
        populate: {
          path: 'store',
        },
      })
      .populate('user')
      .lean();

    if (!request) {
      throw new BadRequestException('Withdrawal request not found');
    }

    return request;
  }

  async markWithdrawalSuccessful(id: string) {
    const request = await this.withdrawalRequestModel.findById(id);

    if (!request) {
      throw new BadRequestException('Withdrawal request not found');
    }

    if (request.status !== WITHDRAWAL_STATUSES.PENDING) {
      throw new BadRequestException('Withdrawal request is not in pending status');
    }

    // Check if the provider is manual
    if (request.provider === WITHDRAWAL_PROVIDERS.MANUAL) {
      // For manual withdrawals, we need to call withdrawalSuccessful
      await this.withdrawalSuccessful({ reference: request.vendorReference });
      return { success: true, message: 'Manual withdrawal marked as successful' };
    } else {
      throw new BadRequestException('Only manual withdrawals can be marked as successful through this endpoint');
    }
  }

  async markWithdrawalFailed(id: string) {
    const request = await this.withdrawalRequestModel.findById(id);

    if (!request) {
      throw new BadRequestException('Withdrawal request not found');
    }

    if (request.status !== WITHDRAWAL_STATUSES.PENDING) {
      throw new BadRequestException('Withdrawal request is not in pending status');
    }

    // Mark the withdrawal as failed with internalFailure set to true
    await this.withdrawalFailed({ reference: request.vendorReference, internalFailure: true });
    return { success: true, message: 'Withdrawal marked as failed with internal failure' };
  }

  async migratePaymentIds() {
    const batchCount = 2000;
    const transactionsToUpdate = await this.transactionModel
      .find({ 'meta.payment_id': { $exists: false }, channel: TRANSACTION_CHANNELS.DIRECT_TRANSFER })
      .limit(batchCount)
      .lean();

    const updates: Promise<any>[] = [];

    for (let transaction of transactionsToUpdate) {
      if (transaction.meta?.payaza_transaction_id || transaction.meta?.squad_transaction_id) {
        const payment = await this.brokerTransport
          .send(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, {
            $or: [
              ...(transaction.meta?.payaza_transaction_id
                ? [{ vendorReference: transaction.meta.payaza_transaction_id }]
                : []),
              ...(transaction.meta?.squad_transaction_id
                ? [{ vendorReference: transaction.meta.squad_transaction_id }]
                : []),
            ],
          })
          .toPromise();

        if (payment) {
          updates.push(
            this.transactionModel
              .findByIdAndUpdate(transaction._id, { $set: { 'meta.payment_id': payment.id } })
              .exec(),
          );
        }
      }
    }

    await Promise.all(updates);
    return { updated: transactionsToUpdate.length };
  }

  async getPublicWithdrawalRequest(id: string) {
    const request = await this.withdrawalRequestModel
      .findById(id)
      .populate('withdrawal_account')
      .populate('user', 'name');

    if (!request || request.status !== WITHDRAWAL_STATUSES.SUCCESSFUL) {
      throw new NotFoundException('Withdrawal request not found');
    }

    const publicData = {
      id: request._id,
      currency: request.currency,
      amount: request.amount,
      status: request.status,
      fee: request.fee,
      created_at: request.createdAt,
      withdrawal_account: request.withdrawal_account,
      user: request.user,
    };

    return publicData;
  }

  async generateWithdrawalRequestPdf(id: string) {
    const url = process.env.CATLOG_WWW + '/receipts/pdf/withdrawals/' + id;
    const data = await generatePDFFromWebpage(url, 550, 900);

    if (data.pdf) {
      return data.pdf;
    } else {
      throw new BadRequestException("Couldn't generate transaction PDF");
    }
  }

  /**
   * Updates payment_count for all wallets based on their credit transactions
   * Processes wallets in batches of 2000
   */
  // async migrateWalletPaymentCounts() {
  //   this.logger.log('Starting wallet payment count migration');

  //   const batchSize = 2000;
  //   let skip = 0;
  //   let processedCount = 0;
  //   let updatedCount = 0;
  //   let hasMore = true;

  //   while (hasMore) {
  //     // Get a batch of wallets
  //     const wallets = await this.walletModel.find({}).limit(batchSize).skip(skip).select('_id payment_count').lean();

  //     if (wallets.length === 0) {
  //       hasMore = false;
  //       break;
  //     }

  //     // Process each wallet in the batch
  //     const updatePromises = wallets.map(async (wallet) => {
  //       // Count credit transactions for this wallet
  //       const creditTransactionCount = await this.transactionModel.countDocuments({
  //         wallet: wallet._id,
  //         type: TRANSACTION_TYPE.CREDIT,
  //         channel: { $in: [TRANSACTION_CHANNELS.DIRECT_TRANSFER, TRANSACTION_CHANNELS.INVOICE_PAYMENT] },
  //       });

  //       // Only update if necessary
  //       if (wallet.payment_count !== creditTransactionCount) {
  //         await this.walletModel.updateOne({ _id: wallet._id }, { $set: { payment_count: creditTransactionCount } });
  //         return true; // Wallet was updated
  //       }
  //       return false; // No update needed
  //     });

  //     // Wait for all updates in this batch to complete
  //     const results = await Promise.all(updatePromises);
  //     const batchUpdates = results.filter(Boolean).length;

  //     updatedCount += batchUpdates;
  //     processedCount += wallets.length;

  //     this.logger.log(`Processed ${processedCount} wallets, updated ${updatedCount}`);

  //     // Move to next batch
  //     skip += batchSize;
  //   }

  //   this.logger.log(`Migration complete: processed ${processedCount} wallets, updated ${updatedCount}`);
  //   return { processedCount, updatedCount };
  // }

  // async resendBusinessEmailCreatedEmails() {
  //   const emailsWithSuccessMessages = [...new Set(successEmails)];

  //   const users = await this.brokerTransport
  //     .send<User[]>(BROKER_PATTERNS.USER.GET_USERS, {
  //       data: { email: { $in: emailsWithSuccessMessages } },
  //       select: 'primary_store',
  //     })
  //     .toPromise();
  //   const sentEmailsStoreIds = users.map((user) => user.primary_store);
  //   const sentEmailsWallets = await this.walletModel.find({ store: { $in: sentEmailsStoreIds } });
  //   const sentEmailsWalletIds = sentEmailsWallets.map((wallet) => wallet._id);
  //   const accountsToNotify = await this.walletAccount.find({
  //     wallet: { $nin: sentEmailsWalletIds },
  //     account_number: { $exists: true },
  //     createdAt: {
  //       $gte: new Date('2024-08-23T00:00:00Z'),
  //       $lt: new Date('2024-08-24T00:00:00Z'),
  //     },
  //   });

  //   for (let index = 0; index < accountsToNotify.length; index++) {
  //     const account = accountsToNotify[index];
  //     const wallet = await this.walletModel.findById(account.wallet);
  //     const store = await this.brokerTransport
  //       .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: wallet.store })
  //       .toPromise();
  //     const user = await this.brokerTransport
  //       .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
  //       .toPromise();

  //     console.log({ email: user.email });
  //     await this.resend.sendEmail(BROKER_PATTERNS.MAIL.BUSINESS_ACCOUNT_CREATED, {
  //         to: user.email,
  //         subject: 'Your business account is here! 🎉',
  //         data: {
  //           name: user.name,
  //           store_name: store.name,
  //           account_name: account?.account_name ?? null,
  //           bank_name: account?.bank_name ?? null,
  //           bank_logo: bankLogos[account?.bank_code] ?? null,
  //           account_number: account?.account_number ?? null,
  //           app_link: process.env.CATLOG_APP,
  //         },
  //       })
  //       .toPromise();
  //   }

  //   return { totalUsers: accountsToNotify.length };
  // }

  /**
   * Update a wallet's currency
   * @param walletId ID of the wallet to update
   * @param currency New currency to set
   * @returns Updated wallet
   */
  async updateWalletCurrency(walletId: string, currency: CURRENCIES) {
    try {
      // First, check if the wallet exists and has zero balance
      const wallet = await this.walletModel.findById(walletId);

      if (!wallet) {
        throw new NotFoundException(`Wallet with ID ${walletId} not found`);
      }

      // Only allow updating the currency if balance is 0
      if (wallet.balance !== 0) {
        throw new BadRequestException(
          'Cannot change wallet currency when balance is not zero. Current balance: ' +
            wallet.balance / 100 +
            ' ' +
            wallet.currency,
        );
      }

      // Update the currency
      wallet.currency = currency;
      await wallet.save();

      return wallet;
    } catch (error) {
      this.logger.error(`Error updating wallet currency: ${error.message}`);
      throw error;
    }
  }
}
