import { InjectQueue, OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { v4 as uuidV4 } from 'uuid';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { WalletService } from './wallet.service';
import { Job, Queue } from 'bull';
import { TRANSACTION_CHANNELS, TRANSACTION_TYPE, TransactionMeta, TransactionSource } from './wallet.schema';
import { Types } from 'mongoose';
import { BadRequestException } from '@nestjs/common';
import { toCurrency } from '../../utils';
import { WITHDRAWAL_PROVIDERS, WITHDRAWAL_STATUSES, WithdrawalAccount } from './wallet.withdrawal.schema';
import {
  ACCOUNT_PROVIDER_TO_PAYMENT_PROVIDER_MAP,
  PAYMENT_METHODS,
  PAYMENT_PROVIDERS,
  PAYMENT_STATUS,
  PAYMENT_TYPES,
} from '../../enums/payment.enum';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Payment, PaymentDocument } from '../payment/payment.schema';
import { Store, StoreDocument } from '../store/store.schema';
import { User } from '../user/user.schema';
import { getDocId, millify, toNaira } from '../../utils/functions';
import { CatlogCreditsTransactions } from '../user/credits/credits.schema';
import { ONE_MINUTE, PAYMENTS_CREDIT_PERCENTAGE, WALLET_LIMITS, FREE_PAYMENT_THRESHOLD } from '../../utils/constants';
import { Errorable } from '../../utils/types';
import { CURRENCIES } from '../country/country.schema';
import { ADMIN_CONFIG, AdminConfig } from '../adminconfig/adminconfig.schema';
import { ACCOUNT_PROVIDERS } from './wallet.account.schema';
import { walletFeeCalculator } from '../../utils/fees';

export type WalletJob<T = any> = {
  type: string;
  payload: T;
};

export type CheckPaymentVolumeMilestone = {
  wallet_id: string;
  transaction_amount: number;
};

export type RequerySquadPayoutParams = {
  withdrawal_request_id: string;
};

export type WalletCharge = {
  charge: number;
  wallet_id: string;
  transaction_id: string;
};

export type WalletWithdrawal = {
  user_id: string;
  request_id: string;
  withdrawal_account: any;
  withdrawal_fee: number;
};

export type WalletSquadTransfer = {
  virtual_account_number;
  principal_amount;
  fee_charged;
  transaction_indicator;
  remarks;
  transaction_reference;
  customer_identifier;
  settled_amount;
  sender_name;
};

export type BankTransferPayload = {
  virtual_account_number: string;
  transaction_indicator: string;
  transaction_reference: string;
  customer_identifier: string;
  principal_amount: number;
  settled_amount: number;
  fee_charged: number;
  source: {
    name: string;
    account_number: string;
    bank: string;
    purpose: string;
    method: string;
  };
  meta: {
    squad_transaction_id?: string;
    payaza_transaction_id?: string;
  };
  provider: ACCOUNT_PROVIDERS.SQUAD | ACCOUNT_PROVIDERS.PAYAZA;
};

export type WalletPayazaTransfer = {
  transaction_reference;
  transaction_status;
  virtual_account_number;
  transaction_fee;
  amount_received;
  initiated_date;
  current_status_date;
  received_from;
};

export interface CurrencyConversionDebitPayload {
  walletId: string;
  amount: number;
  fee: number;
  channel: string;
  narration: string;
  meta: TransactionMeta;
  source: TransactionSource;
}

export interface CurrencyConversionCreditPayload {
  walletId: string;
  amount: number;
  fee: number;
  channel: string;
  narration: string;
  meta: TransactionMeta;
  source: TransactionSource;
}

@Processor(QUEUES.WALLET)
export class WalletQueue {
  constructor(private walletService: WalletService) {}

  @OnQueueActive()
  onActive(job: Job<WalletCharge>) {
    console.log(`Processing job ${job.id} of type ${job.name} with data ${JSON.stringify(job.data)}...`);
  }

  @OnQueueFailed()
  onFailed(job: Job, error: Error) {
    console.log(`Failed to process job ${job.id} of type ${job.name} with data, error: ${error.message}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job, result: any) {
    console.log(`Job ${job.id} of type ${job.name} successfully completed, result: ${result}`);
    job.remove().catch(console.log);
  }

  @Process({ name: QUEUES.WALLET, concurrency: 1 })
  async handleWalletJobs(job: Job<WalletJob>) {
    switch (job.data.type) {
      case JOBS.WALLET_BANK_PAYMENT:
        await this.processBankPayment(job);
        break;
      case JOBS.WALLET_SQUAD_PAYMENT:
        await this.squadPayment(job);
        break;
      case JOBS.WALLET_WITHDRAWAL:
        await this.withdrawFromWallet(job);
        break;
      case JOBS.PAYMENT_VOLUME_MILESTONE:
        await this.checkPaymentVolumeMilestone(job);
        break;
      case JOBS.REQUERY_SQUAD_PAYOUT:
        await this.requerySquadPayout(job);
        break;
      case JOBS.WALLET_CURRENCY_CONVERSION_DEBIT:
        await this.handleCurrencyConversionDebit(job);
        break;
      case JOBS.WALLET_CURRENCY_CONVERSION_CREDIT:
        await this.handleCurrencyConversionCredit(job);
        break;
      default:
      // do nothing
    }
  }

  async processBankPayment(job: Job<WalletJob<BankTransferPayload>>) {
    const payload = job.data.payload;
    const {
      provider,
      principal_amount,
      fee_charged,
      settled_amount,
      meta,
      virtual_account_number,
      transaction_reference,
      source,
    } = payload;

    if (provider === ACCOUNT_PROVIDERS.SQUAD && payload.transaction_indicator !== 'C') {
      return;
    }

    // Find existing transaction
    const metaKeys = Object.keys(meta);
    const queryKey = `meta.${metaKeys[0]}`;
    const tx = await this.walletService.transactionModel.findOne({
      [queryKey]: meta[metaKeys[0]],
    });

    if (tx) {
      console.log(`${provider} TRANSACTION HAS BEEN PREVIOUSLY PROCESSED`);
      job.remove().catch(console.log);
      return;
    }

    // Find account based on provider
    const account = await this.walletService.walletAccount.findOne({
      account_number: virtual_account_number,
      provider,
    });

    if (!account) {
      console.log(`NO MATCHING ACCOUNT FOUND FOR ${provider} PAYMENT`);
      job.remove().catch(console.log);
      return;
    }

    // Common processing logic for providers
    const wallet = await this.walletService.walletModel.findOne({ _id: account.wallet });

    if (!wallet) {
      console.log('WALLET DOES NOT EXIST');
      job.remove().catch(console.log);
      return;
    }

    const store = await this.walletService.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { wallet: account.wallet })
      .toPromise();

    if (!store) {
      console.log('Found a wallet that has no store. How?!'.toUpperCase());
      job.remove().catch(console.log);
      return;
    }

    const owner = await this.walletService.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    if (!owner) {
      console.log('Found a store that has no owner. How?!'.toUpperCase());
      job.remove().catch(console.log);
      return;
    }

    const senderName = source?.name;
    let fee = walletFeeCalculator(principal_amount, wallet.currency, store?.current_plan?.plan_type);

    if (!wallet?.meta?.has_crossed_free_threshold) {
      const paymentCount = await this.walletService.countCreditPayments(wallet.id);

      if (paymentCount <= FREE_PAYMENT_THRESHOLD) {
        fee = 0; //set fee to 0 if the wallet has not crossed 10 payments
      }
    }

    const newWallet = await this.walletService.creditHandler(
      account.wallet,
      principal_amount,
      fee,
      TRANSACTION_CHANNELS.DIRECT_TRANSFER,
      `Transfer from ${senderName}`,
      meta,
      source,
    );

    if (wallet.balance === newWallet.wallet.balance) return;

    const payment = await this.walletService.brokerTransport
      .send<PaymentDocument>(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, {
        type: PAYMENT_TYPES.TEST,
        store: getDocId(store),
        status: PAYMENT_STATUS.PENDING,
      })
      .toPromise();

    if (!!payment) {
      store.onboarding_steps.test_payment_made = true;
      payment.status = PAYMENT_STATUS.SUCCESS;

      await this.walletService.brokerTransport
        .send(BROKER_PATTERNS.PAYMENT.TEST_PAYMENT_PAID, {
          paymentId: payment.id,
          gateway_fee: fee_charged,
          gateway_amount_settled: settled_amount,
        })
        .toPromise();

      await this.walletService.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
          filter: { _id: store.id },
          update: store,
        })
        .toPromise();

      // await this.walletService.brokerTransport
      //   .send(BROKER_PATTERNS.WEBSOCKET.TARGETTED_MESSAGE, {
      //     id: 'TEST_PAYMENT.' + store.id,
      //     data: {
      //       event: 'TEST_PAYMENT.PAID',
      //       payload: payment,
      //       balance: newWallet.wallet.balance,
      //     },
      //   })
      //   .toPromise();

      this.walletService.brokerTransport
        .send(BROKER_PATTERNS.SSE.TARGETTED_MESSAGE, {
          id: 'TEST_PAYMENT.' + store.id,
          data: {
            event: 'TEST_PAYMENT_SUCCESSFUL',
            reference: payment.reference,
            payload: payment,
            balance: newWallet.wallet.balance,
          },
        })
        .toPromise();
    } else {
      const manuallyCreatedPayment = await this.walletService.brokerTransport
        .send<Payment>(BROKER_PATTERNS.PAYMENT.MANUALLY_RECORD_PAYMENT, {
          reference: transaction_reference,
          storeId: getDocId(store),
          amount: principal_amount,
          gateway_charge: fee_charged,
          gateway_amount_settled: settled_amount,
          currency: CURRENCIES.NGN,
          provider: ACCOUNT_PROVIDER_TO_PAYMENT_PROVIDER_MAP[provider],
          payment_type: PAYMENT_TYPES.WALLET,
          payment_method: PAYMENT_METHODS.DIRECT_TRANSFER,
        })
        .toPromise();

      await this.walletService.transactionModel.findByIdAndUpdate(getDocId(newWallet.transaction), {
        meta: { ...meta, payment_id: getDocId(manuallyCreatedPayment) },
      });
    }

    const emailPayload = {
      name: owner.name.split(' ')[0],
      received: toCurrency((principal_amount / 100).toString(), wallet.currency),
      source_name: senderName ? `${senderName}` : 'Someone',
      current_balance: toCurrency((newWallet.wallet.balance / 100).toString(), wallet.currency),
      fee: toCurrency(toNaira(fee), wallet.currency),
      credited: toCurrency((principal_amount / 100).toString(), wallet.currency),
      date: new Date().toDateString(),
      funds_link: process.env.CATLOG_APP,
    };

    this.walletService.resend.sendEmail(BROKER_PATTERNS.MAIL.TRANSFER_RECEIVED, {
      to: owner.email,
      subject: `${senderName ? senderName : 'Someone'} just paid you ${toCurrency(
        (principal_amount / 100).toString(),
        wallet.currency,
      )} 🤑`,
      data: emailPayload,
    });

    await this.walletService.brokerTransport
      .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        message: {
          title: "You've received some money 🤑",
          message: `${senderName ? senderName : 'Someone'} sent you ${toCurrency(
            (principal_amount / 100).toString(),
            wallet.currency,
          )} via your account`,
          path: `/dashboard`,
        },
        owner_only: false,
        store: store?.id,
      })
      .toPromise();

    // job.remove().catch(console.log);
    return;
  }

  // @Process(JOBS.WALLET_SQUAD_PAYMENT)
  async squadPayment(job: Job<WalletJob<WalletSquadTransfer>>) {
    const payload = job.data.payload;

    if (payload.transaction_indicator !== 'C') return;

    const principal_amount = parseFloat(payload.principal_amount) * 100;
    const fee_charged = parseFloat(payload.fee_charged) * 100;
    const settled_amount = parseFloat(payload.settled_amount) * 100;

    const remarks = payload.remarks;
    const transaction_reference = payload.transaction_reference;

    const customer_identifier = payload.customer_identifier;

    const tx = await this.walletService.transactionModel.findOne({
      meta: { squad_transaction_id: transaction_reference },
    });

    if (!!tx) {
      console.log('TRANSACTION HAS BEEN PREVIOUSLY PROCESSED');
      job.remove().catch(console.log);
      return;
    }

    const account = await this.walletService.walletAccount.findOne({ customer_identifier });

    if (!account) {
      console.log('NO MATCHING ACCOUNT FOUND FOR SQUAD PAYMENT');
      job.remove().catch(console.log);
      return;
    }

    const wallet = await this.walletService.walletModel.findOne({ _id: account.wallet });

    if (!wallet) {
      console.log('WALLET DOES NOT EXIST');
      job.remove().catch(console.log);
      return;
    }

    const store = await this.walletService.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { wallet: account.wallet })
      .toPromise();

    if (!store) {
      console.log('Found a wallet that has no store. How?!'.toUpperCase());
      job.remove().catch(console.log);
      return;
    }

    const owner = await this.walletService.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store.owner })
      .toPromise();

    if (!owner) {
      console.log('Found a store that has no owner. How?!'.toUpperCase());
      job.remove().catch(console.log);
      return;
    }

    const getSenderName = () => {
      const startString = 'transfer from ';
      let extractName = remarks.toLowerCase().substring(startString.length, remarks.toLowerCase().lastIndexOf(' |'));

      return extractName;
    };

    const senderName =
      payload?.sender_name ??
      getSenderName()
        .split(' ')
        .map((s: string) => s.toUpperCase())
        .join(' ');

    const newWallet = await this.walletService.creditHandler(
      account.wallet,
      principal_amount,
      0,
      TRANSACTION_CHANNELS.DIRECT_TRANSFER,
      `Transfer from ${senderName}`,
      {
        squad_transaction_id: transaction_reference,
      },
      { account_number: '', name: senderName, purpose: 'Inward Transfer', method: 'Direct Transfer' },
    );

    if (wallet.balance === newWallet.wallet.balance) return;

    const payment = await this.walletService.brokerTransport
      .send<PaymentDocument>(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, {
        type: PAYMENT_TYPES.TEST,
        store: getDocId(store),
        status: PAYMENT_STATUS.PENDING,
      })
      .toPromise();

    if (!!payment) {
      store.onboarding_steps.test_payment_made = true;
      payment.status = PAYMENT_STATUS.SUCCESS;

      await this.walletService.brokerTransport
        .send(BROKER_PATTERNS.PAYMENT.TEST_PAYMENT_PAID, {
          paymentId: payment.id,
          gateway_fee: fee_charged,
          gateway_amount_settled: settled_amount,
        })
        .toPromise();

      await this.walletService.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
          filter: { _id: store.id },
          update: store,
        })
        .toPromise();

      await this.walletService.brokerTransport
        .send(BROKER_PATTERNS.WEBSOCKET.TARGETTED_MESSAGE, {
          id: 'TEST_PAYMENT.' + store.id,
          data: {
            event: 'TEST_PAYMENT.PAID',
            payload: payment,
            balance: newWallet.wallet.balance,
          },
        })
        .toPromise();
    } else {
      await this.walletService.brokerTransport
        .send<Payment>(BROKER_PATTERNS.PAYMENT.MANUALLY_RECORD_PAYMENT, {
          reference: transaction_reference,
          storeId: getDocId(store),
          amount: principal_amount,
          gateway_charge: fee_charged,
          gateway_amount_settled: settled_amount,
          customer: {
            name: senderName,
            email: '',
            phone: '',
            id: '',
          },
          currency: CURRENCIES.NGN,
          provider: PAYMENT_PROVIDERS.SQUAD,
          payment_type: PAYMENT_TYPES.WALLET,
          payment_method: PAYMENT_METHODS.DIRECT_TRANSFER,
        })
        .toPromise();
    }

    this.walletService.resend.sendEmail(BROKER_PATTERNS.MAIL.TRANSFER_RECEIVED, {
      to: owner.email,
      subject: `${senderName ? senderName : 'Someone'} just paid you ${toCurrency(
        (principal_amount / 100).toString(),
        wallet.currency,
      )} 🤑`,
      data: {
        name: owner.name.split(' ')[0],
        received: toCurrency((principal_amount / 100).toString(), wallet.currency),
        source_name: senderName ? `${senderName}` : 'Someone',
        current_balance: toCurrency((newWallet.wallet.balance / 100).toString(), wallet.currency),
        fee: toCurrency(String(0), wallet.currency),
        credited: toCurrency((principal_amount / 100).toString(), wallet.currency),
        date: new Date().toDateString(),
        funds_link: process.env.CATLOG_APP,
      },
    });

    await this.walletService.brokerTransport
      .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        message: {
          title: "You've received some money 🤑",
          message: `${senderName ? senderName : 'Someone'} sent you ${toCurrency(
            (principal_amount / 100).toString(),
            wallet.currency,
          )} via your account`,
          path: `/dashboard`,
        },
        owner_only: false,
        store: store?.id,
      })
      .toPromise();

    // job.remove().catch(console.log);
    return;
  }

  // @Process(JOBS.WALLET_WITHDRAWAL)
  async withdrawFromWallet(job: Job<WalletJob<WalletWithdrawal>>) {
    console.log('PROCESSING WITHDRAWAL');
    const payload = job.data.payload;
    const request = await this.walletService.withdrawalRequestModel.findOne({
      _id: payload.request_id,
      user: payload.user_id,
    });

    const withdrawalAccount = payload.withdrawal_account as WithdrawalAccount;
    const withdrawalFee = payload.withdrawal_fee;

    //ATTEMPT TO CHARGE WALLET
    const wallet = await this.walletService.walletModel.findOneAndUpdate(
      {
        _id: request.wallet,
        balance: {
          $gte: request.amount + withdrawalFee,
        },
      },
      {
        $inc: {
          balance: -1 * (request.amount + withdrawalFee),
        },
      },
      { new: true },
    );

    if (!wallet) {
      console.log('CANT PROCESS WITHDRAWAL, NO WALLET OR LESS BALANCE'.toUpperCase());
      job.remove().catch(console.log);
      return;
    }

    let response: Errorable<any> = undefined;
    const provider = request?.provider;

    if (provider == WITHDRAWAL_PROVIDERS.BLOCHQ && request.currency === CURRENCIES.NGN) {
      response = this.walletService.blochq.singleTransfer(
        request.amount,
        withdrawalAccount.account_number,
        withdrawalAccount.bank_code,
        request.vendorReference,
        `Withdrawal to ${withdrawalAccount.account_name}`,
      );
    } else if (provider == WITHDRAWAL_PROVIDERS.SQUAD && request.currency === CURRENCIES.NGN) {
      response = this.walletService.squad.initiateTransfer({
        transaction_reference: request.vendorReference,
        account_name: withdrawalAccount.account_name,
        account_number: withdrawalAccount.account_number,
        amount: request.amount,
        bank_code: withdrawalAccount.squad_code,
        remark: `Withdrawal to ${withdrawalAccount.account_name}`,
      });
    } else if (provider === WITHDRAWAL_PROVIDERS.STARTBUTTON) {
      response = this.walletService.startbutton.initiateTransfer({
        amount: request.amount,
        account_name: withdrawalAccount.account_name,
        account_number: withdrawalAccount.account_number,
        bank_code: withdrawalAccount.bank_code,
        currency: request.currency,
        reference: request.vendorReference,
      });
    } else {
      response = this.walletService.paystack.singleTransfer(
        request.amount,
        withdrawalAccount.recipient_id,
        `Withdrawal to ${withdrawalAccount.account_name}`,
        request.currency as CURRENCIES,
        request.vendorReference,
      );
    }

    let res = await response;

    //checking for status codes is not ideal but paystack sucks
    if (res.error) {
      if ((res.status === 424 || res.status > 499) && request.provider === WITHDRAWAL_PROVIDERS.SQUAD) {
        this.walletService.logger.log(`WITHDRAWAL REQUEST TIMED OUT OR ERROR ${request?.id}`);
        this.walletService.logger.log({
          id: request.id,
          account: withdrawalAccount,
          wallet: request.wallet,
          amount: request.amount,
          provider: 'SQUAD',
        });

        await this.walletService.walletQueue.add(
          QUEUES.WALLET,
          {
            type: JOBS.REQUERY_SQUAD_PAYOUT,
            payload: {
              withdrawal_request_id: request.id,
            },
          },
          { delay: ONE_MINUTE * 5 },
        );
      } else if (res.status === 504) {
        this.walletService.logger.log(`WITHDRAWAL REQUEST TIMED OUT: REQUEST ID ${request?.id}`);
        this.walletService.logger.log({
          id: request.id,
          account: withdrawalAccount,
          wallet: request.wallet,
          amount: request.amount,
        });
      } else {
        wallet.balance += request.amount + withdrawalFee;
        await wallet.save();
        request.status = WITHDRAWAL_STATUSES.FAILED;
        request.fee = withdrawalFee;
        await request.save();

        //NOTIFY USER THAT WITHDRAWAL FAILED
        await this.walletService.sendFailedWithdrawalNotification(wallet, request);
        console.log('REQUEST TO PAYSTACK FAILED'.toUpperCase());
        job.remove().catch(console.log);
        // throw new BadRequestException('Could not process withdrawal, please try again later.');
        return;
      }
    }

    new this.walletService.transactionModel({
      amount: request.amount,
      type: TRANSACTION_TYPE.DEBIT,
      channel: TRANSACTION_CHANNELS.WITHDRAWAL,
      narration: `Withdrawal to ${withdrawalAccount.account_name}`,
      balance_before: wallet.balance + request.amount + withdrawalFee,
      reference: request.vendorReference,
      wallet: wallet._id,
      meta: {
        withdrawal_request: request._id,
      },
      source: {
        name: withdrawalAccount.account_name,
        account_number: withdrawalAccount.account_number,
        method: 'Paystack',
        purpose: 'Withdrawal',
      },
      fee: withdrawalFee,
      currency: wallet.currency,
    }).save();

    request.fee = withdrawalFee;
    request.status = WITHDRAWAL_STATUSES.PENDING;
    await request.save();

    if (request.provider === WITHDRAWAL_PROVIDERS.SQUAD && res.status !== 424) {
      await this.walletService.withdrawalSuccessful({
        reference: request.vendorReference,
        nipReference: res?.data?.nip_transaction_reference,
      });
    }

    // job.remove().catch(console.log);
    return;
  }

  async checkPaymentVolumeMilestone(job: Job<WalletJob<CheckPaymentVolumeMilestone>>) {
    const milestones = [100_000, 500_000, 1_000_000, 5_000_000, 10_000_000, 50_000_000, 100_000_000];
    const {
      type,
      payload: { wallet_id, transaction_amount },
    } = job.data;

    const wallet = await this.walletService.walletModel.findOne({ _id: wallet_id }).populate('store');
    const dbUser = await this.walletService.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, {
        _id: (wallet.store as Store).owner,
      })
      .toPromise();

    // const currentTotalVolume = await this.walletService.transactionModel
    //   .find({ wallet: wallet_id, type: TRANSACTION_TYPE.CREDIT })
    //   .then((transactions) =>
    //     transactions.reduce((accum, transaction) => {
    //       accum += transaction.amount;
    //       return accum;
    //     }, 0),
    //   );

    const result = await this.walletService.transactionModel
      .aggregate([
        {
          $match: {
            type: 'credit',
            $or: [{ wallet: wallet_id }, { wallet: Types.ObjectId(wallet_id) }],
          },
        },
        {
          $group: {
            _id: null,
            total: {
              $sum: '$amount',
            },
          },
        },
      ])
      .exec();

    const totalVolume = result[0]?.total as number;

    if (!wallet.has_completed_kyc) {
      const hasPassedLimit =
        totalVolume >= (wallet.limits?.collection_limit || WALLET_LIMITS[wallet.currency].collection_limit);

      if (hasPassedLimit) {
        await this.walletService.brokerTransport
          .send<StoreDocument>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
            filter: {
              _id: (wallet.store as Store)._id,
            },
            update: {
              payments_enabled: false,
            },
          })
          .toPromise();

        //TODO: SEND EMAIL NOTIFICATIONS
      }
    }

    const previousVolume = toNaira(totalVolume - transaction_amount);

    for (let index = 0; index < milestones.length; index++) {
      const m = milestones[index];

      if (previousVolume < m && toNaira(totalVolume) >= m) {
        const millifiedValue = millify(m);

        const bonusAmount = (m - (index > 0 ? milestones[index - 1] : 0)) * PAYMENTS_CREDIT_PERCENTAGE;

        await this.walletService.brokerTransport
          .send<CatlogCreditsTransactions>(BROKER_PATTERNS.USER.CREDITS.ADD_CREDITS, {
            user_id: dbUser?.id,
            amount: bonusAmount,
            meta: {
              timestamp: Date.now(),
            },
            narration: `You crossed ${millifiedValue} in payments`,
          })
          .toPromise();

        await this.walletService.brokerTransport
          .send<StoreDocument>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
            filter: {
              _id: (wallet.store as Store)._id,
            },
            update: {
              $push: { 'milestones.store_payments': { milestone: m, date: new Date() } },
            },
          })
          .toPromise();

        await this.walletService.resend.sendEmail(BROKER_PATTERNS.MAIL.PAYMENTS_MILESTONE, {
          to: dbUser.email,
          subject: `🥳 You’ve crossed ${millifiedValue} in payments collected!`,
          data: {
            name: dbUser.name.split(' ')[0],
            value: millifiedValue,
            currency: wallet.currency,
          },
        });

        break;
      }
    }

    // job.remove().catch(console.log);
    return;
  }

  async requerySquadPayout(job: Job<WalletJob<RequerySquadPayoutParams>>) {
    const { payload } = job.data;
    const request = await this.walletService.withdrawalRequestModel.findById(payload.withdrawal_request_id);

    if (!request) {
      console.log('WITHDRAWAL REQUEST NOT FOUND');
      return;
    }

    const response = await this.walletService.squad.requeryTransfer({ transaction_reference: request.vendorReference });

    const responseDescription = response.data?.data?.response_description;
    const payoutSuccessful = responseDescription && this.checkSquadPayoutStatus(responseDescription);
    const payoutFailed =
      response?.error &&
      response?.data &&
      ((response?.data as any)?.message?.toLocaleLowerCase().includes('reversed') ||
        (response?.data as any)?.message?.toLocaleLowerCase().includes('failed'));

    if (payoutSuccessful) {
      await this.walletService.withdrawalSuccessful({
        reference: request.vendorReference,
        nipReference: response.data?.data?.nip_transaction_reference,
      });
    } else if (payoutFailed || response?.status === 404) {
      await this.walletService.withdrawalFailed({ reference: request.vendorReference });
    } else {
      //re-schedule another check
      await this.walletService.walletQueue.add(
        QUEUES.WALLET,
        {
          type: JOBS.REQUERY_SQUAD_PAYOUT,
          payload: {
            withdrawal_request_id: request.id,
          },
        },
        { delay: ONE_MINUTE * 5 },
      );
    }

    return;
  }

  checkSquadPayoutStatus(responseDescription: string) {
    return (
      responseDescription?.toLocaleLowerCase().includes('success') ||
      responseDescription?.toLocaleLowerCase().includes('approve')
    );
  }

  private async handleCurrencyConversionDebit(job: Job<WalletJob<CurrencyConversionDebitPayload>>) {
    const payload = job.data.payload;
    const { walletId, amount, fee, channel, narration, meta, source } = payload;

    try {
      const result = await this.walletService.debitHandler(walletId, amount, channel, narration, meta, source, fee);

      if (!result.success) {
        throw new Error(result.message);
      }
    } catch (error) {
      // Handle error
      throw error;
    }
  }

  private async handleCurrencyConversionCredit(job: Job<WalletJob<CurrencyConversionCreditPayload>>) {
    const payload = job.data.payload;
    const { walletId, amount, fee, channel, narration, meta, source } = payload;

    try {
      const result = await this.walletService.creditHandler(walletId, amount, fee, channel, narration, meta, source);

      if (!result.success) {
        throw new Error(result.message);
      }
    } catch (error) {
      // Handle error
      throw error;
    }
  }
}
