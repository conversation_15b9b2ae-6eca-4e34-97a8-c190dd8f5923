import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { Model } from 'mongoose';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { TransactionMeta, TransactionSource, WalletDocument } from './wallet.schema';
import { WalletService } from './wallet.service';
import { PaginatedQueryDto, SearchTransactionsDto } from './dtos/search.dto';
import { WITHDRAWAL_PROVIDERS } from './wallet.withdrawal.schema';
import { SkipThrottle } from '@nestjs/throttler';
import { CurrencyConversionService } from './currency-conversion/currency-conversion.service';
import { CURRENCIES } from '../country/country.schema';

@SkipThrottle()
@Controller()
export class WalletBroker {
  constructor(
    private readonly walletService: WalletService,
    private readonly currencyConversionService: CurrencyConversionService,
  ) {}

  @MessagePattern(BROKER_PATTERNS.WALLET.INITIATE_WALLET)
  async initiateWallet(data: { storeId: string }) {
    return await this.walletService.initializeWallet(data.storeId);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.CREATE_WALLET)
  async createWallet(data: { storeId: string; isAdmin: boolean }) {
    return await this.walletService.createWallet(data.storeId, data.isAdmin === true);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.GET_WALLET)
  async getWallet(data: { storeId: string }) {
    return await this.walletService.getWalletByStoreId(data.storeId);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.GET_ACCOUNTS)
  async getAccounts(walletId: string) {
    return await this.walletService.getAccounts(walletId);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.GET_WALLETS)
  async getWallets(filter: any) {
    return await this.walletService.getWallets(filter);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.CREDIT)
  async creditWallet(data: {
    walletId: string;
    amount: number;
    channel: string;
    narration: string;
    fee: number;
    meta: TransactionMeta;
    source: TransactionSource;
  }) {
    return this.walletService.creditHandler(
      data.walletId,
      data.amount,
      data.fee,
      data.channel,
      data.narration,
      data.meta,
      data.source,
    );
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.WITHDRAWAL_RESPONSE)
  async paystackTransfer(data: { reference: string; success: boolean }) {
    if (data.success) {
      return await this.walletService.withdrawalSuccessful({
        reference: data.reference,
      });
    } else {
      return await this.walletService.withdrawalFailed({ reference: data.reference });
    }
  }

  // @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYSTACK_WITHDRAWAL)
  // async paystackTransfer(data: { reference: string; success: boolean; fee_charged: number }) {
  //   if (data.success) {
  //     return await this.walletService.withdrawalSuccessful({
  //       reference: data.reference,
  //     });
  //   } else {
  //     return await this.walletService.withdrawalFailed({ reference: data.reference });
  //   }
  // }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYSTACK_REVERSAL)
  async paystackReversal(data: { narration: string; recipient_id: string; amount: string; reference: string }) {
    return await this.walletService.withdrawalFailed({ reference: data.reference });
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.REFUND_PAYMENT_TO_WALLET)
  async refundPaymentToWallet(data: {
    narration: string;
    paymentReference: string;
    storeId: string;
    deductAmount?: number;
  }) {
    return await this.walletService.refundPaymentToWallet(
      data.storeId,
      data.paymentReference,
      data.narration,
      data?.deductAmount ?? 0,
    );
  }

  // @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYSTACK_FAILED)
  // async paystackFailed(data: {narration: string, recipient_id: string, amount: string, reference: string}) {
  //     return await this.walletService.paystackFailed(data)
  // }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.BLOCHQ_WITHDRAWAL)
  async blochqWithdrawal(data: { reference: string; success: boolean }) {
    if (data.success) {
      return await this.walletService.withdrawalSuccessful({
        reference: data.reference,
      });
    } else {
      return await this.walletService.withdrawalFailed({ reference: data.reference });
    }
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.SQUAD_TRANSFER_RECIEVED)
  async squadTransfer(data: {
    virtual_account_number: string;
    principal_amount: string;
    fee_charged: string;
    transaction_indicator: 'C' | 'D';
    remarks: string;
    transaction_reference: string;
    customer_identifier: string;
    settled_amount: string;
  }) {
    return await this.walletService.squadTransfer(data);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.PAYAZA_TRANSFER_RECEIVED)
  async payazaTransfer(data: {
    transaction_reference: string;
    transaction_status: string;
    virtual_account_number: string;
    transaction_fee: string;
    amount_received: string;
    initiated_date: string;
    current_status_date: string;
    received_from: {
      account_name: string;
      account_number: string;
      bank_name: string;
    };
  }) {
    return await this.walletService.payazaTransfer(data);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.GET_SETUP_PROGRESS)
  async countCreditTransactions(wallet: string) {
    const data = await this.walletService.getSetupProgress(wallet);

    return data;
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.GET_TRANSACTIONS)
  async getTransactions(data: {
    storeId: string;
    walletId: string;
    filter: SearchTransactionsDto;
    query: PaginatedQueryDto;
  }) {
    return await this.walletService.getWalletTransactionsFromStore(
      data.storeId,
      data.walletId,
      data.filter,
      data.query,
    );
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.GET_TRANSACTION)
  async getTransaction(query: any) {
    return await this.walletService.transactionModel.findOne(query);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.MIGRATE_SQUAD_PAYMENTS)
  async migrateSquadPayments() {
    return await this.walletService.migrateAllSquadTransactionsToPayments();
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.ADD_TRANSFER_FEES)
  async addTransferFees() {
    return await this.walletService.addGatewayFeeToTransfers();
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.DEBIT_WALLET)
  async debitWallet({ walletId, amount, channel, narration, meta, source, fee = 0 }) {
    return await this.walletService.debitHandler(walletId, amount, channel, narration, meta, source, fee);
  }

  @MessagePattern(BROKER_PATTERNS.PAYMENT.WEBHOOKS.FINCRA_CONVERSION)
  async fincraConversion(data: any) {
    return await this.currencyConversionService.handleFincraWebhookEvent(data);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.GET_EXCHANGE_RATES)
  async getExchangeRates() {
    return await this.currencyConversionService.getAllRates();
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.GET_EXCHANGE_RATE)
  async getExchangeRatesById(id: string) {
    return await this.currencyConversionService.getRatesById(id);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.AUTO_SEND_FUNDS_TO_ACCOUNT)
  async autoSendFundsToAccount(transaction) {
    return await this.walletService.autoSendTxToBank(transaction);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.COUNT_CREDIT_PAYMENTS)
  async countCreditPayments(data: { walletId: string }) {
    return await this.walletService.countCreditPayments(data.walletId);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.UPDATE_WALLET_CURRENCY)
  async updateWalletCurrency(data: { walletId: string; currency: CURRENCIES }) {
    return await this.walletService.updateWalletCurrency(data.walletId, data.currency);
  }

  @MessagePattern(BROKER_PATTERNS.WALLET.GET_MILESTONES)
  async getMilestones({ wallet_id }) {
    return await this.walletService.getMilestones(wallet_id);
  }
}
