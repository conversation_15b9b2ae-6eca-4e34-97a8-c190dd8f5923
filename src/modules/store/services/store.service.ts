import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ConflictException,
  PreconditionFailedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { FilterQuery, MongooseFilterQuery, PaginateModel, Types, Model } from 'mongoose';

import { IStoreCategory, Store, STORE_TYPES, StoreDocument } from '../store.schema';

import { JwtService } from '@nestjs/jwt';
import dayjs from 'dayjs';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PLAN_TYPE } from '../../../enums/plan.enum';
import { PaginatedStoreAnalysisQueryDto, PaginatedStoreQueryDto } from '../../../models/dtos/PaginatedDto';
import { CreateStoreDto, CreateStoreDtoV2, FilterStoreDto, UpdateStoreDto } from '../../../models/dtos/StoreDtos';
import { arrayToMap, createSubdomainURL, getDocId, millify } from '../../../utils/functions';
import { Wallet } from '../../wallets/wallet.schema';
import { Account, ACCOUNT_PROVIDERS } from '../../wallets/wallet.account.schema';
import { SlackRepository } from '../../../repositories/slack.respository';
import { actionIsAllowed, formatPhoneNumber, genChars, sluggify, stripUnderScoreId } from '../../../utils';
import { STORE_PUBLIC_HIDDEN_INFO } from '../../../utils/constants';
import { PAID_FEATURES } from '../../../utils/features';
import {
  Country,
  COUNTRY_CODE,
  COUNTRY_CURRENCY_MAP,
  COUNTRY_FLAG_EMOJIS,
  CURRENCIES,
} from '../../country/country.schema';
import { Item, ItemDocument } from '../../item/item.schema';
import { Plan } from '../../plan/plan.schema';
import { Subscription } from '../../subscription/subscription.schema';
import { User } from '../../user/user.schema';
import { DeliveryAreaService } from '../delivery-areas/delivery-areas.service';
import BaseStoreService from './base.service';
import { BUSINESS_CATEGORIES, CATEGORY_NAMES } from '../utils/store-categories';
import { DELIVERY_PROVIDERS } from '../../../enums/deliveries';
import { Order, ORDER_STATUSES } from '../../orders/order.schema';
import { PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import * as fs from 'fs';
import * as JSONStream from 'JSONStream';
import * as es from 'event-stream';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';
import { WalletWithAccounts } from '../../wallets/wallet.service';
import { ApiGuardConfig } from '../../../config/types/api-guard.config';
import { ConfigService } from '@nestjs/config';
import { CustomerIoRepository } from '../../../repositories/customer-io.repository';
import { CurrencyRates } from '../../wallets/currency-conversion/currency-rate.schema';
import { ResendRepository } from '../../../repositories/resend.repository';
import { ItemService } from '../../item/item.service';
import { CartService } from '../../cart/cart.service';
import { CartDocument } from '../../cart/cart.schema';
import { AnalyticService } from '../../analytic/analytic.service';
import { AnalyticDocument } from '../../analytic/analytic.schema';
import { OrdersService } from '../../orders/orders.service';
import { Filter } from 'aws-sdk/clients/devicefarm';
import { PaymentGettersService } from '../../payment/services/payment.getters.service';
import { Payment } from '../../payment/payment.schema';
import { CustomerService } from '../../orders/customers/customer.service';
import { Customer } from '../../orders/customers/customer.schema';
import { StoreDomain, StoreDomainDocument } from '../domains/store-domain.schema';
import axios from 'axios';
import { HostApiRepository } from '../../../repositories/host-api.repository';
import { DomainService } from '../domains/domain.service';
import { ModuleRef } from '@nestjs/core';
import { SCOPES } from '../../../utils/permissions.util';

@Injectable()
export class StoreService extends BaseStoreService {
  constructor(
    @InjectModel(Store.name) protected readonly storeModel: PaginateModel<StoreDocument>,
    @InjectModel(StoreDomain.name) private readonly storeDomainModel: Model<StoreDomainDocument>,
    private readonly itemService: ItemService,
    private readonly cartService: CartService,
    private readonly analyticService: AnalyticService,
    private readonly ordersService: OrdersService,
    private readonly customerService: CustomerService,
    private readonly paymentGetterService: PaymentGettersService,
    protected readonly logger: Logger,
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly jwtService: JwtService,
    protected readonly deliveryArea: DeliveryAreaService,
    protected readonly resend: ResendRepository,
    private readonly slack: SlackRepository,
    private readonly customerIo: CustomerIoRepository,
    private readonly config: ConfigService,
    private readonly hostApiRepository: HostApiRepository,
    private readonly moduleRef: ModuleRef,
  ) {
    super(storeModel, brokerTransport);
  }

  async create(user: User, storeReq: CreateStoreDto, hasSubscription: boolean = false) {
    const { slug } = await this.generateSlug(storeReq.name);
    const userId = user.id;
    let subscription = null;

    const userFirstStore = user?.primary_store ? await this.storeModel.findById(user.primary_store) : null;
    const oldStores = [...user.stores];
    const userHasStores = userFirstStore && oldStores.length > 0 && String(userFirstStore?.owner) === String(user.id);
    const copyStoreConfig = userHasStores && storeReq?.copy_config;

    const usesChowbot = storeReq?.store_type === STORE_TYPES.RESTAURANT;

    let store = await new this.storeModel({
      ...storeReq,
      slug,
      primary_slug: slug,
      slugs: [slug],
      owner: userId,
      subscription: hasSubscription ? user?.subscription?._id : undefined,
      logo: storeReq.logo || '',
      checkout_channels: {
        whatsapp: [
          {
            id: genChars(8) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(10),
            label: 'Main',
            phone: storeReq.phone,
            type: 'WHATSAPP',
          },
        ],
      },
      currencies: copyStoreConfig
        ? { ...userFirstStore?.currencies }
        : {
            default: COUNTRY_CURRENCY_MAP[storeReq.country as COUNTRY_CODE],
            products: COUNTRY_CURRENCY_MAP[storeReq.country as COUNTRY_CODE],
            storefront: [COUNTRY_CURRENCY_MAP[storeReq.country as COUNTRY_CODE]],
            storefront_default: COUNTRY_CURRENCY_MAP[storeReq.country as COUNTRY_CODE],
            rates: null,
          },
      flags: {
        uses_chowbot: usesChowbot,
      },
      business_category: {
        name: usesChowbot ? CATEGORY_NAMES.food : '',
        product_types: [],
      },
      configuration: copyStoreConfig
        ? { ...userFirstStore.configuration }
        : {
            view_modes: {
              grid: true,
              card: true,
              horizontal: true,
              default: 'grid',
            },
            hours: null,
            fb_pixel: '',
            ga_id: '',
            custom_message: '',
            direct_checkout_enabled: true,
            sort_by_latest_products: false,
            show_unavailable_products: true,
            customer_pickup_enabled: false,
            color: '',
            color_cache: null,
            whatsapp_checkout_enabled: true,
            facebook_pixel_enabled: false,
            facebook_pixel_enabled_cache: null,
            require_delivery_info: true,
            confirm_order_before_payment: usesChowbot ? true : false,
            pickup_address: '',
            average_delivery_timeline: null,
            send_menu_on_initiation: false,
            menu_images: [],
            payment_validates_order: usesChowbot,
            require_geolocation: false,
          },
      extra_info: copyStoreConfig
        ? { ...userFirstStore.extra_info }
        : {
            delivery_timeline: '',
            production_timeline: '',
            refund_policy: '',
            images: [],
            images_label: '',
          },
      delivery_providers: copyStoreConfig
        ? [...userFirstStore.delivery_providers]
        : storeReq?.country === COUNTRY_CODE.NG
        ? [DELIVERY_PROVIDERS.SHIPBUBBLE, DELIVERY_PROVIDERS.FEZ_DELIVERY]
        : storeReq?.country === COUNTRY_CODE.GH
        ? [DELIVERY_PROVIDERS.SHAQ_EXPRESS]
        : [],
      public_access_tokens: copyStoreConfig ? { ...userFirstStore.public_access_tokens } : {},
      access_tokens: copyStoreConfig ? { ...userFirstStore?.access_tokens, instagram: null } : {},
      third_party_configs: copyStoreConfig ? { ...userFirstStore?.third_party_configs } : {},
    }).save();

    store = store.toFilteredJSON();

    user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.ADD_STORE, {
        id: userId,
        storeId: store.id,
        isPrimaryStore: !userHasStores,
      })
      .toPromise();

    //Create catlog credit wallet for user
    if (!userHasStores) {
      await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.REDEEM_REFERRAL_CREDITS, {
          userId,
          country: store.country,
        })
        .toPromise();
    }

    store.country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: store.country,
      })
      .toPromise();

    store.item_count = 0;

    // if (!userHasStores && process.env.NODE_ENV === 'production') {
    //   try {
    //     await this.slack?.sendSignUpNotification?.({
    //       name: user.name,
    //       country: store.country.name + ' ' + COUNTRY_FLAG_EMOJIS[store.country.code],
    //       phone: formatPhoneNumber(user.phone),
    //       whatsapp: formatPhoneNumber(user.phone).replace('+', ''),
    //       store_name: store.name,
    //       store_slug: store.slug,
    //     });
    //   } catch (e) {
    //     console.log('SLACK NOTIFICATION ERROR: ' + e);
    //   }
    // }

    //initialize store wallet
    await this.brokerTransport
      .send<Wallet>(BROKER_PATTERNS.WALLET.INITIATE_WALLET, {
        storeId: getDocId(store),
      })
      .toPromise();

    await this.customerIo.createOrUpdateUser({
      id: getDocId(user),
      email: user.email,
      store_name: store.name,
      store_link: createSubdomainURL(process.env.CATLOG_WWW, store.slug),
      store_country: storeReq.country,
      store_logo: store.logo,
      store_type: usesChowbot ? 'chowbot' : 'regular',
    });

    const response = { ...user, subscription, stores: [...oldStores, store] };

    return response;
  }

  async createStoreV2(user: User, storeReq: CreateStoreDtoV2) {
    console.log({ storeReq });
    let slug = storeReq.slug;

    if (slug) {
      const slugIsValid = await this.verifyStoreSlug(slug);

      if (slugIsValid.used) {
        throw new BadRequestException('Store link is already in use');
      }
    } else {
      const generatedSlug = await this.generateSlug(storeReq.name);
      slug = generatedSlug.slug;
    }

    const userId = user.id;
    let subscription = null;

    const usesChowbot = storeReq?.store_type === STORE_TYPES.RESTAURANT;

    let store = await new this.storeModel({
      ...storeReq,
      slug,
      primary_slug: slug,
      slugs: [slug],
      owner: userId,
      subscription: undefined,
      logo: '',
      checkout_channels: {
        whatsapp: [
          {
            id: genChars(8) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(10),
            label: 'Main',
            phone: storeReq.phone,
            type: 'WHATSAPP',
          },
        ],
      },
      currencies: {
        default: COUNTRY_CURRENCY_MAP[storeReq.country as COUNTRY_CODE],
        products: COUNTRY_CURRENCY_MAP[storeReq.country as COUNTRY_CODE],
        storefront: [COUNTRY_CURRENCY_MAP[storeReq.country as COUNTRY_CODE]],
        storefront_default: COUNTRY_CURRENCY_MAP[storeReq.country as COUNTRY_CODE],
        rates: null,
      },
      flags: {
        uses_chowbot: usesChowbot,
      },
      business_category: {
        name: usesChowbot ? CATEGORY_NAMES.food : '',
        product_types: [],
      },
      configuration: {
        view_modes: {
          grid: true,
          card: true,
          horizontal: true,
          default: 'grid',
        },
        hours: null,
        fb_pixel: '',
        ga_id: '',
        custom_message: '',
        direct_checkout_enabled: true,
        sort_by_latest_products: false,
        show_unavailable_products: true,
        customer_pickup_enabled: false,
        color: '',
        color_cache: null,
        whatsapp_checkout_enabled: true,
        facebook_pixel_enabled: false,
        facebook_pixel_enabled_cache: null,
        require_delivery_info: true,
        confirm_order_before_payment: usesChowbot ? true : false,
        pickup_address: '',
        average_delivery_timeline: null,
        send_menu_on_initiation: false,
        menu_images: [],
        payment_validates_order: true,
        payment_timeout: 15,
        require_geolocation: false,
        collect_order_notes: true,
        require_emails: true,
      },
      extra_info: {
        delivery_timeline: '',
        production_timeline: '',
        refund_policy: '',
        images: [],
        images_label: '',
      },
      delivery_providers:
        storeReq?.country === COUNTRY_CODE.NG
          ? [DELIVERY_PROVIDERS.SHIPBUBBLE, DELIVERY_PROVIDERS.FEZ_DELIVERY]
          : storeReq?.country === COUNTRY_CODE.GH
          ? [DELIVERY_PROVIDERS.SHAQ_EXPRESS]
          : [],
      public_access_tokens: {},
      access_tokens: {},
      third_party_configs: {},
    }).save();

    store = store.toFilteredJSON();

    user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.ADD_STORE, {
        id: userId,
        storeId: store.id,
        isPrimaryStore: true,
      })
      .toPromise();

    //Create catlog credit wallet for user
    await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.REDEEM_REFERRAL_CREDITS, {
        userId,
        country: store.country,
      })
      .toPromise();

    store.country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: store.country,
      })
      .toPromise();

    store.item_count = 0;

    //initialize store wallet
    await this.brokerTransport
      .send<Wallet>(BROKER_PATTERNS.WALLET.INITIATE_WALLET, {
        storeId: getDocId(store),
      })
      .toPromise();

    await this.customerIo.createOrUpdateUser({
      id: getDocId(user),
      email: user.email,
      store_name: store.name,
      store_link: createSubdomainURL(process.env.CATLOG_WWW, store.slug),
      store_country: storeReq.country,
      store_logo: store.logo,
      store_type: usesChowbot ? 'chowbot' : 'regular',
    });

    const response = { ...user, subscription, stores: [store] };

    return response;
  }

  getFeaturesToModify(key: PLAN_TYPE, disableFeatures = false): PAID_FEATURES[] {
    const ToDisable = {
      [PLAN_TYPE.STARTER]: [
        PAID_FEATURES.STORE_SLUGS,
        PAID_FEATURES.TEAMS_AND_MULTIPLE_STORES,
        PAID_FEATURES.COLOR_CUSTOMIZATION,
        PAID_FEATURES.KITCHEN,
        PAID_FEATURES.FACEBOOK_PIXEL,
      ],
      [PLAN_TYPE.BASIC]: [PAID_FEATURES.TEAMS_AND_MULTIPLE_STORES, PAID_FEATURES.KITCHEN, PAID_FEATURES.FACEBOOK_PIXEL],
      [PLAN_TYPE.BUSINESS_PLUS]: [PAID_FEATURES.KITCHEN],
    };
    const ToEnable = {
      [PLAN_TYPE.BUSINESS_PLUS]: [
        PAID_FEATURES.STORE_SLUGS,
        PAID_FEATURES.TEAMS_AND_MULTIPLE_STORES,
        PAID_FEATURES.COLOR_CUSTOMIZATION,
        PAID_FEATURES.FACEBOOK_PIXEL,
      ],
      [PLAN_TYPE.BASIC]: [PAID_FEATURES.STORE_SLUGS, PAID_FEATURES.COLOR_CUSTOMIZATION],
      [PLAN_TYPE.KITCHEN]: [
        PAID_FEATURES.KITCHEN,
        PAID_FEATURES.STORE_SLUGS,
        PAID_FEATURES.TEAMS_AND_MULTIPLE_STORES,
        PAID_FEATURES.COLOR_CUSTOMIZATION,
        PAID_FEATURES.FACEBOOK_PIXEL,
      ],
    };
    if (!disableFeatures) return ToEnable[key];
    return ToDisable[key];
  }

  async disableStoreFeatures(
    filter: FilterQuery<Store>,
    isPaidSubscription: boolean,
    toPlanKey?: PLAN_TYPE,
    planOption?: PlanOption,
  ) {
    const stores = await this.storeModel.find(filter).sort({ _id: 1 });

    if (stores.length === 0) {
      this.logger.error('store with owner does not exist');
      throw new BadRequestException('store with owner does not exist');
    }
    const featuresToDisable = this.getFeaturesToModify(toPlanKey, true);
    // const shouldDisableSlugs = featuresToDisable.includes(PAID_FEATURES.STORE_SLUGS);
    const shouldDisableStores = featuresToDisable.includes(PAID_FEATURES.TEAMS_AND_MULTIPLE_STORES);
    const shouldDisableColor = featuresToDisable.includes(PAID_FEATURES.COLOR_CUSTOMIZATION);
    const shouldDisableKitchen = featuresToDisable.includes(PAID_FEATURES.KITCHEN);
    const shouldDisableFacebookPixel = featuresToDisable.includes(PAID_FEATURES.FACEBOOK_PIXEL);

    for (let i = 0; i < stores.length; i++) {
      let store = stores[i];

      let updateData = {
        disabled_slugs: store.disabled_slugs,
        slug: store.slug,
        slugs: store.slugs,
        disabled: store.disabled,
        owners: store.owners,
        configuration: { ...store.configuration },
        has_paid_subscription: isPaidSubscription,
        third_party_configs: store?.third_party_configs,
        current_plan: {
          plan_type: planOption.plan_type,
          interval_text: planOption.interval_text,
          interval: planOption.interval,
        },
      };

      // if (shouldDisableSlugs) {
      //   const newSlug = await this.generateSlug();
      //   updateData.disabled_slugs = store.slugs;
      //   updateData.slug = newSlug;
      //   updateData.slugs = [newSlug];
      // }

      if (shouldDisableColor) {
        updateData.configuration.color_cache = store.configuration.color;
        updateData.configuration.color = '';
      }

      if (shouldDisableStores) {
        updateData.disabled = true;
        // updateData.disabled = i === 0 ? false : true;
        updateData.owners = store.owners?.map((o) => ({ ...o, disabled: true })) ?? [];
      }

      if (shouldDisableKitchen) {
        await this.brokerTransport
          .send<Item[]>(BROKER_PATTERNS.ITEM.UPDATE_STORE_ITEMS, {
            store: store._id,
            payload: {
              is_menu_item: false,
            },
          })
          .toPromise();

        if (
          store?.third_party_configs?.chowdeck?.sync_from_chowdeck ||
          store?.third_party_configs?.chowdeck?.sync_to_chowdeck
        ) {
          updateData.third_party_configs.chowdeck.sync_from_chowdeck = false;
          updateData.third_party_configs.chowdeck.sync_to_chowdeck = false;
        }
      }

      if (shouldDisableFacebookPixel) {
        updateData.configuration.facebook_pixel_enabled_cache = store.configuration.facebook_pixel_enabled;
        updateData.configuration.facebook_pixel_enabled = false;
      }

      stores[i] = await this.storeModel.updateOne({ _id: store._id }, updateData, { new: true });
    }

    return stores;
  }

  async enableStoreFeatures(
    filter: FilterQuery<Store>,
    isPaidSubscription: boolean,
    toPlanKey?: PLAN_TYPE,
    planOption?: PlanOption,
  ) {
    const stores = await this.storeModel.find(filter).sort({ _id: 1 });

    if (stores.length === 0) {
      this.logger.error('store with owner does not exist');
      throw new BadRequestException('store with owner does not exist');
    }

    const featuresToEnable = this.getFeaturesToModify(toPlanKey);
    const shouldEnableSlugs = featuresToEnable.includes(PAID_FEATURES.STORE_SLUGS);
    const shouldEnableStores = featuresToEnable.includes(PAID_FEATURES.TEAMS_AND_MULTIPLE_STORES);
    const shouldEnableColors = featuresToEnable.includes(PAID_FEATURES.COLOR_CUSTOMIZATION);
    const shouldEnableKitchen = featuresToEnable.includes(PAID_FEATURES.KITCHEN);
    const shouldEnableFacebookPixel = featuresToEnable.includes(PAID_FEATURES.FACEBOOK_PIXEL);

    for (let i = 0; i < stores.length; i++) {
      const store = stores[i];
      const updateData = {
        disabled_slugs: store.disabled_slugs,
        slug: store.slug,
        slugs: store.slugs,
        disabled: false,
        owners: store.owners,
        configuration: { ...store.configuration },
        has_paid_subscription: isPaidSubscription,
        current_plan: {
          plan_type: planOption.plan_type,
          interval_text: planOption.interval_text,
          interval: planOption.interval,
        },
      };

      if (shouldEnableSlugs) {
        updateData.disabled_slugs = [];
        updateData.slug =
          store.disabled_slugs.length > 0 ? store.disabled_slugs[store.disabled_slugs.length - 1] : store.slug;
        updateData.slugs = [...store.disabled_slugs, store.slug];
      }

      if (shouldEnableColors) {
        updateData.configuration.color = store.configuration.color_cache;
      }

      if (shouldEnableStores) {
        updateData.owners = store.owners?.map((o) => {
          return { ...o, disabled: false };
        }); //enable all store owners
      }

      if (shouldEnableKitchen && store?.flags?.uses_chowbot && store?.configuration?.send_menu_on_initiation) {
        await this.brokerTransport
          .send<Item[]>(BROKER_PATTERNS.ITEM.UPDATE_STORE_ITEMS, {
            store: store._id,
            payload: {
              is_menu_item: true,
            },
          })
          .toPromise();
      }

      if (shouldEnableFacebookPixel) {
        updateData.configuration.facebook_pixel_enabled = store.configuration.facebook_pixel_enabled_cache;
      }

      await this.storeModel
        .updateOne({ _id: store._id }, updateData, { new: true })
        .populate({ path: 'subscription', populate: 'plan' });
    }
    return stores;
  }

  // Get a single store using user id or store id
  async getWithId(id: string) {
    let store = await this.storeModel
      .findOne({
        $or: [{ owner: id as any }, { _id: id }],
      })
      .populate({ path: 'subscription', populate: 'plan' });

    if (!store) {
      return null;
    }

    store = await store.toJSON();
    store.country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: store.country,
      })
      .toPromise();

    // store.item_count = await this.brokerTransport
    //   .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, { store: store.id })
    //   .toPromise();

    store.has_paid_subscription = await this.getHasPaidSubscription(store);

    return store;
  }

  async getStore(data: mongoose.FilterQuery<StoreDocument>, select: string = '-non_existent_value') {
    return await this.storeModel
      .findOne(data, select)
      .populate({ path: 'subscription', populate: 'plan' })
      .populate('pickup_address');
  }

  async getStoreLean(filter: mongoose.FilterQuery<StoreDocument>, select: any) {
    return await this.storeModel
      .findOne(filter, select)
      .populate({ path: 'subscription', populate: 'plan' })
      .populate('pickup_address')
      .lean();
  }

  async paginateStoresLean(filter, count, page, select) {
    const options = {
      page,
      limit: count,
      select,
      lean: true,
    };

    return await this.storeModel.paginate(filter, options);
  }

  // async getAndValidateStore(storeId, projections = undefined): Promise<StoreDocument> {
  //   const store = await this.storeModel.findById(storeId, projections);

  //   if (!store) {
  //     throw new BadRequestException('Store with id does not exist');
  //   }
  //   return store;
  // }

  async getStoreBySlug(slug: string, xadmin?: string, password?: string) {
    slug = String(slug).toLowerCase();
    slug = slug.replace(/_/g, '-'); // Replace underscores with hyphens

    const infoToHide = STORE_PUBLIC_HIDDEN_INFO.replace(' -subscription', ''); //allow subscription to be fetched

    let store: StoreDocument & { is_admin?: boolean } = await this.getStore(
      {
        $or: [
          { disabled_slugs: { $all: [slug] } },
          { disabled_slug: { $eq: slug } },
          { $or: [{ slug }, { slugs: slug }] },
        ],
      },
      infoToHide,
    );

    if (!store) {
      throw new NotFoundException('Store is not found');
    }

    store = store.toFilteredJSON();
    store.delivery_areas = await this.getDeliveryAreas(store.delivery_areas as string[]);

    store.country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: store.country,
      })
      .toPromise();

    store.has_paid_subscription = await this.getHasPaidSubscription(store);

    store.highlights_config = {
      count: await this.brokerTransport
        .send<number>(BROKER_PATTERNS.ITEM.HIGHLIGHTS.COUNT_HIGHLIGHTS, {
          storeId: store._id,
          active: true,
        })
        .toPromise(),
      shouldShowHighlights: actionIsAllowed({
        plan: store.current_plan.plan_type,
        planPermission: SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_STORE_HIGHLIGHTS,
      }),
    };

    console.log({ highlights_config: store.highlights_config });

    // Add admin visibility logic
    const isAdmin = await this.verifyIsAdmin(xadmin, password);

    if (isAdmin) {
      store.is_admin = true;
      store.disabled = false;
    }

    const sc = store.currencies?.storefront; //storefront currency options
    const hasMultipleCurrencies = sc.length > 1 || (sc.length === 1 && sc[0] !== store.currencies?.products);

    if (hasMultipleCurrencies) {
      const rates = await this.brokerTransport
        .send<CurrencyRates>(BROKER_PATTERNS.WALLET.GET_EXCHANGE_RATES, {})
        .toPromise();
      store.meta = { rates } as any;
    }

    // Check if store items have embeddings and queue generation if needed
    this.checkAndQueueItemEmbeddings(getDocId(store)).catch((err) => {
      this.logger.error(`Error checking/queueing item embeddings: ${err.message}`);
    });

    const { subscription, ...data } = store;

    return data;
  }

  /**
   * Checks if store items have embeddings and queues generation for those that don't
   * This runs asynchronously so it doesn't block the response
   */
  private async checkAndQueueItemEmbeddings(storeId: string | Types.ObjectId): Promise<void> {
    try {
      // First check if we need to generate embeddings
      const itemsWithoutEmbeddings = await this.itemService.countItems({
        store: storeId as any,
        is_deleted: { $ne: true },
        $or: [{ embedding: { $exists: false } }, { embedding: null }],
      } as MongooseFilterQuery<ItemDocument>);

      if (itemsWithoutEmbeddings > 0) {
        this.logger.log(`Found ${itemsWithoutEmbeddings} items without embeddings in store ${storeId}`);

        // Use the batch embedding generation for better efficiency
        await this.brokerTransport
          .send<any>(BROKER_PATTERNS.ITEM.GENERATE_STORE_ITEM_EMBEDDINGS, {
            storeId: storeId.toString(),
            batchSize: 100, // Process in reasonable batches
            limit: 500, // Limit to 500 items per call to avoid overloading
          })
          .toPromise();

        this.logger.log(`Queued embedding generation for items in store ${storeId}`);
      }
    } catch (error) {
      this.logger.error(`Error checking for items without embeddings: ${error.message}`);
    }
  }

  async verifyIsAdmin(xadmin: string, password: string) {
    if (!xadmin || !password) return false;
    const apiGuardCfg = this.config.get<ApiGuardConfig>('apiGuardConfig');

    if (apiGuardCfg.username === xadmin && apiGuardCfg.password === password) return true;

    return false;
  }

  async getCategories(storeId) {
    const store = await this.getAndValidateStore(storeId);
    return await Promise.all(
      stripUnderScoreId<IStoreCategory[]>(store.toJSON().categories).map(async (category) => {
        category.items_count = await this.brokerTransport
          .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, {
            category: category.id,
            store: storeId,
          })
          .toPromise();
        return category;
      }),
    );
  }

  async getCategoriesWithFirstItem(storeId: string) {
    const store = await this.getAndValidateStore(storeId);

    // First get all categories
    const categories = stripUnderScoreId<IStoreCategory[]>(store.toJSON().categories);

    // Get first item from each category
    return await Promise.all(
      categories.map(async (category) => {
        try {
          // Get item count for the category
          category.items_count = await this.brokerTransport
            .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, {
              category: category.id,
              store: storeId,
            })
            .toPromise();

          // Get just the first item for this category using the optimized method
          const item = await this.itemService.getFirstItemForCategory(storeId, category.id);

          // If we have an item, add simplified version to the category
          if (item) {
            return {
              ...category,
              first_item: {
                id: item._id?.toString(),
                name: item.name,
                price: item.price,
                images: item.images || [],
                description: item.description,
                available: item.available,
              },
            };
          }
        } catch (error) {
          console.error(`Error getting first item for category ${category.name}:`, error);
        }

        return category;
      }),
    );
  }

  async getStatistics(storeId: string, filter?: any, isMobile?: boolean) {
    const from = dayjs(filter?.from);
    const to = dayjs(filter?.to);

    const trendTo = from.clone();
    const trendFrom = from.clone().subtract(to.diff(from, 'days'), 'days');

    // const total_items = await this.brokerTransport
    //   .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, {
    //     store: storeId,
    //   })
    //   .toPromise();

    const total_items = await this.itemService.countItems({ store: storeId } as MongooseFilterQuery<ItemDocument>);

    const getSingleStats = async (created_at) => {
      created_at = filter && created_at;

      // const total_carts = await this.brokerTransport
      //   .send<number>(BROKER_PATTERNS.CART.GET_TOTAL, {
      //     store: storeId,
      //     created_at,
      //   })
      //   .toPromise();

      const total_carts = await this.cartService.getTotal({
        store: storeId,
        created_at,
      } as MongooseFilterQuery<CartDocument>);

      // const visits = await this.brokerTransport
      //   .send(BROKER_PATTERNS.ANALYTICS.GET_STORE_VISITS, {
      //     store: storeId,
      //     item: null,
      //     created_at,
      //   })
      //   .toPromise();

      const visits = await this.analyticService.getStoreVisits({
        store: storeId,
        item: null,
        created_at,
      } as MongooseFilterQuery<AnalyticDocument>);

      // Conditionally add total_orders and total_payments if the request is from a mobile device
      let total_orders, total_payments;

      if (isMobile) {
        // total_orders = await this.brokerTransport
        //   .send(BROKER_PATTERNS.ORDER.COUNT_ORDERS, {
        //     store: Types.ObjectId(storeId),
        //     status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
        //     created_at,
        //   })
        //   .toPromise();

        total_orders = await this.ordersService.countOrders({
          store: storeId,
          status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
          created_at,
        } as FilterQuery<Order>);

        // total_payments = await this.brokerTransport
        //   .send(BROKER_PATTERNS.PAYMENT.GET_TOTAL_PAYMENTS, {
        //     store: Types.ObjectId(storeId),
        //     status: PAYMENT_STATUS.SUCCESS,
        //     $or: [
        //       { type: PAYMENT_TYPES.INVOICE },
        //       { type: PAYMENT_TYPES.WALLET, payment_method: PAYMENT_METHODS.DIRECT_TRANSFER },
        //     ],
        //     created_at,
        //   })
        //   .toPromise();

        total_payments = await this.paymentGetterService.getTotalPayments({
          store: storeId.toString(),
          status: PAYMENT_STATUS.SUCCESS,
          $or: [
            { type: PAYMENT_TYPES.INVOICE },
            { type: PAYMENT_TYPES.WALLET, payment_method: PAYMENT_METHODS.DIRECT_TRANSFER },
          ],
          created_at,
        } as FilterQuery<Payment>);
      }

      return {
        total_carts,
        total_items,
        visits,
        total_visits: visits.length,
        total_orders,
        total_payments,
      };
    };

    const currentData = await getSingleStats(this.getCreateAtFilter(from.toDate(), to.toDate()));
    const prevData = await getSingleStats(this.getCreateAtFilter(trendFrom.toDate(), trendTo.toDate()));

    const visits = currentData.visits;
    delete currentData.visits;

    const trends = {
      total_visits: this.calcPercentageDifference(prevData.total_visits, currentData.total_visits),
      total_carts: this.calcPercentageDifference(prevData.total_carts, currentData.total_carts),
      total_items: this.calcPercentageDifference(prevData.total_items, currentData.total_items),
      total_orders: this.calcPercentageDifference(prevData.total_orders, currentData.total_orders),
      total_payments: this.calcPercentageDifference(prevData.total_payments, currentData.total_payments),
    };

    return {
      ...{
        ...currentData,
        trends,
      },
      visits,
    };
  }

  private calcPercentageDifference(start: number, end: number): number {
    if (start === 0 || end === 0) {
      return start > end ? -100 : start === end ? 0 : 100;
    }
    return Math.floor((100.0 / start) * (end - start));
  }

  private getCreateAtFilter(from: Date, to: Date): { $gte: Date; $lt: Date } {
    return {
      $gte: from,
      $lt: to,
    };
  }

  async getMilestones(storeId: string) {
    type Milestone = {
      volume: number;
      total?: number;
    };
    const store = await this.storeModel.findById(storeId);
    if (store) {
      const orderMilestones = await this.brokerTransport
        .send<Milestone>(BROKER_PATTERNS.ORDER.GET_MILESTONES, {
          store: storeId,
          currency: store?.currencies?.default ?? 'NGN',
        })
        .toPromise();

      // console.log('<======================', orderMilestones,'', '=======================>');

      const walletId = store.wallet ?? store?.wallets?.find((w) => w.currency === store.currencies?.default);

      const paymentsMilestones = walletId
        ? await this.brokerTransport
            .send<Milestone>(BROKER_PATTERNS.WALLET.GET_MILESTONES, {
              wallet_id: getDocId(walletId),
            })
            .toPromise()
        : { volume: 0, total: 0 };

      const storeVisits = store.total_visits;

      return {
        total_store_visits: storeVisits ?? 0,
        total_payments_volume: paymentsMilestones?.volume ?? 0,
        total_orders: orderMilestones?.total ?? 0,
        total_orders_volume: orderMilestones?.volume ?? 0,
      };
    }
    throw new PreconditionFailedException('Store not found');
  }

  async getEmailSummaries(storeId: string, filter: any, period: 'week' | 'month') {
    try {
      const from = dayjs(filter.from);
      const to = dayjs(filter.to);

      const trendTo = from.clone();
      const trendFrom = from.clone().subtract(to.diff(from, 'days'), 'days');

      const store = await this.storeModel.findById(storeId);

      const getSingleSummary = async (created_at: { $gte: Date; $lt: Date }) => {
        const storeObjectId = Types.ObjectId(storeId);

        const [
          total_orders,
          total_payments_count,
          visits,
          new_customers_count,
          total_payments_by_currency,
        ] = await Promise.all([
          this.ordersService.countOrders({
            store: storeObjectId,
            status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
            created_at,
          } as FilterQuery<Order>),

          this.paymentGetterService.getPaymentCount({
            store: storeObjectId,
            status: PAYMENT_STATUS.SUCCESS,
            $or: [
              { type: PAYMENT_TYPES.INVOICE },
              { type: PAYMENT_TYPES.WALLET, payment_method: PAYMENT_METHODS.DIRECT_TRANSFER },
            ],
            created_at,
          } as FilterQuery<Payment>),

          this.analyticService.getStoreVisits({
            store: storeId,
            item: null,
            created_at,
          } as MongooseFilterQuery<AnalyticDocument>),

          this.customerService.getCustomerCount(storeId, {
            created_at,
          }),

          (async () => {
            const currencies = store.wallets.map((w) => w.currency);
            let currencyPaymentsMap: Partial<Record<CURRENCIES, number>> = {};

            currencies.forEach((c) => (currencyPaymentsMap[c] = 0));

            const paymentsProcessed = await this.paymentGetterService.getTotalPaymentsByCurrency({
              store: storeId,
              status: PAYMENT_STATUS.SUCCESS,
              $or: [
                { type: PAYMENT_TYPES.INVOICE },
                { type: PAYMENT_TYPES.WALLET, payment_method: PAYMENT_METHODS.DIRECT_TRANSFER },
              ],
              created_at,
            } as FilterQuery<Payment>);

            currencyPaymentsMap = { ...currencyPaymentsMap, ...paymentsProcessed };

            return currencyPaymentsMap;
          })(),
        ]);

        let top_products = [],
          top_customers = [];

        if (period === 'month') {
          const productsAndCustomers = await Promise.all([
            this.ordersService.getTopProducts(storeId, created_at.$gte, created_at.$lt, 5),
            this.ordersService.getTopCustomers(storeId, created_at.$gte, created_at.$lt, 3),
          ]);

          top_products = productsAndCustomers[0];
          top_customers = productsAndCustomers[1];
        }

        return {
          total_orders,
          total_payments_count,
          total_visits: visits.length,
          new_customers_count,
          total_payments_by_currency,
          top_products,
          top_customers,
        };
      };

      const [currentData, prevData] = await Promise.all([
        getSingleSummary(this.getCreateAtFilter(from.toDate(), to.toDate())),
        getSingleSummary(this.getCreateAtFilter(trendFrom.toDate(), trendTo.toDate())),
      ]);

      const trends = {
        total_orders: this.calcPercentageDifference(prevData.total_orders, currentData.total_orders),
        total_visits: this.calcPercentageDifference(prevData.total_visits, currentData.total_visits),
        total_payments_count: this.calcPercentageDifference(
          prevData.total_payments_count,
          currentData.total_payments_count,
        ),
        new_customers_count: this.calcPercentageDifference(
          prevData.new_customers_count,
          currentData.new_customers_count,
        ),
        payments_trends: {},
      };

      const paymentsTrends: Partial<Record<CURRENCIES, number>> = {};

      const allCurrencies = new Set<CURRENCIES>([
        ...(currentData.total_payments_by_currency
          ? (Object.keys(currentData.total_payments_by_currency) as CURRENCIES[])
          : []),
        ...(prevData.total_payments_by_currency
          ? (Object.keys(prevData.total_payments_by_currency) as CURRENCIES[])
          : []),
      ]);

      allCurrencies.forEach((currency) => {
        const prevAmount = prevData.total_payments_by_currency?.[currency] || 0;
        const currentAmount = currentData.total_payments_by_currency?.[currency] || 0;
        paymentsTrends[currency] = this.calcPercentageDifference(prevAmount, currentAmount);
      });

      trends.payments_trends = paymentsTrends;

      return {
        ...currentData,
        trends,
      };
    } catch (error) {
      console.error('Error in getEmailSummaries:', error);
      throw new InternalServerErrorException('Failed to generate email summaries.');
    }
  }

  async getTopItems(storeId: string) {
    const top_items = await this.brokerTransport
      .send<Item[]>(BROKER_PATTERNS.ITEM.GET_TOP_ITEMS, {
        store: storeId,
        is_deleted: { $ne: true },
      })
      .toPromise();

    return top_items;
  }

  async getInvoiceStatistics(storeId: string) {
    const storeInvoiceStats = await this.brokerTransport
      .send(BROKER_PATTERNS.INVOICE.GET_STATISTICS, storeId)
      .toPromise();

    return storeInvoiceStats;
  }

  async getStoreBankAccount(storeId: string) {
    const store = await this.storeModel.findOne({ _id: storeId });

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    if (!store.payments_enabled || !store.wallet) null;

    const wallet = await this.brokerTransport
      .send<Wallet>(BROKER_PATTERNS.WALLET.GET_WALLET, { storeId })
      .toPromise();

    if (!wallet || !wallet.accounts) return null;
    const accountsWithDetails = wallet?.accounts?.filter((a: Account) => !!a.account_number) as Account[];

    const accountWithDetails: Account =
      accountsWithDetails?.length > 0
        ? accountsWithDetails?.find((a) => a.is_primary) ?? accountsWithDetails[accountsWithDetails.length - 1]
        : null;

    if (!accountWithDetails) return;

    const { account_name, account_number, bank_code, bank_name } = accountWithDetails as Account;

    return {
      account_name,
      account_number,
      bank_code,
      bank_name,
    };
  }

  async getStoreBankAccounts(storeId: string) {
    // Fetch the store from the database
    const store = await this.storeModel.findOne({ _id: storeId });

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    // Check if payments are enabled and the wallet exists
    if (!store.payments_enabled || !store.wallet) return null;

    // Fetch the wallet using the broker transport
    const wallet = await this.brokerTransport
      .send<WalletWithAccounts>(BROKER_PATTERNS.WALLET.GET_WALLET, { storeId })
      .toPromise();

    // Check if the wallet and accounts exist
    if (!wallet || !wallet.accounts || wallet.accounts.length === 0) return null;

    // Map over the accounts to extract necessary details
    const accountData = wallet.accounts.map((account) => {
      const { account_name, account_number, bank_code, bank_name } = account;
      return {
        account_name,
        account_number,
        bank_code,
        bank_name,
      };
    });

    return accountData;
  }

  async countStoreTotal(filter: FilterQuery<Store>) {
    return this.storeModel.countDocuments(filter);
  }

  async deleteCategory(storeId, categoryId) {
    const store = await this.getAndValidateStore(storeId);

    store.categories = store.categories.filter((category) => category._id.toString() !== categoryId);

    return await store.save();
  }

  async addView(filter: FilterQuery<StoreDocument>) {
    const store = await this.storeModel
      .findOneAndUpdate(
        filter,
        {
          $inc: { total_visits: 1 },
        },
        { new: true },
      )
      .populate('owner');

    this.checkStoreVisitMilestone(store);

    return store;
  }

  async checkStoreVisitMilestone(store: StoreDocument) {
    const milestones = [100, 500, 1_000, 5_000, 10_000, 50_000, 100_000];

    if (store.total_visits - 1 < milestones[0] && store.total_visits < milestones[0]) return;

    for (let m of milestones) {
      if (store.total_visits - 1 < m && store.total_visits >= m) {
        const millifiedValue = millify(m);
        await this.storeModel.updateOne(
          { _id: store._id },
          { $push: { 'milestones.store_visits': { milestone: m, date: new Date() } } },
        );

        await this.resend.sendEmail(BROKER_PATTERNS.MAIL.STORE_VISITS_MILESTONE, {
          to: store.owner.email,
          subject: `You crossed ${millifiedValue} store visits 🎉`,
          data: {
            name: store.owner.name.split(' ')[0],
            value: millifiedValue,
          },
        });

        break;
      }
    }
  }

  getStoreModel() {
    return this.storeModel;
  }

  async getStores(filter: FilterStoreDto, paginationQuery: PaginatedStoreQueryDto) {
    const matchFn = () => {
      const f = { reference: { $exists: false } };
      if (filter.country) f['country'] = filter.country;
      if (filter.search) f['name'] = new RegExp(filter.search, 'ig');

      return f;
    };

    const storeCount = await this.storeModel.countDocuments(matchFn());

    const isVisitSort = paginationQuery.sort === 'VISITS';
    const isDateCreatedSort = paginationQuery.sort === 'DATE_CREATED';

    const aggregations: any = [
      {
        $match: matchFn(),
      },
      {
        $sort: (() => {
          if (isVisitSort) return { total_visits: -1 };
          if (isDateCreatedSort) return { createdAt: -1 };
          return { createdAt: 1 };
        })(),
      },
      {
        $skip: (paginationQuery.page - 1 || 0) * (paginationQuery.per_page || 25),
      },
      { $limit: paginationQuery.per_page || 25 },
      {
        $lookup: {
          from: 'subscriptions',
          localField: 'subscription',
          foreignField: '_id',
          as: 'subscription',
          pipeline: [
            {
              $project: { next_payment_date: 1, plan: 1 },
            },
          ],
        },
      },
      // {
      //   $lookup: {
      //     from: 'items',
      //     localField: '_id',
      //     foreignField: 'store',
      //     as: 'item_count',
      //     pipeline: [
      //       {
      //         $project: { name: 1 },
      //       },
      //     ],
      //   },
      // },
      // { $addFields: { item_count: { $size: '$item_count' } } },
      {
        $facet: {
          metadata: [
            { $count: 'stores' },
            {
              $addFields: {
                page: paginationQuery.page || 1,
                total_stores: storeCount,
              },
            },
          ],
          data: [
            {
              $project: {
                subscription: { next_payment_date: 1, plan: 1 },
                name: 1,
                total_visits: 1,
                slug: 1,
                phone: 1,
                createdAt: 1,
                country: 1,
                item_count: 1,
              },
            },
          ], // add projection here wish you re-shape the docs
        },
      },
    ];

    let stores = await this.storeModel.aggregate(aggregations);

    const plans = await this.brokerTransport.send<Plan[]>(BROKER_PATTERNS.PLAN.GET_PLANS_LEAN, {}).toPromise();

    const planMap = arrayToMap(plans, '_id');
    let returnedStores = stores[0];
    //because of the aggredation stores.subscription is returned as an array

    returnedStores.data = returnedStores.data.map((store) => {
      if (store?.subscription[0]?.plan) {
        return {
          ...store,
          subscription: {
            ...store.subscription[0],
            plan: planMap[store.subscription[0].plan.toString()],
          },
        };
      }

      return {
        ...store,
        subscription: {},
      };
    });

    if (filter?.plan) {
      returnedStores.data = returnedStores.data.filter((s) => s?.subscription?.plan?.type === filter.plan);
    }

    if (paginationQuery.sort == 'PRODUCTS') {
      returnedStores.data = returnedStores.data.sort((a, b) => b.item_count - a.item_count);
    }

    // // returnedStores = returnedStores[0]
    returnedStores.metadata = returnedStores.metadata[0] || {};

    return returnedStores;
  }

  async storeCount() {
    return await this.storeModel.countDocuments();
  }

  async countStores(filter: any = {}) {
    return await this.storeModel.countDocuments(filter);
  }

  async storeVisits() {
    const visit = await this.storeModel.find({}).select('total_visits');

    return visit.map((d) => d.total_visits).reduce((partialSum, a) => partialSum + a, 0);
  }

  async storesCreatedToday() {
    return await this.storeModel
      .find({
        createdAt: {
          $gt: Date.now() - 1000 * 60 * 60 * 24,
        },
      })
      .countDocuments();
  }

  /**
   * @function generateSlug
   * Generates the store link
   */
  async generateSlug(name: string): Promise<{ slug: string }> {
    let slug = sluggify(name).toLowerCase();
    let slugExists;

    do {
      slugExists = await this.storeModel.exists({
        $or: [{ slug }, { slugs: slug }, { disabled_slugs: slug }],
      });

      if (slugExists) {
        slug = slug + genChars(2, false);
      }
    } while (slugExists);

    return { slug };
  }
  // async generateSlug(name: string): Promise<string> {
  //   // let slug = genChars(8, false).toLowerCase();
  //   let slug = sluggify(name).toLowerCase();
  //   const slugExists = await this.storeModel.exists({
  //     $or: [{ slug }, { slugs: slug }, { disabled_slugs: slug }],
  //   });
  //   while (slugExists) {
  //     slug = slug + genChars(2, false);
  //   }
  //   return slug;
  // }

  async countSEOEligibeStores() {
    const plans = (
      await this.brokerTransport
        .send<Plan[]>(BROKER_PATTERNS.PLAN.GET_PLANS, {
          type: { $ne: PLAN_TYPE.STARTER },
        })
        .toPromise()
    ).map((p) => p.id);

    const subscriptions = (
      await this.brokerTransport
        .send<Subscription[]>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTIONS_LEAN, {
          filter: {
            plan: { $in: plans },
          },
          select: {
            _id: 1,
          },
        })
        .toPromise()
    ).map((s) => s._id);

    const stores = await this.storeModel.countDocuments({ subscription: { $in: subscriptions } });

    return stores;
  }

  async getSitemapPage(page: number, per_page: number) {
    const result = await this.storeModel.paginate(
      { disabled: { $ne: true }, reference: { $exists: false } },
      {
        sort: {
          _id: 1,
        },
        page: page,
        limit: per_page,
        lean: true,
        projection: { name: 1, slug: 1 },
      },
    );

    return result.docs;
  }

  async storeSeoFetch() {
    const plan = await this.brokerTransport
      .send(BROKER_PATTERNS.PLAN.GET_PLAN, {
        type: { $ne: PLAN_TYPE.STARTER },
      })
      .toPromise();

    return this.storeModel.aggregate([
      {
        $lookup: {
          from: 'subscriptions',
          localField: 'owner',
          foreignField: 'owner',
          as: 'subscription',
        },
      },
      { $unwind: '$subscription' },

      { $match: { 'subscription.plan': Types.ObjectId(plan.id) } },

      { $project: { slug: 1, _id: 0 } },
    ]);
  }

  async aggregateStore(filter: any) {
    return this.storeModel.aggregate(filter);
  }

  async getStoresLean(filter: any, select, pagination?: { skip: number; limit: number }) {
    let storePromise = this.storeModel.find(filter, select).sort({ createdAt: 1 }).lean();
    if (pagination) storePromise = storePromise.skip(pagination.skip).limit(pagination.limit);
    return storePromise;
  }

  async getCheckoutChannels(storeId: string) {
    let store = await this.storeModel.findOne({ _id: storeId });

    if (!store) {
      throw new BadRequestException('Store with id does not exist');
    }

    return store.checkout_channels;
  }

  async getDeliveryAreas(areas: string[]) {
    const deliveryAreas = await Promise.all(
      areas.map(async (a) => {
        const area = await this.deliveryArea.getDeliveryArea({ _id: a });
        return area;
      }),
    );

    return deliveryAreas;
  }

  async adminUpdateStore(id: string, data: UpdateStoreDto) {
    const response = await this.storeModel.findByIdAndUpdate(id, { ...data }, { new: true });

    return response;
  }

  async getCatgeoryById(categoryId: string, storeId: string) {
    const store = await this.getAndValidateStore(storeId);

    const category = store.categories.find((c) => c.id === categoryId);

    return category;
  }

  async getUserStores(userId: string) {
    try {
      const user = await this.brokerTransport
        .send<User>(BROKER_PATTERNS.USER.GET_USER, { id: userId })
        .toPromise();

      return user.stores;
    } catch (e) {
      return [];
    }
  }

  async validateStoreOwnership(userId: string, storeId: string): Promise<boolean> {
    const store = await this.storeModel.findById(storeId).exec();
    if (!store) {
      return false; // Store not found
    }

    // Check if the user is the primary owner
    if (store.owner.toString() === userId) {
      return true;
    }

    // Check if the user is in the owners array
    const ownerIds = store.owners.map((owner) => owner.user.toString());
    if (ownerIds.includes(userId)) {
      return true;
    }

    return false; // User is not an owner
  }

  async extractStoreImageUrls(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const imageUrlSet = new Set<string>();

        console.log('Current working directory:', process.cwd());

        // Create a read stream from the JSON file
        const stream = fs
          .createReadStream('staging.stores.json', { encoding: 'utf8' })
          .pipe(JSONStream.parse('*')) // Parses each store in the array
          .pipe(
            es.through(function (store) {
              // Process store.logo
              if (store.logo && typeof store.logo === 'string') {
                if (store.logo.includes('catlog-1.s3.eu-west-2.amazonaws.com')) {
                  imageUrlSet.add(store.logo);
                }
              }

              // Process store.hero_image
              if (store.hero_image && typeof store.hero_image === 'string') {
                if (store.hero_image.includes('catlog-1.s3.eu-west-2.amazonaws.com')) {
                  imageUrlSet.add(store.hero_image);
                }
              }

              // Process store.configuration.menu_images
              if (store.configuration && Array.isArray(store.configuration.menu_images)) {
                for (const imageUrl of store.configuration.menu_images) {
                  if (typeof imageUrl === 'string' && imageUrl.includes('catlog-1.s3.eu-west-2.amazonaws.com')) {
                    imageUrlSet.add(imageUrl);
                  }
                }
              }

              // Process store.extra_info.images
              if (store.extra_info && Array.isArray(store.extra_info.images)) {
                for (const imageUrl of store.extra_info.images) {
                  if (typeof imageUrl === 'string' && imageUrl.includes('catlog-1.s3.eu-west-2.amazonaws.com')) {
                    imageUrlSet.add(imageUrl);
                  }
                }
              }

              // Continue to the next store
              this.emit('data', store);
            }),
          );

        stream.on('end', () => {
          // Convert the Set to an Array
          const imageUrlArray = Array.from(imageUrlSet);

          // Write the array to a new JSON file
          fs.writeFileSync('staging.stores.image-urls.json', JSON.stringify(imageUrlArray, null, 2));

          console.log('Store image URLs extracted successfully.');
          resolve();
        });

        stream.on('error', (error) => {
          console.error('Error extracting store image URLs:', error);
          reject(error);
        });
      } catch (error) {
        console.error('Error extracting store image URLs:', error);
        reject(error);
      }
    });
  }

  async verifyStoreSlug(slug: string) {
    const slugExists = await this.storeModel.exists({
      $or: [{ slug }, { slugs: slug }, { disabled_slugs: slug }],
    });

    let suggestedSlug = slug;

    if (slugExists) {
      suggestedSlug = sluggify(slug).toLowerCase() + genChars(2, false);
    }

    return {
      used: slugExists,
      suggestedSlug,
    };
  }

  async addDomain(storeId: string, domain: string) {
    console.log('<===== Adding Domain In Servive =====>');
    // Normalize the domain
    domain = this.normalizeDomain(domain);

    // Validate domain format
    if (!this.validateDomainFormat(domain)) {
      throw new BadRequestException('Invalid domain format');
    }

    // Check if domain already exists
    const domainExists = await this.storeDomainModel.findOne({ domain });
    if (domainExists) {
      throw new ConflictException('Domain already exists');
    }

    // Create verification code for DNS TXT record
    const verificationCode = `catlog-verify=${this.generateRandomString(16)}`;

    // Create the domain
    const domainDoc = await this.storeDomainModel.create({
      domain,
      store_id: storeId,
      verification_code: verificationCode,
      verified: false,
      certificate_issued: false,
    });

    console.log('<===== Domain Added In Servive =====>');

    return domainDoc;
  }

  async getDomains(storeId: string) {
    return this.storeDomainModel.find({ store_id: storeId });
  }

  async removeDomain(storeId: string, domainId: string) {
    try {
      // Find the domain by ID and ensure it belongs to the store
      const domainDoc = await this.storeDomainModel.findOne({
        _id: domainId,
        store_id: storeId,
      });

      if (!domainDoc) {
        throw new NotFoundException('Domain not found or does not belong to this store');
      }

      const domain = domainDoc.domain;

      // If the domain was verified and potentially has a certificate,
      // clean it up on the host server
      if (domainDoc.verified || domainDoc.certificate_issued) {
        try {
          // Call the host API to delete the domain's SSL and Nginx config
          const deleteResult = await this.hostApiRepository.deleteDomain(domain);

          if (deleteResult.error) {
            // Log the error but continue with domain removal from database
            this.logger.warn(
              `Warning: Could not fully clean up domain ${domain} on host server: ${
                typeof deleteResult.error === 'string' ? deleteResult.error : JSON.stringify(deleteResult.error)
              }`,
            );
          } else {
            this.logger.log(`Successfully removed domain ${domain} from host server`);
          }
        } catch (error) {
          // Log the error but continue with domain removal from database
          this.logger.warn(`Error cleaning up domain ${domain} on host server: ${error.message}`);
        }
      }

      // Remove the domain from database
      return this.storeDomainModel.findByIdAndDelete(domainId);
    } catch (error) {
      // If it's already a NestJS exception, just rethrow it
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      // Otherwise log and throw a server error
      this.logger.error(`Failed to remove domain: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Error removing domain: ${error.message}`);
    }
  }

  async getStoreByDomain(domain: string, xadmin?: string, password?: string) {
    const normalizedDomain = this.normalizeDomain(domain);
    const domainMapping = await this.storeDomainModel.findOne({ domain: normalizedDomain });

    if (!domainMapping) {
      throw new NotFoundException('Store not found');
    }

    const infoToHide = STORE_PUBLIC_HIDDEN_INFO.replace(' -subscription', ''); //allow subscription to be fetched

    let store: StoreDocument & { is_admin?: boolean } = await this.getStore(
      {
        _id: domainMapping.store_id,
      },
      infoToHide,
    );

    if (!store) {
      throw new NotFoundException('Store is not found');
    }

    store = store.toFilteredJSON();
    store.delivery_areas = await this.getDeliveryAreas(store.delivery_areas as string[]);

    store.country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: store.country,
      })
      .toPromise();

    store.has_paid_subscription = await this.getHasPaidSubscription(store);

    // Add admin visibility logic
    const isAdmin = await this.verifyIsAdmin(xadmin, password);

    if (isAdmin) {
      store.is_admin = true;
      store.disabled = false;
    }

    const sc = store.currencies?.storefront; //storefront currency options
    const hasMultipleCurrencies = sc.length > 1 || (sc.length === 1 && sc[0] !== store.currencies?.products);

    if (hasMultipleCurrencies) {
      const rates = await this.brokerTransport
        .send<CurrencyRates>(BROKER_PATTERNS.WALLET.GET_EXCHANGE_RATES, {})
        .toPromise();
      store.meta = { rates } as any;
    }

    const { subscription, ...data } = store;

    return data;
  }

  private normalizeDomain(domain: string | any): string {
    if (!domain || typeof domain !== 'string') {
      throw new BadRequestException('Invalid domain format: domain must be a string');
    }

    // Remove protocol
    let normalizedDomain = domain.replace(/^https?:\/\//, '');

    // Remove www. prefix
    normalizedDomain = normalizedDomain.replace(/^www\./, '');

    // Remove trailing slash
    normalizedDomain = normalizedDomain.replace(/\/$/, '');

    // Validate the domain format after normalization
    if (!this.validateDomainFormat(normalizedDomain)) {
      throw new BadRequestException('Invalid domain format after normalization');
    }

    return normalizedDomain.toLowerCase();
  }

  private validateDomainFormat(domain: string): boolean {
    if (!domain || typeof domain !== 'string') {
      return false;
    }
    const domainRegex = /^[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return domainRegex.test(domain);
  }

  private generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  async generateSslCertificate(domainId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Find the domain by ID
      const domainDoc = await this.storeDomainModel.findById(domainId);

      if (!domainDoc) {
        return {
          success: false,
          message: 'Domain not found',
        };
      }

      const domain = domainDoc.domain;

      // Validate domain format to prevent command injection
      if (!this.validateDomainFormat(domain)) {
        return {
          success: false,
          message: 'Invalid domain format',
        };
      }

      const result = await this.hostApiRepository.generateSslCertificate(domain);

      if (!result.error) {
        // Update the domain with certificate issuance information
        await this.storeDomainModel.findByIdAndUpdate(domainId, {
          certificate_issued: true,
          certificate_issued_at: new Date(),
        });
        return {
          success: result.data.success,
          message: result.data.message,
        };
      }

      return {
        success: false,
        message: result.error || 'Failed to generate SSL certificate',
      };
    } catch (error) {
      this.logger.error(`Failed to generate SSL certificate: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Error generating SSL certificate: ${error.message}`,
      };
    }
  }

  async verifyDomain(
    domainId: string,
  ): Promise<{
    success: boolean;
    message: string;
    verified: boolean;
    certificateGenerated?: boolean;
    certificateMessage?: string;
  }> {
    try {
      const domainDoc = await this.storeDomainModel.findById(domainId);

      if (!domainDoc) {
        return {
          success: false,
          message: 'Domain not found',
          verified: false,
        };
      }

      if (!domainDoc.verification_code) {
        return {
          success: false,
          message: 'Verification code not found for this domain',
          verified: false,
        };
      }

      const domain = domainDoc.domain;
      const verificationCode = domainDoc.verification_code;

      const verificationResult = await this.hostApiRepository.verifyDomainOwnership(domain, verificationCode);

      if (verificationResult.error) {
        return {
          success: false,
          message: verificationResult.error || 'Domain verification failed',
          verified: false,
        };
      }

      const { verified } = verificationResult.data;

      // Update domain verification status
      await this.storeDomainModel.findByIdAndUpdate(domainId, {
        verified,
        verified_at: verified ? new Date() : undefined,
      });

      // If verified, try to generate SSL certificate
      let certificateResult = { success: false, message: '' };
      if (verified) {
        certificateResult = await this.generateSslCertificate(domainId);
      }

      return {
        success: true,
        message: verificationResult.data.message,
        verified,
        certificateGenerated: certificateResult.success,
        certificateMessage: certificateResult.message,
      };
    } catch (error) {
      this.logger.error(`Failed to verify domain: ${error.message}`, error.stack);
      return {
        success: false,
        message: `An error occurred during domain verification: ${error.message}`,
        verified: false,
      };
    }
  }

  /**
   * Get instance of DomainService for domain-related operations
   * Used by the StoreBroker for domain purchase completion
   */
  async getDomainService() {
    const domainService = this.moduleRef.get(DomainService, { strict: false });
    if (!domainService) {
      throw new Error('DomainService not found in module registry');
    }
    return domainService;
  }

  async getAnalysisAnalytics() {
    const freePlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { type: PLAN_TYPE.STARTER })
      .toPromise();

    const subscriptionIds = await this.brokerTransport
      .send<{ _id: string }[]>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION_IDS, {
        plan: { $ne: getDocId(freePlan) },
        last_payment_reference: { $exists: true },
      })
      .toPromise();

    const allStores = await this.storeModel.countDocuments({});
    const activeStores = await this.storeModel.countDocuments({
      subscription: { $in: subscriptionIds.map((s) => new Types.ObjectId(s._id)) } as any,
    });
    const allStoresWithPaidOrders = await this.storeModel.countDocuments({
      'onboarding_steps.has_taken_first_order_with_payment': true,
    });
    const activeStoresWithPaidOrders = await this.storeModel.countDocuments({
      subscription: { $in: subscriptionIds.map((s) => new Types.ObjectId(s._id)) } as any,
      'onboarding_steps.has_taken_first_order_with_payment': true,
    });

    return {
      allStores,
      activeStores,
      activeStoresWithPaidOrders,
      allStoresWithPaidOrders,
    };
  }

  async getStoresDataForSubscribers(
    filter: Record<string, any>,
    paginationQuery: PaginatedStoreAnalysisQueryDto,
    active_subscribers: boolean,
  ) {
    const freePlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { type: PLAN_TYPE.STARTER })
      .toPromise();

    const subscriptionIds = await this.brokerTransport
      .send<{ _id: string }[]>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION_IDS, {
        plan: active_subscribers ? { $ne: getDocId(freePlan) } : { $eq: getDocId(freePlan) },
        last_payment_reference: { $exists: true },
      })
      .toPromise();

    return await this.getStoresData(
      { ...filter, subscription: { $in: subscriptionIds.map((s) => new Types.ObjectId(s._id)) } },
      paginationQuery,
    );
  }

  async getStoresData(filter: Record<string, any>, paginationQuery: PaginatedStoreAnalysisQueryDto) {
    if (filter['search']) {
      // Special handling for search
      filter['name'] = new RegExp(filter['search'], 'ig');
      delete filter['search'];
    }

    const storeCount = await this.storeModel.countDocuments(filter);

    const aggregations = [
      {
        $match: { ...filter, reference: { $exists: false } },
      },
      {
        $sort: { createdAt: paginationQuery.sort === 'ASC' ? 1 : -1 },
      },
      {
        $skip: (paginationQuery.page - 1 || 0) * (paginationQuery.per_page || 25),
      },
      { $limit: paginationQuery.per_page || 25 },
      {
        $lookup: {
          from: 'orders',
          localField: '_id',
          foreignField: 'store',
          as: 'orders',
          pipeline: [
            {
              $match: {
                status: { $nin: [ORDER_STATUSES.CANCELLED, ORDER_STATUSES.ABANDONED] },
              },
            },
            {
              $count: 'total',
            },
          ],
        },
      },
      {
        $facet: {
          metadata: [
            { $count: 'stores' },
            {
              $addFields: {
                page: paginationQuery.page || 1,
                total_stores: storeCount,
              },
            },
          ],
          data: [
            {
              $project: {
                name: 1,
                slug: 1,
                phone: 1,
                createdAt: 1,
                country: 1,
                total_orders: {
                  $ifNull: [{ $arrayElemAt: ['$orders.total', 0] }, 0],
                },
                total_visits: 1,
                item_count: 1,
                kyc_approved: 1,
                current_plan: 1,
                onboarding_steps: 1,
                configuration: 1,
                business_category: 1,
                owner: 1,
              },
            },
          ],
        },
      },
    ];

    const stores = await this.storeModel.aggregate(aggregations);
    const result = stores[0];
    result.metadata = result.metadata[0] || {};

    return result;
  }
}
