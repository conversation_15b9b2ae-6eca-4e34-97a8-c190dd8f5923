import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsObject, IsOptional, ValidateNested } from 'class-validator';
import mongoose, { Document } from 'mongoose';
import { Country, COUNTRY_CODE, CURRENCIES, CurrencyMap } from '../country/country.schema';
import { Subscription } from '../subscription/subscription.schema';
import { Plan } from '../plan/plan.schema';
import { User } from '../user/user.schema';
import { DeliveryArea } from './delivery-areas/delivery-areas.schema';
import { Invite } from './teams/invites.schema';
import { COLORS } from '../../utils/constants';
import { Address } from '../deliveries/deliveries.schema';
import { Branch } from './branches/branches.schema';
import { DELIVERY_PROVIDERS } from '../../enums/deliveries';
import { PLAN_TYPE } from '../../enums/plan.enum';
import { Type } from 'class-transformer';

export enum STORE_TYPES {
  REGULAR = 'REGULAR',
  RESTAURANT = 'RESTAURANT',
}

export enum BUSINESS_TYPES {
  PHYSICAL = 'PHYSICAL',
  DIGITAL = 'DIGITAL',
  PROPERTIES = 'PROPERTIES',
  SERVICE = 'SERVICE',
  OTHERS = 'OTHERS',
}

export enum INPUT_TYPE {
  TEXT = 'text',
  NUMBER = 'number',
  RADIO = 'radio',
  DROPDOWN = 'dropdown',
  TEXTAREA = 'textarea',
}

export enum INFO_BLOCK_CONTENT_TYPE {
  TEXT = 'TEXT',
  IMAGES = 'IMAGES',
}

export enum STOREFRONT_VERSION {
  V1 = 'V1',
  V2 = 'V2',
}

export class WhatsappCheckoutChannel {
  label: string;
  phone: string;
  type: 'WHATSAPP';
  primary: boolean;
  id?: string;
}

export class InstagramCheckoutChannel {
  username: string;
  type: 'INSTAGRAM';
  id?: string;
  enabled?: boolean;
}

export class CheckoutChannels {
  whatsapp: WhatsappCheckoutChannel[];
  instagram?: InstagramCheckoutChannel;
}

export class StoreCurrencySettings {
  default: CURRENCIES;
  products: CURRENCIES;
  storefront: CURRENCIES[];
  storefront_default: CURRENCIES;
  rates: CurrencyMap;
}

export class StorePaymentOptions {
  [key: string]: StorePaymentMethod[];
}

export class StorePaymentMethod {
  type: string;
  enabled: boolean;
}

export type StoreDocument = Store & Document;

export class CustomCheckoutFormItem {
  @ApiProperty()
  name: string;

  @ApiProperty()
  type: INPUT_TYPE;

  @ApiProperty()
  is_required: boolean;

  @ApiProperty()
  is_enabled: boolean;

  @ApiProperty()
  label?: boolean;

  @ApiProperty()
  options?: { value: string; price?: number; image?: string }[];
}

export class IStoreCategory {
  @ApiProperty()
  id: string;
  _id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  emoji: string;

  @ApiProperty()
  items_count?: number;

  @ApiProperty()
  meta?: {
    chowdeck?: {
      id?: number;
      reference?: string;
    };
  };
}

export class IStoreSocials {
  @ApiProperty()
  twitter: string;

  @ApiProperty()
  facebook: string;

  @ApiProperty()
  instagram: string;

  @ApiProperty()
  snapchat: string;

  @ApiProperty()
  tiktok: string;
}

export class IStoreOwner {
  @ApiProperty()
  user: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  role: string;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  disabled?: boolean;
}

export class IMilestone {
  @ApiProperty()
  milestone: number;

  @ApiProperty({ type: Date })
  @Prop({ type: Date })
  date?: Date;
}

export class storeMeta {
  @ApiProperty()
  @IsNumber()
  instagram_item_upload_count?: number;
}

// ==================== CONFIGURATION CLASSES ====================
export class StoreViewModes {
  @ApiProperty()
  grid: boolean;

  @ApiProperty()
  card: boolean;

  @ApiProperty()
  horizontal: boolean;

  @ApiProperty({ enum: ['grid', 'card', 'horizontal'] })
  default: 'grid' | 'card' | 'horizontal';
}

export class StoreHours {
  @ApiProperty()
  monday: string;

  @ApiProperty()
  tuesday: string;

  @ApiProperty()
  wednesday: string;

  @ApiProperty()
  thursday: string;

  @ApiProperty()
  friday: string;

  @ApiProperty()
  saturday: string;

  @ApiProperty()
  sunday: string;
}

export class CustomerCheckIn {
  @ApiProperty()
  enabled: boolean;

  @ApiProperty()
  days: number;

  @ApiProperty()
  message: string;
}

export class StoreConfiguration {
  @ApiProperty({ type: StoreViewModes })
  @ValidateNested()
  @Type(() => StoreViewModes)
  view_modes: StoreViewModes;

  @ApiProperty({ type: StoreHours, required: false })
  @ValidateNested()
  @Type(() => StoreHours)
  hours?: StoreHours;

  @ApiProperty({ type: CustomerCheckIn, required: false })
  @ValidateNested()
  @Type(() => CustomerCheckIn)
  auto_customer_check_in?: CustomerCheckIn;

  @ApiProperty()
  @Prop({ type: String, required: false })
  fb_pixel: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  ga_id: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  custom_message: string;

  @ApiProperty({ required: false })
  @Prop({ type: String, required: false })
  bot_initiation_message: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  custom_order_success_message: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  enquiry_message: string;

  @ApiProperty({ required: false })
  @Prop({ type: Boolean, required: false })
  direct_checkout_enabled?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  sort_by_latest_products: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  show_unavailable_products: boolean;

  @ApiProperty({ required: false })
  @Prop({ type: String, required: false })
  color?: string;

  @ApiProperty({ required: false })
  @Prop({ type: String, required: false })
  color_cache?: string;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  whatsapp_checkout_enabled: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  facebook_pixel_enabled: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  facebook_pixel_enabled_cache: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  customer_pickup_enabled: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  require_delivery_info: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, required: false })
  confirm_order_before_payment: boolean;

  @ApiProperty()
  @Prop({ type: String, required: false })
  pickup_address: string;

  @ApiProperty()
  @Prop({ type: Number, required: false })
  average_delivery_timeline: number;

  @ApiProperty({ required: false })
  @Prop({ type: Boolean, required: false })
  send_menu_on_initiation?: boolean;

  @ApiProperty({ required: false, type: [String] })
  @Prop({ type: [String], default: [], required: false })
  menu_images?: string[];

  @ApiProperty({ required: false })
  @Prop({ type: Boolean, required: false })
  payment_validates_order?: boolean;

  @ApiProperty({ required: false })
  @Prop({ type: Boolean, required: false })
  require_geolocation?: boolean;

  @ApiProperty({ required: false, default: false })
  @Prop({ type: Boolean, required: false })
  auto_confirm_chowbot_orders?: boolean;

  @ApiProperty({ required: false, default: true })
  @Prop({ type: Boolean, required: false })
  collect_order_notes?: boolean;

  @ApiProperty({ required: false, default: false })
  @Prop({ type: Boolean, required: false })
  pass_chowbot_fee_to_deliveries?: boolean;

  @ApiProperty({ required: false })
  @Prop({ type: Boolean, required: false })
  require_emails?: boolean;

  @ApiProperty({ required: false })
  @Prop({ type: Number, required: false })
  payment_timeout?: number;

  @ApiProperty({ required: false, type: [CustomCheckoutFormItem] })
  @ValidateNested({ each: true })
  @Type(() => CustomCheckoutFormItem)
  @Prop({ type: [CustomCheckoutFormItem], required: false })
  custom_checkout_form?: CustomCheckoutFormItem[];
}

// Onboarding Steps
@Schema()
export class OnboardingSteps {
  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  products_added?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  link_added?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  test_payment_made?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  security_pin_added?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  has_taken_first_order_with_payment?: boolean;
}

export class InfoBlock {
  _id?: any;

  @ApiProperty()
  @Prop({ type: String, required: true })
  title: string;

  @ApiProperty({ enum: INFO_BLOCK_CONTENT_TYPE })
  @Prop({ type: String, enum: INFO_BLOCK_CONTENT_TYPE, required: true })
  content_type: INFO_BLOCK_CONTENT_TYPE;

  @ApiProperty()
  @Prop({ type: String, required: false })
  text_content?: string;

  @ApiProperty({ type: [String] })
  @Prop({ type: [String], default: [] })
  image_content?: string[];

  @ApiProperty()
  @Prop({ type: Date, default: Date.now })
  created_at: Date;

  @ApiProperty()
  @Prop({ type: Boolean, default: true })
  is_visible: boolean;
}

export class Testimonial {
  _id?: any;

  @ApiProperty()
  @Prop({ type: String, required: true })
  customer_name: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  content: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  source?: string;

  @ApiProperty()
  @Prop({ type: Date, default: Date.now })
  created_at: Date;

  @ApiProperty()
  @Prop({ type: Boolean, default: true })
  is_visible: boolean;
}

export class FAQ {
  _id?: any;

  @ApiProperty()
  @Prop({ type: String, required: true })
  question: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  answer: string;

  @ApiProperty()
  @Prop({ type: Date, default: Date.now })
  created_at: Date;

  @ApiProperty()
  @Prop({ type: Boolean, default: true })
  is_visible: boolean;
}

@Schema({ timestamps: true })
export class Store {
  _id: string;

  @ApiProperty()
  id: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  description: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  phone: string;

  @ApiProperty()
  @Prop({ type: String, required: false })
  secondary_phone?: string;

  @ApiProperty()
  @Prop({ type: String, default: '', unique: true })
  slug: string;

  @ApiProperty()
  @Prop({ type: String, default: '', unique: true })
  primary_slug: string;

  @ApiProperty()
  @Prop({ type: [String], default: [], index: true })
  slugs: string[];

  @ApiProperty()
  @Prop({ type: [String] })
  disabled_slugs: string[];

  @ApiProperty()
  @Prop({ type: String })
  disabled_slug: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  address: string | null;

  @ApiProperty({ type: () => User })
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  owner: User;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.DocumentArray, default: [] })
  owners: IStoreOwner[];

  @ApiProperty({ type: () => Array })
  @Prop([{ type: mongoose.Schema.Types.ObjectId, ref: 'Invite', default: [] }])
  invites: Invite[];

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Plan' })
  plan: Plan;

  @ApiProperty()
  @Prop({ type: Object })
  current_plan?: {
    plan_type: PLAN_TYPE;
    interval_text: string;
    interval: number;
  };

  @ApiProperty()
  @Prop({ type: String, default: '' })
  state: string;

  @ApiProperty()
  @Prop({ type: String, default: COUNTRY_CODE.NG })
  country: COUNTRY_CODE | Country;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  logo: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  hero_image: string;

  @ApiProperty()
  @Prop({ type: String, default: '' })
  custom_message: string;

  @ApiProperty({ type: [IStoreCategory] })
  @Prop({ type: [{ name: String, emoji: String, meta: Object }] })
  categories: IStoreCategory[];

  @ApiProperty({
    type: IStoreSocials,
  })
  @IsOptional()
  @IsObject()
  @Prop({
    type: {
      twitter: String,
      facebook: String,
      instagram: String,
      whatsapp: String,
      snapchat: String,
      tiktok: String,
    },
    default: {
      twitter: '',
      instagram: '',
      facebook: '',
      whatsapp: '',
      snapchat: '',
      tiktok: '',
    },
  })
  socials: {
    twitter: string;
    facebook: string;
    instagram: string;
    whatsapp: string;
    snapchat: string;
    tiktok: string;
  };

  @ApiProperty({ type: StoreConfiguration })
  @Prop({
    type: Object,
    default: new StoreConfiguration(),
  })
  @ValidateNested()
  @Type(() => StoreConfiguration)
  configuration: StoreConfiguration;

  @ApiProperty({
    type: 'object',
    nullable: true,
    properties: {
      chowdeck: {
        type: 'object',
        properties: {
          sync_to_chowdeck: { type: 'boolean' },
          sync_from_chowdeck: { type: 'boolean' },
          auto_delivery: { type: 'boolean' },
          fields_to_update: {
            type: 'object',
            properties: {
              name: { type: 'boolean' },
              description: { type: 'boolean' },
              category: { type: 'boolean' },
              price: { type: 'boolean' },
              variants: { type: 'boolean' },
              availability: { type: 'boolean' },
              images: { type: 'boolean' },
            },
          },
        },
      },
    },
  })
  @Prop({
    type: {
      chowdeck: {
        sync_to_chowdeck: Boolean,
        sync_from_chowdeck: Boolean,
        auto_delivery: Boolean,
        fields_to_update: {
          name: Boolean,
          description: Boolean,
          category: Boolean,
          price: Boolean,
          variants: Boolean,
          availability: Boolean,
          images: Boolean,
        },
      },
    },
    default: {},
  })
  third_party_configs?: {
    chowdeck: {
      sync_to_chowdeck: boolean;
      sync_from_chowdeck: boolean;
      auto_delivery: boolean;
      fields_to_update?: {
        name: boolean;
        description: boolean;
        category: boolean;
        price: boolean;
        variants: boolean;
        availability: boolean;
        images: boolean;
      };
    };
  };

  @ApiProperty()
  @Prop({ type: String, default: '' })
  delivery_locations: string;

  @ApiProperty()
  @Prop({ type: [mongoose.Schema.Types.ObjectId], default: [], ref: DeliveryArea.name })
  delivery_areas: Array<string | DeliveryArea>;

  @Prop({ type: Date })
  last_reminder_date?: Date;

  @Prop({ type: Date })
  last_activity?: Date;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  total_visits: number;

  @ApiProperty({ type: CheckoutChannels })
  @Prop({ type: Array, default: { whatsapp: [], instagram: [] } })
  checkout_channels: CheckoutChannels;

  @ApiProperty({ type: String })
  @Prop({ type: String })
  wallet: string;

  @ApiProperty({ type: [String] })
  @Prop({ type: [{ currency: String, id: mongoose.Schema.Types.ObjectId }], ref: 'Wallet', _id: false })
  wallets: { currency: CURRENCIES; id: string }[];

  @ApiProperty()
  @Prop({ type: String })
  kyc: string;

  @ApiProperty()
  @Prop({ type: Boolean, default: false, required: false })
  kyc_approved?: boolean;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  item_count?: number;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Subscription' })
  subscription?: Subscription | string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Address' })
  pickup_address?: Address | string;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Branches' })
  branches?: Branch | string;

  // @ApiProperty()
  // @Prop({
  //   type: {
  //     products_added: Boolean,
  //     link_added: Boolean,
  //     test_payment_made: Boolean,
  //     security_pin_added: Boolean,
  //   },
  //   default: {
  //     products_added: false,
  //     link_added: false,
  //     test_payment_made: false,
  //     security_pin_added: false,
  //   },
  // })
  // onboarding_steps: {
  //   products_added: boolean;
  //   link_added: boolean;
  //   test_payment_made: boolean;
  //   security_pin_added: boolean;
  // };

  @ApiProperty({ type: () => OnboardingSteps })
  @ValidateNested()
  @Type(() => OnboardingSteps)
  @Prop({ type: OnboardingSteps, default: () => new OnboardingSteps() })
  onboarding_steps: OnboardingSteps;

  @ApiProperty()
  @Prop({ type: String })
  security_pin?: string;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  disabled?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  maintenance_mode?: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  payments_enabled: boolean;

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  deliveries_enabled?: boolean;

  @ApiProperty()
  @Prop({
    type: StoreCurrencySettings,
    default: {
      default: CURRENCIES.NGN,
      products: CURRENCIES.NGN,
      storefront: [CURRENCIES.NGN],
      storefront_default: CURRENCIES.NGN,
      rates: {},
    },
  })
  currencies: StoreCurrencySettings;

  @ApiProperty()
  @Prop({
    type: Object,
    default: {},
  })
  payment_options: StorePaymentOptions;

  @Prop({
    type: Object,
    default: {
      order_volume: [],
      store_visits: [],
      store_payments: [],
      total_orders: [],
    },
  })
  milestones: {
    order_volume: IMilestone[];
    store_visits: IMilestone[];
    store_payments: IMilestone[];
    total_orders: IMilestone[];
  };

  @ApiProperty()
  @Prop({ type: Object, default: {} })
  access_tokens?: {
    [key: string]: {
      token: string;
      meta?: any;
    };
  };

  @Prop({ type: Object, default: {} })
  public_access_tokens?: {
    [key: string]: {
      key?: string;
      reference?: string;
      meta?: any;
    };
  };

  @Prop({
    type: Object,
  })
  extra_info?: {
    delivery_timeline?: string;
    production_timeline?: string;
    refund_policy?: string;
    images?: string[];
    images_label?: string;
  };

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  has_paid_subscription?: boolean;

  @ApiProperty()
  @Prop({ type: Object })
  highlights_config?: {
    count: number;
    shouldShowHighlights: boolean;
  };

  @ApiProperty()
  @Prop({ type: Boolean, default: false })
  is_wrap_data_created?: boolean;

  @ApiProperty()
  @Prop({ type: Object })
  flags?: {
    uses_chowbot?: boolean;
    storefront_version?: STOREFRONT_VERSION;
  };

  @ApiProperty()
  @Prop({
    type: {
      name: String,
      product_types: [String],
    },
  })
  business_category?: {
    name: string;
    type: BUSINESS_TYPES;
    monthly_orders: string;
    product_types: string[];
  };

  @ApiProperty()
  @Prop({
    type: String,
  })
  store_menu?: string;

  @ApiProperty()
  @Prop({ type: [String], default: [DELIVERY_PROVIDERS.SHIPBUBBLE] })
  delivery_providers: DELIVERY_PROVIDERS[];

  @ApiProperty()
  @Prop({ type: storeMeta })
  meta?: storeMeta;

  @ApiProperty({ type: [Testimonial] })
  @Prop({
    type: [
      {
        customer_name: String,
        content: String,
        source: String,
        created_at: { type: Date, default: Date.now },
        is_visible: { type: Boolean, default: true },
        _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
      },
    ],
    default: [],
  })
  testimonials?: Testimonial[];

  @ApiProperty({ type: [FAQ] })
  @Prop({
    type: [
      {
        question: String,
        answer: String,
        created_at: { type: Date, default: Date.now },
        is_visible: { type: Boolean, default: true },
        _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
      },
    ],
    default: [],
  })
  faqs?: FAQ[];

  @ApiProperty()
  @Prop({
    type: {
      content: String,
      images: [String],
    },
    default: {
      content: '',
      images: [],
    },
  })
  about_us?: {
    content: string;
    images: string[];
  };

  toFilteredJSON(): any {}
}

export const StoreSchema = SchemaFactory.createForClass(Store);

StoreSchema.methods.toFilteredJSON = function () {
  const obj = this.toJSON();
  delete obj.security_pin;
  delete obj.access_tokens;
  return obj;
};
