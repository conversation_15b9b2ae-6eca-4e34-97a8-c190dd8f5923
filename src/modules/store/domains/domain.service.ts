import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { businessTLDs, catlogInfo, Go54Repository, UserInfo } from '../../../repositories/go54.repository';
import { getDocId, toNaira } from '../../../utils/functions';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import { StoreDomain, StoreDomainDocument } from './store-domain.schema';
import { DomainPurchase, DomainPurchaseDocument, DOMAIN_PURCHASE_STATUS } from './domain-purchase.schema';
import { Store } from '../store.schema';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCIES } from '../../country/country.schema';
import { HostApiRepository } from '../../../repositories/host-api.repository';
import { QUEUES, JOBS } from '../../../enums/queues.enum';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { CurrencyConversionService } from '../../wallets/currency-conversion/currency-conversion.service';
import { CacheService } from '../../shared/cache/cache.service';
import { DOMAIN_PURCHASE_MARKUPS } from '../../../utils/constants';
import { formatDate } from '../../../utils/time';
import { toCurrency } from '../../../utils';
import { ResendRepository } from '../../../repositories/resend.repository';
@Injectable()
export class DomainService {
  // Default TLDs array with common options
  private readonly ratesCacheKey = 'domain_rates';

  constructor(
    @InjectModel(StoreDomain.name) private readonly storeDomainModel: Model<StoreDomainDocument>,
    @InjectModel(DomainPurchase.name) private readonly domainPurchaseModel: Model<DomainPurchaseDocument>,
    private readonly go54Repository: Go54Repository,
    private readonly hostApiRepository: HostApiRepository,
    private readonly resend: ResendRepository,
    private readonly brokerTransport: BrokerTransportService,
    private readonly cacheService: CacheService,
    private readonly logger: Logger,
    @InjectQueue(QUEUES.DOMAIN) private readonly domainQueue: Queue,
    private readonly currencyConversionService: CurrencyConversionService,
  ) {
    this.logger.setContext('DomainService');
  }

  /**
   * Check domain availability and pricing through Go54
   */
  async checkDomainAvailability(domain: string, storeCountry: COUNTRY_CODE) {
    // Normalize the domain
    domain = this.normalizeDomain(domain);

    // Validate domain format
    if (!this.validateDomainFormat(domain)) {
      throw new BadRequestException('Invalid domain format');
    }

    const result = await this.go54Repository.checkDomainAvailability(domain, storeCountry);

    if (result.error) {
      this.logger.error(`Failed to check domain availability: ${JSON.stringify(result.error)}`);
      throw new BadRequestException(result.error);
    }

    let { recommended, alternatives } = await result.data.alternatives
      .filter((alt) => alt.available)
      .sort((a, b) => a.price - b.price)
      .reduce(async (accPromise, curr) => {
        const acc = await accPromise;

        const convertedPrice = await this.convertDomainPriceToStoreCurrency(
          curr.price,
          CURRENCIES.NGN,
          COUNTRY_CURRENCY_MAP[storeCountry],
        );

        const domainEntry = {
          ...curr,
          price: convertedPrice,
        };

        if (businessTLDs.some((suffix) => curr.domain.endsWith(suffix))) {
          acc.recommended.push(domainEntry);
        } else {
          acc.alternatives.push(domainEntry);
        }

        return acc;
      }, Promise.resolve({ recommended: [], alternatives: [] }));

    const price = await this.convertDomainPriceToStoreCurrency(
      result.data.price,
      CURRENCIES.NGN,
      COUNTRY_CURRENCY_MAP[storeCountry],
    );

    return {
      domain: result.data.domain,
      available: result.data.available,
      price,
      currency: result.data.currency,
      message: result.data.message,
      alternatives,
      recommended,
    };
  }

  /**
   * Initiate domain purchase process
   */
  async initiateDomainPurchase(storeId: string, domain: string, contactInfo?: any, nameservers?: string[]) {
    // Normalize the domain
    domain = this.normalizeDomain(domain);

    // Validate domain format
    if (!this.validateDomainFormat(domain)) {
      throw new BadRequestException('Invalid domain format');
    }

    // Check if domain is available for purchase - use efficient single domain check
    const availabilityResult = await this.go54Repository.checkSingleDomain(domain);

    if (availabilityResult.error) {
      this.logger.error(`Failed to check domain availability: ${JSON.stringify(availabilityResult.error)}`);
      throw new BadRequestException('Failed to check domain availability');
    }

    if (!availabilityResult.data.available) {
      throw new BadRequestException(`Domain ${domain} is not available for purchase`);
    }

    // Get store details including owner information
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    const amount = await this.convertDomainPriceToStoreCurrency(
      availabilityResult.data.price,
      CURRENCIES.NGN,
      store.currencies.default,
    );

    // Create a domain purchase record
    const domainPurchase = await this.domainPurchaseModel.create({
      domain,
      store: storeId,
      status: DOMAIN_PURCHASE_STATUS.PENDING,
      amount,
      currency: store.currencies.default,
      nameservers: nameservers,
      owner: getDocId(store.owner),
    });

    return domainPurchase;
  }

  /**
   * Complete domain purchase after payment
   */
  async completeDomainPurchase(
    purchaseId: string,
    paymentId?: string,
    nameservers?: string[],
    fromBroker: boolean = true,
  ) {
    const domainPurchase = await this.domainPurchaseModel.findById(purchaseId);

    if (!domainPurchase) {
      this.handleError('Domain purchase record not found', fromBroker);
    }

    if (!paymentId && !domainPurchase.payment_id) {
      this.handleError('Payment ID is required', fromBroker);
    }

    if (!paymentId) {
      paymentId = domainPurchase.payment_id;
    }

    // Get payment details to verify payment was successful
    const payment = await this.brokerTransport
      .send(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, { _id: paymentId })
      .toPromise();

    if (!payment || payment.status !== PAYMENT_STATUS.SUCCESS) {
      this.handleError('Payment was not successful', fromBroker);
    }

    // Get owner details
    const owner = await this.brokerTransport
      .send<any>(BROKER_PATTERNS.USER.GET_USER, { _id: getDocId(domainPurchase.owner) })
      .toPromise();

    if (!owner) {
      this.handleError('We could not find the record of the user tied to this domain purchase', fromBroker);
    }

    // // Prepare contact info from store and owner details
    const ownerName = owner.name.split(' ');
    const firstName = ownerName[0] || '';
    const lastName = ownerName.length > 1 ? ownerName.slice(1).join(' ') : '';

    const contactInfo: UserInfo = {
      firstName: firstName,
      lastName: lastName || firstName, // Use firstName as lastName if lastName is empty
      email: owner.email,
      phone: owner.phone.replace('-', ''),
    };

    // Register the domain with Go54
    const registrationResult = await this.go54Repository.purchaseDomain(
      domainPurchase.domain,
      contactInfo,
      nameservers,
    );

    if (registrationResult.error) {
      this.logger.error(`Failed to register domain: ${JSON.stringify(registrationResult.error)}`);

      // Update the domain purchase status to failed
      await this.domainPurchaseModel.findByIdAndUpdate(purchaseId, {
        status: DOMAIN_PURCHASE_STATUS.FAILED,
        payment_id: paymentId,
        meta: {
          error: registrationResult.error,
        },
      });

      this.handleError(registrationResult.error ?? "Couldn't register domain please try again later", fromBroker);

      return;
    }

    // Update the domain purchase record
    await this.domainPurchaseModel.findByIdAndUpdate(purchaseId, {
      status: DOMAIN_PURCHASE_STATUS.COMPLETED,
      payment_id: paymentId,
      registration_id: registrationResult.data.registrationId,
      expires_at: new Date(registrationResult.data.expiresAt),
    });

    // Add the domain to the store's domains
    const verificationCode = `catlog-verify=${this.generateRandomString(16)}`;

    const domainDoc = await this.storeDomainModel.create({
      domain: domainPurchase.domain,
      store_id: domainPurchase.store,
      verification_code: verificationCode,
      verified: false,
      certificate_issued: false,
    });

    // Queue domain DNS and certificate setup
    this.queueDomainDnsSetup(getDocId(domainDoc));

    //Send Email
    const emailData = {
      to: owner.email,
      subject: 'Your domain purchase was successful ✅',
      data: {
        name: owner.name.split(' ')[0],
        payment_amount: toCurrency(toNaira(payment.amount), payment.currency),
        domain_name: domainPurchase.domain,
        payment_method_name: payment.payment_method.split('_').join(' '),
        date: formatDate(new Date()),
        payment_reference: String(payment.reference),
        cta_link: `${process.env.CATLOG_DASHBOARD}/chowbot`,
      },
    };

    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.DOMAIN_PURCHASE, emailData);

    return {
      domain_purchase: domainPurchase,
      domain: domainDoc,
      registration_response: registrationResult.data,
    };
  }

  /**
   * Get all domain purchases for a store
   */
  async getDomainPurchases(storeId: string) {
    return this.domainPurchaseModel.find({ store_id: storeId }).sort({ createdAt: -1 }).exec();
  }

  /**
   * Get a single domain purchase
   */
  async getDomainPurchase(purchaseId: string) {
    return this.domainPurchaseModel.findById(purchaseId).exec();
  }

  /**
   * Create payment for domain purchase
   */
  async createDomainPurchasePayment(purchaseId: string, paymentMethods: PAYMENT_METHODS[], storeId: string) {
    const domainPurchase = await this.domainPurchaseModel.findById(purchaseId);

    if (!domainPurchase) {
      throw new NotFoundException('Domain purchase record not found');
    }

    if (domainPurchase.status !== DOMAIN_PURCHASE_STATUS.PENDING) {
      throw new BadRequestException('This domain purchase is not in pending status');
    }

    // Get store details
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    // Convert amount to store's currency if different from domain purchase currency
    let amount = domainPurchase.amount;
    let currency = domainPurchase.currency;

    if (store.currencies && store.currencies.default !== domainPurchase.currency) {
      try {
        const convertedAmount = await this.convertDomainPriceToStoreCurrency(
          domainPurchase.amount,
          domainPurchase.currency,
          store.currencies.default,
        );
        amount = convertedAmount;
        currency = store.currencies.default;
        this.logger.log(
          `Converted domain price from ${domainPurchase.amount} ${domainPurchase.currency} to ${amount} ${currency}`,
        );
      } catch (error) {
        this.logger.error(`Failed to convert currency: ${error.message}`, error.stack);
        // If conversion fails, use original currency and amount
      }
    }

    // Create payment using the payment service
    const paymentData = {
      type: PAYMENT_TYPES.DOMAIN_PURCHASE,
      payment_methods: paymentMethods,
      currency: currency,
      amount: amount,
      domain_purchase: getDocId(domainPurchase),
    };

    const paymentResult = await this.brokerTransport
      .send(BROKER_PATTERNS.PAYMENT.CREATE_PAYMENT_FOR_DOMAIN, { body: paymentData, storeId })
      .toPromise();

    return paymentResult;
  }

  /**
   * Convert domain price from one currency to another using currency rates
   * @param amount The amount to convert
   * @param fromCurrency The source currency (usually NGN for domains)
   * @param toCurrency The target currency (store's currency)
   * @returns Converted amount in target currency
   */
  private async convertDomainPriceToStoreCurrency(
    amount: number,
    fromCurrency: CURRENCIES,
    toCurrency: CURRENCIES,
  ): Promise<number> {
    if (fromCurrency === toCurrency) {
      return amount + toNaira(DOMAIN_PURCHASE_MARKUPS[toCurrency]); // No conversion needed
    }

    try {
      // First get rates for the base currency (fromCurrency)
      let rates = await this.cacheService.get(this.ratesCacheKey);

      if (!rates) {
        rates = await this.currencyConversionService.getRatesForCurrency(fromCurrency);
        await this.cacheService.setWithLock(this.ratesCacheKey, rates, 30 * 60); // 30 minutes
      }

      if (!rates || !rates[toCurrency]) {
        // If direct conversion rate not available, try using USD as intermediate
        if (fromCurrency !== CURRENCIES.USD && toCurrency !== CURRENCIES.USD) {
          const usdToFromRate = await this.getExchangeRate(CURRENCIES.USD, fromCurrency);
          const usdToToRate = await this.getExchangeRate(CURRENCIES.USD, toCurrency);

          if (usdToFromRate && usdToToRate) {
            // Convert from source currency to USD, then USD to target currency
            const amountInUsd = amount / usdToFromRate;
            return amountInUsd * usdToToRate;
          }
        }

        throw new Error(`Currency conversion rate not available from ${fromCurrency} to ${toCurrency}`);
      }

      // Direct conversion from fromCurrency to toCurrency
      return amount * rates[toCurrency] + toNaira(DOMAIN_PURCHASE_MARKUPS[toCurrency]);
    } catch (error) {
      this.logger.error(`Currency conversion error: ${error.message}`, error.stack);
      throw new Error(`Failed to convert currency: ${error.message}`);
    }
  }

  /**
   * Helper method to get exchange rate between two currencies
   */
  private async getExchangeRate(fromCurrency: CURRENCIES, toCurrency: CURRENCIES): Promise<number | null> {
    try {
      const rates = await this.currencyConversionService.getRatesForCurrency(fromCurrency);
      return rates[toCurrency] || null;
    } catch (error) {
      this.logger.warn(`Could not get exchange rate from ${fromCurrency} to ${toCurrency}: ${error.message}`);
      return null;
    }
  }

  /**
   * Normalize domain by removing protocol, www prefix and trailing slashes
   */
  private normalizeDomain(domain: string | any): string {
    if (!domain || typeof domain !== 'string') {
      throw new BadRequestException('Invalid domain format: domain must be a string');
    }

    // Remove protocol
    let normalizedDomain = domain.replace(/^https?:\/\//, '');

    // Remove www. prefix
    normalizedDomain = normalizedDomain.replace(/^www\./, '');

    // Remove trailing slash
    normalizedDomain = normalizedDomain.replace(/\/$/, '');

    // Check if domain already has a TLD
    const hasTld = /\.[a-zA-Z]{2,}$/.test(normalizedDomain);

    // If no TLD is present, append the first default TLD
    if (!hasTld) {
      normalizedDomain = normalizedDomain + '.store';
    }

    // Validate the domain format after normalization
    if (!this.validateDomainFormat(normalizedDomain)) {
      throw new BadRequestException('Invalid domain format after normalization');
    }

    return normalizedDomain.toLowerCase();
  }

  /**
   * Validate domain format
   */
  private validateDomainFormat(domain: string): boolean {
    // Basic domain format validation
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z]{2,})+$/;
    return domainRegex.test(domain);
  }

  /**
   * Generate a random string for verification code
   */
  private generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Test Go54 login functionality
   */
  async testGo54Login() {
    try {
      const loginResult = await this.go54Repository.login();

      if (loginResult.error) {
        this.logger.error(`Go54 login failed: ${JSON.stringify(loginResult.error)}`);
        return { error: loginResult.error };
      }

      return { data: loginResult.data };
    } catch (error) {
      this.logger.error(`Exception during Go54 login: ${error.message}`, error.stack);
      return {
        error: {
          message: error.message,
        },
      };
    }
  }

  /**
   * Queue domain DNS setup and certificate generation
   * @param domainId ID of the StoreDomain document
   */
  async queueDomainDnsSetup(domainId: string) {
    // Get domain details
    const domain = await this.storeDomainModel.findById(domainId);

    if (!domain) {
      throw new NotFoundException('Domain not found');
    }

    // Add job to queue for DNS setup and certificate generation
    await this.domainQueue.add(
      JOBS.DOMAIN_DNS_SETUP,
      {
        domainId: getDocId(domain),
        attempts: 0,
        dnsSetupComplete: false, // DNS setup needs to be done initially
      },
      {
        delay: 15 * 60 * 1000, // 15 minutes delay
        attempts: 5,
        backoff: {
          type: 'exponential',
          delay: 30 * 60 * 1000, // 30 minutes between retries
        },
      },
    );

    this.logger.log(`Queued DNS setup for domain ${domain.domain} with ID ${domainId}`);
    return { success: true, message: 'Domain DNS setup queued successfully' };
  }

  handleError(message: string, fromBroker: boolean = true) {
    if (fromBroker) {
      return { error: message };
    } else {
      throw new BadRequestException(message);
    }
  }
}
