import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { Store } from '../store.schema';

export type StoreDomainDocument = StoreDomain & Document;

@Schema({ timestamps: true })
export class StoreDomain {
  @Prop({ type: String, required: true, unique: true })
  domain: string;

  @Prop({ type: Types.ObjectId, ref: 'Store', required: true })
  store_id: string | Store;

  @Prop({ type: Boolean, default: false })
  verified: boolean;

  @Prop({ type: Date })
  verified_at?: Date;

  @Prop({ type: String })
  verification_code?: string;

  @Prop({ type: Boolean, default: false })
  certificate_issued: boolean;

  @Prop({ type: Date })
  certificate_issued_at?: Date;
}

export const StoreDomainSchema = SchemaFactory.createForClass(StoreDomain);
