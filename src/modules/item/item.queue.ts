import { OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { ResendRepository } from '../../repositories/resend.repository';
import { ItemExportImportService } from './item-export-import.service';
import { Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Item, ItemDocument } from './item.schema';
import { OpenaiRepository } from '../../repositories/openai.respository';

export interface ItemJob<T> {
  type: JOBS;
  data: T;
}

interface SendDeletedImagesMailPayload {
  email: string;
  name: string;
  store: string;
  store_slug: string;
}

interface ImportItemsPayload {
  storeId: string;
  data: any[];
  userId: string;
  storeName: string;
}

interface GenerateEmbeddingPayload {
  itemId: string;
}

interface HandleItemValueChangePayload {
  store: string;
}

@Processor(QUEUES.ITEM)
export class ItemQueue {
  private readonly logger = new Logger(ItemQueue.name);

  constructor(
    protected readonly brokerTransport: BrokerTransportService,
    private readonly resend: ResendRepository,
    private readonly itemExportImportService: ItemExportImportService,
    @InjectModel(Item.name) private readonly itemModel: Model<ItemDocument>,
    private readonly openaiRepository: OpenaiRepository,
  ) {}

  @OnQueueActive()
  onActive(job: Job<any>) {
    console.log(`Processing job ${job.id} of type ${job.name} with data ${job.data.name}...`);
  }

  @OnQueueFailed()
  onFailed(job: Job, error: Error) {
    console.log(`Failed to process job ${job.id} of type ${job.name} with data, error: ${error.message}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job, result: any) {
    console.log(`Job ${job.id} of type ${job.name} successfully completed, result: ${result}`);
  }

  @Process(QUEUES.ITEM)
  async send(job: Job<ItemJob<any>>) {
    switch (job.data.type) {
      case JOBS.SEND_DELETED_IMAGES_MAIL:
        return await this.sendDeletedImagesMail(job);
      case JOBS.IMPORT_ITEMS:
        return await this.processImportItems(job);
      case JOBS.GENERATE_EMBEDDING:
        return await this.processGenerateEmbedding(job.data.data);
      default:
    }
  }

  async processGenerateEmbedding(jobData: GenerateEmbeddingPayload) {
    try {
      const { itemId } = jobData;
      this.logger.log(`Generating embedding for item ${itemId}`);

      // Get the item
      const item = await this.itemModel.findById(itemId);
      if (!item) {
        this.logger.warn(`Item ${itemId} not found. Cannot generate embedding.`);
        return { success: false, error: 'Item not found' };
      }

      // Skip if item already has an embedding
      if (item.embedding && item.embedding.length > 0) {
        this.logger.log(`Item ${itemId} already has an embedding. Skipping.`);
        return { success: true, message: 'Item already has embedding' };
      }

      // Generate the embedding
      const embedding = await this.openaiRepository.generateTextEmbedding(item);
      if (!embedding) {
        this.logger.warn(`Failed to generate embedding for item ${itemId}`);
        return { success: false, error: 'Failed to generate embedding' };
      }

      // Update the item with the new embedding
      await this.itemModel.findByIdAndUpdate(itemId, { embedding });

      this.logger.log(`Successfully generated and stored embedding for item ${itemId}`);
      return { success: true };
    } catch (error) {
      this.logger.error(`Error generating embedding: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async sendDeletedImagesMail(job: Job<ItemJob<SendDeletedImagesMailPayload>>) {
    const data = job.data.data;
    this.resend.sendEmail(BROKER_PATTERNS.MAIL.DELETED_PRODUCT_IMAGES, {
      to: data.email,
      subject: '🚨 Important Notice from Catlog',
      data: {
        name: data.name,
        store_name: data.store,
        store_link: 'https://catlog.shop/' + data.store_slug,
        dashboard_link: 'https://catlog.shop/app/dashboard',
      },
    });
  }

  async processImportItems(job: Job<ItemJob<ImportItemsPayload>>) {
    const data = job.data.data;
    return await this.itemExportImportService.processImportJob(data);
  }
}
