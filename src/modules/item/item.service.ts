import mongoose, { ClientSession, FilterQuery, Model, MongooseFilterQuery, PaginateModel, Types } from 'mongoose';

import {
  BadRequestException,
  CACHE_MANAGER,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  OnModuleDestroy,
  OnModuleInit,
  PreconditionFailedException,
} from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { Item, ItemDocument, ItemVariants } from './item.schema';
import {
  CreateCouponDto,
  CreateDiscountDto,
  CreateItemDto,
  DeleteDiscountDto,
  ItemsPaginatedResponse,
  UpdateDiscountDto,
  UpdateItemDto,
  UpdateItemQuantitiesDto,
  CreateItemDtoItem,
  BulkUpdateDto,
  UpdateSortIndexDto,
} from '../../models/dtos/ItemDtos';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { IStoreCategory, Store, StoreDocument } from '../store/store.schema';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import {
  capitalizeFirstLetter,
  checkIfUserOwnsStore,
  getDiscountPrice,
  sluggify,
  stripUnderScoreId,
} from '../../utils';
import { Subscription } from '../subscription/subscription.schema';
import { Plan } from '../plan/plan.schema';
import { PLANS, PLAN_TYPE } from '../../enums/plan.enum';
import { User } from '../user/user.schema';
import { Coupon, CouponDocument, Discount, DiscountDocument } from './discount-coupons/discounts-coupons.schema';

import { convertToObjectIds, isValidObjectId, genChars } from '../../utils';
import { Country } from '../country/country.schema';
import { MailchimpRepository } from '../../repositories/mailchimp.repository';
import { DeliveryArea } from '../store/delivery-areas/delivery-areas.schema';
import { ImportItemsDto } from '../../models/dtos/StoreDtos';
import { IRequest } from '../../interfaces/request.interface';
import { CustomItem, CustomItemDocument } from './custom-items/custom-item.schema';
import { ObjectId } from 'aws-sdk/clients/codecommit';
import { Cache } from 'cache-manager';
import { STORE_PUBLIC_HIDDEN_INFO } from '../../utils/constants';
import { BrevoRepository } from '../../repositories/brevo.repository';
import { getDocId } from '../../utils/functions';
import { QUEUES, JOBS } from '../../enums/queues.enum';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ITEM_SOURCE } from '../../enums/item.enum';
import * as fs from 'fs';
import * as JSONStream from 'JSONStream';
import * as es from 'event-stream';
import { isDefined } from 'class-validator';
import { CustomerIoRepository } from '../../repositories/customer-io.repository';
import { CurrencyRates } from '../wallets/currency-conversion/currency-rate.schema';
import { OpenaiRepository } from '../../repositories/openai.respository';
import { InfoBlock, InfoBlockDocument } from '../store/info-blocks.schema';
import { ItemRecommendationsService } from './item.recommendations.service';

const MAX_FEATURABLE_ITEMS = 10;
let CAN_WATCH_COLLECTION = true;

@Injectable()
export class ItemService implements OnModuleInit, OnModuleDestroy {
  private changeStream;
  private streamJobs: any = {};
  private readonly itemRecommendationsService: ItemRecommendationsService;

  constructor(
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(Item.name)
    private readonly itemModel: PaginateModel<ItemDocument>,
    @InjectModel(CustomItem.name)
    private readonly customItemModel: Model<CustomItemDocument>,
    @InjectModel(Discount.name)
    private readonly discountModel: PaginateModel<DiscountDocument>,
    @InjectModel(Coupon.name)
    private readonly couponModel: PaginateModel<CouponDocument>,
    @InjectModel(InfoBlock.name)
    private readonly infoBlockModel: Model<InfoBlockDocument>,
    private readonly logger: Logger,
    private readonly brevo: BrevoRepository,
    private readonly customerIo: CustomerIoRepository,
    @InjectConnection()
    protected readonly connection: mongoose.Connection,
    @InjectQueue(QUEUES.ITEM) private itemQueue: Queue,
    private readonly openaiRepository: OpenaiRepository,
  ) {
    this.logger.setContext('item.service.ts');

    this.itemRecommendationsService = new ItemRecommendationsService(this.itemModel, this.openaiRepository);
  }

  async onModuleInit() {
    if (CAN_WATCH_COLLECTION && process.env.USE_CHANGE_STREAMS !== 'false') {
      CAN_WATCH_COLLECTION = false;
      this.watchItemsCollection();
      return;
    }
  }

  async onModuleDestroy() {
    if (this.changeStream) await this.changeStream.close();
  }

  watchItemsCollection() {
    this.changeStream = this.itemModel.watch([], { fullDocument: 'updateLookup' });

    this.changeStream.on(
      'change',
      async (change: {
        _id: {
          _data: string;
        };
        fullDocument: Item;
        operationType: string;
        updateDescription: {
          updatedFields: any;
          removedFields: any;
          truncatedArrays: any;
        };
      }) => {
        const dbItem = change.fullDocument;
        if (dbItem?.is_menu_item != true && change.fullDocument?.meta?.chowdeck_reference === undefined) return;

        const triggerOnFields = [
          'price',
          'variants',
          'available',
          'is_always_available',
          'discount_price',
          'is_deleted',
          'category',
          'name',
          'description',
          'quantity',
        ];

        const updatedFields = change.updateDescription?.updatedFields;
        const updatedFieldsKeys = Object.keys(updatedFields ?? {});
        const filteredFieldKeys = updatedFieldsKeys.filter((field) => triggerOnFields.includes(field));
        let canTrigger = filteredFieldKeys.length > 0;
        let shouldRefreshMenu = true;

        if (filteredFieldKeys.every((f) => ['quantity', 'variants', 'is_always_available', 'price'].includes(f))) {
          const streamJob = this.streamJobs[getDocId(dbItem.store)];
          const timeoutId = setTimeout(async () => {
            let batchHasSignificantChange = false;
            const jobs = this.streamJobs[getDocId(dbItem.store)].items as any[];

            if (jobs.some((job) => job?.change?.price || job?.change?.is_always_available)) {
              batchHasSignificantChange = true;
            } else {
              if (
                jobs.some(
                  (job) =>
                    (job.change?.quantity === 0 && job.history?.quantity > 0) ||
                    (job.history?.quantity === 0 && job.change?.quantity > 0),
                )
              ) {
                batchHasSignificantChange = true;
              } else if (jobs.some((job) => Boolean(job.change?.variants))) {
                batchHasSignificantChange = true;
              }
            }

            if (batchHasSignificantChange) {
              console.log('<======', 'GENERATING NEW MENU FROM BATCH', '=====>');
              await this.brokerTransport
                .send<any>(BROKER_PATTERNS.WHATSAPP_BOT.REFRESH_STORE_MENU, getDocId(dbItem.store))
                .toPromise();
            }

            // CLEAN UP
            clearTimeout(timeoutId);
            delete this.streamJobs[getDocId(dbItem.store)];
          }, 2000);

          if (streamJob) {
            clearTimeout(streamJob.timeoutId);
          }

          this.streamJobs[getDocId(dbItem.store)] = {
            timeoutId,
            items: [
              ...(streamJob?.items ?? []),
              {
                change: updatedFields,
                history: dbItem?.meta?.history ?? updatedFields,
              },
            ],
          };
          shouldRefreshMenu = false;
        }

        if (canTrigger) {
          if (change.operationType === 'update' && change.fullDocument?.is_menu_item === true && shouldRefreshMenu) {
            await this.brokerTransport
              .send<any>(BROKER_PATTERNS.WHATSAPP_BOT.REFRESH_STORE_MENU, getDocId(change.fullDocument?.store))
              .toPromise();
          }

          // if (change.operationType === 'update' && change.fullDocument?.meta?.chowdeck_reference) {
          //   await this.brokerTransport
          //     .send<any>(BROKER_PATTERNS.STORE.AUTO_SYNC_ITEM_TO_CHOWDECK, change.fullDocument)
          //     .toPromise();
          // }
        }
      },
    );
  }

  async create(user: IRequest['user'], itemReq: CreateItemDto) {
    const store = await this.getStore({ _id: itemReq.store as any });
    const subscription = user?.store?.subscription ?? user?.subscription;

    const itemsCount = await this.itemModel.countDocuments({
      store: store.id,
      is_deleted: { $ne: true },
    });

    if (!subscription) {
      if (itemsCount >= PLANS.MAX_UPLOADS.STARTER) {
        throw new BadRequestException(
          `You've reached the product limit for your plan you cannot upload more than 10 products`,
        );
      }
    } else if (itemsCount >= PLANS.MAX_UPLOADS[(subscription.plan as Plan).type]) {
      throw new BadRequestException(
        `You've reached the product limit for your plan you cannot upload more than ${
          PLANS.MAX_UPLOADS[(subscription.plan as Plan).type]
        } products`,
      );
    }

    if (store?.configuration?.direct_checkout_enabled) {
      for (const i of itemReq.items) {
        const r = this.checkIfHasQuantity(i?.quantity, i?.is_always_available, i.variants);

        if (!r) {
          throw new PreconditionFailedException('Product must have a quantity or be always available');
        }
      }
    }

    const instagramItems = itemReq.items.filter((item) => item.upload_source === ITEM_SOURCE.INSTAGRAM);
    const numInstagramItems = instagramItems.length;

    const lastSortedItem = await this.itemModel
      .find({ store: getDocId(store) as any })
      .sort({ sort_index: -1 })
      .limit(1);
    let lastSortIndex = lastSortedItem[0]?.sort_index;
    lastSortIndex = lastSortIndex ? lastSortIndex : 0;

    const itemsToSave: (ItemDocument | Item)[] = [];
    for (let i = 0; i < itemReq.items.length; i++) {
      const itemData = itemReq.items[i];
      const index = i;

      if (!isValidObjectId(itemData.category)) {
        delete itemData.category;
      }

      if (itemData.variants && Array.isArray(itemData.variants.options)) {
        itemData.variants.options = itemData.variants.options.map((option) => {
          option._id = option._id || new mongoose.Types.ObjectId();
          return option;
        });
      }

      const newItem = new this.itemModel({
        ...itemData,
        is_menu_item: Boolean(store?.configuration?.send_menu_on_initiation),
        slug: this.generateItemSlug(itemData.name),
        store: store.id,
        sort_index: lastSortIndex + index + 1,
      });

      // Instead of generating embedding here, we'll queue it
      itemsToSave.push(newItem);
    }

    const savedItems = await this.itemModel.insertMany(itemsToSave);

    // Queue embedding generation for each item
    for (const item of savedItems) {
      this.itemQueue.add(QUEUES.ITEM, {
        type: JOBS.GENERATE_EMBEDDING,
        data: {
          itemId: getDocId(item),
        },
      });
    }

    // Update info blocks for new items
    for (const item of savedItems) {
      if (item.info_blocks && item.info_blocks.length > 0) {
        await this.addItemToInfoBlocks(getDocId(item), item.info_blocks as string[]);
      }
    }

    // Update store's instagram_item_upload_count (if any Instagram items)
    if (numInstagramItems > 0) {
      await this.updateStore(store.id, { $inc: { 'meta.instagram_item_upload_count': numInstagramItems } });
    }

    if (store?.configuration?.send_menu_on_initiation) {
      await this.brokerTransport.send<any>(BROKER_PATTERNS.WHATSAPP_BOT.REFRESH_STORE_MENU, store?.id).toPromise();
    }

    this.brevo.addUserProducts(user.email, itemsCount + savedItems.length);

    if (user?.primary_store && getDocId(store).toString() === getDocId(user?.primary_store).toString()) {
      //extra check to prevent block from running during tests
      this.customerIo.createOrUpdateUser({
        id: getDocId(user),
        email: user?.email,
        products_count: itemsCount + savedItems.length,
      });
    }

    //update store items count
    await this.brokerTransport
      .send<any>(BROKER_PATTERNS.STORE.UPDATE_ITEMS_COUNT, { store: getDocId(store), inc: savedItems.length })
      .toPromise();

    // for (const it of items) {
    //   const itemJson = it.toJSON();
    //   itemJson._id = it._id || it.id;

    //   this.botQueue.add(JOBS.TWEET_PRODUCT, itemJson, {
    //     delay: 1000 * 60 * 60,
    //   }); // 60 minutes
    // }

    return savedItems;
  }

  async updateItemQuantities(storeId: string, itemsReq: UpdateItemQuantitiesDto) {
    const requests: Promise<ItemDocument>[] = [];

    for (let key in itemsReq.items) {
      const item = itemsReq.items[key];
      const _id = item.id;
      const variants = item.variants;

      requests.push(
        this.itemModel
          .findByIdAndUpdate(_id, {
            quantity: item.quantity,
            is_always_available: item.is_always_available,
          })
          .exec(),
      );

      if (variants) {
        const itemDoc = await this.itemModel.findOne({
          _id,
          store: storeId as any,
        });
        const docOptions = itemDoc.variants?.options?.map((o) => o._id) ?? [];
        const options = variants.options?.filter((o) => docOptions.includes(o.id)) ?? [];
        variants.options = options;
        itemDoc.variants = variants;

        requests.push(itemDoc.save());
      }
    }

    await Promise.all(requests);

    return {};
  }

  async checkItemQuantities(storeId: ObjectId) {
    const allItems = await this.itemModel.find({ store: storeId as any, is_deleted: { $ne: true } }).lean();
    const itemsWithoutQuantities: Item[] = [];

    for (let item of allItems) {
      if (item.is_always_available) {
        continue;
      }

      if (item.quantity === null || item.quantity === undefined) {
        itemsWithoutQuantities.push(item);
        continue;
      }

      if (item?.variants?.options.length > 0) {
        for (let variant of item.variants.options) {
          if (variant.quantity === null || variant.quantity === undefined) {
            itemsWithoutQuantities.push(item);
            break;
          }
        }
      }
    }

    return itemsWithoutQuantities;
  }

  async bulkUpdate(storeId: string, itemsReq: BulkUpdateDto) {
    // Assuming BulkUpdateItemDto might look like: { id: string; price?: number; quantity?: number; variants?: ItemVariants; is_always_available?: boolean; }
    const updates = itemsReq.items.map(async (itemUpdate) => {
      // Only destructure fields known to be in BulkUpdateItemDto
      const { id, is_always_available, price, quantity, variants } = itemUpdate;
      const updatePayload: Partial<ItemDocument> = {};

      if (price !== undefined) updatePayload.price = price;
      if (variants !== undefined) updatePayload.variants = variants as ItemVariants; // Assuming variants here matches ItemVariants structure
      if (quantity !== undefined && quantity > -1) updatePayload.quantity = quantity;
      if (is_always_available !== undefined) updatePayload.is_always_available = is_always_available;

      // Check if embedding needs update - only possible if variants change in this DTO
      const needsEmbeddingUpdate = itemUpdate.hasOwnProperty('variants');

      if (Object.keys(updatePayload).length > 0) {
        // Cast storeId to any to resolve type mismatch if schema expects ObjectId
        const result = await this.itemModel.updateOne({ _id: id, store: storeId as any }, updatePayload).lean();

        // Queue embedding generation if needed
        if (needsEmbeddingUpdate && variants !== undefined) {
          this.itemQueue.add(QUEUES.ITEM, {
            type: JOBS.GENERATE_EMBEDDING,
            data: {
              itemId: id,
            },
          });
        }

        return result;
      } else {
        return Promise.resolve(null); // No update needed for this item
      }
    });

    const results = await Promise.all(updates);
    // Filter out null results if any items didn't need updating
    return results.filter(Boolean);
  }

  async update(itemId: string, itemReq: UpdateItemDto, store: IRequest['user']['store']) {
    const itemDb = await this.itemModel.findById(itemId);
    if (!itemDb) {
      throw new PreconditionFailedException('Item does not exist');
    }

    if (itemReq.variants && itemReq.variants.options) {
      itemReq.variants.options.map((option) => {
        option._id = option?._id || (option as any)?.id || mongoose.Types.ObjectId();
      });
    }

    if (store?.configuration?.direct_checkout_enabled) {
      const r = this.checkIfHasQuantity(
        itemReq.quantity ?? itemDb.quantity,
        itemReq.is_always_available ?? itemDb.is_always_available,
        itemReq.variants ?? itemDb.variants,
      );

      if (!r) throw new PreconditionFailedException('Product must have a quantity or be always available');
    }

    if (itemReq?.is_featured) {
      delete itemReq.is_featured;
    }

    // Prepare update data
    const updateData: Partial<ItemDocument> = {
      ...itemReq,
      slug:
        itemDb.name === itemReq.name || itemReq.name === undefined ? itemDb.slug : this.generateItemSlug(itemReq.name),
    };

    // Conditionally add quantity and is_always_available
    if (itemReq.quantity !== undefined && itemReq.quantity > -1) {
      updateData.quantity = itemReq.quantity;
    }
    if (itemReq.is_always_available !== undefined) {
      updateData.is_always_available = itemReq.is_always_available;
    }

    // Handle info_blocks field - preserve existing if not provided in the update
    if (itemReq.info_blocks !== undefined) {
      const oldInfoBlocks = (itemDb.info_blocks as string[]) ?? [];
      updateData.info_blocks = itemReq.info_blocks;

      const newInfoBlocks: string[] = itemReq.info_blocks ?? [];
      const infoBlocksToRemove = oldInfoBlocks.filter((block: string) => !newInfoBlocks.includes(block));
      const infoBlocksToAdd = newInfoBlocks.filter((block: string) => !oldInfoBlocks.includes(block));

      await this.removeItemFromInfoBlocks(itemId, infoBlocksToRemove);
      await this.addItemToInfoBlocks(itemId, infoBlocksToAdd);
    }

    // Check if fields that affect embedding have changed
    const fieldsForEmbedding = ['name', 'description', 'category', 'variants'];
    const needsEmbeddingUpdate = fieldsForEmbedding.some((field) => itemReq.hasOwnProperty(field));

    const updatedItem = await this.itemModel.findByIdAndUpdate(
      itemId,
      updateData,
      { new: true }, // Return the updated document
    );

    // Queue embedding generation if needed
    if (needsEmbeddingUpdate) {
      this.itemQueue.add(QUEUES.ITEM, {
        type: JOBS.GENERATE_EMBEDDING,
        data: {
          itemId: getDocId(updatedItem),
        },
      });
    }

    const itemJson = updatedItem.toJSON();
    itemJson._id = updatedItem._id || updatedItem.id;

    return updatedItem;
  }

  async updateFeaturedItem(itemId: string, itemReq: UpdateItemDto, store: IRequest['user']['store']) {
    const itemDb = await this.itemModel.findById(itemId);
    if (itemDb === undefined) {
      throw new PreconditionFailedException('Item does not exist');
    }

    if (itemReq?.is_featured) {
      const featuredItemsCount = await this.itemModel.countDocuments({
        store: store?.id as any,
        is_featured: true,
        is_deleted: { $ne: true },
      });

      if (featuredItemsCount >= MAX_FEATURABLE_ITEMS) {
        throw new BadRequestException(`Cannot Feature More than ${MAX_FEATURABLE_ITEMS} item`);
      }
    }

    const item = await this.itemModel.findByIdAndUpdate(itemId, { is_featured: itemReq.is_featured }, { new: true });

    const itemJson = item.toJSON();
    itemJson._id = item._id || item.id;

    return item;
  }

  async updateSortIndexes(store: string, items: UpdateSortIndexDto[]) {
    const bulkOps = items.map((item) => ({
      updateOne: {
        filter: { _id: mongoose.Types.ObjectId(item.id), store: mongoose.Types.ObjectId(store) },
        update: { $set: { sort_index: item.sort_index } },
      },
    }));

    const updatedItems = await this.itemModel.bulkWrite(bulkOps);

    return updatedItems;
  }

  async getVariantTemplates(storeId: string) {
    const items = await this.itemModel.find({ store: storeId as any, 'variants.is_template': true });
    return items.map((i) => i.variants);
  }

  async delete(itemId: string, req: IRequest) {
    const item = await this.itemModel.findById(itemId);
    checkIfUserOwnsStore(req.user.store as Store, (item.store as unknown) as string);
    item.is_deleted = true;
    item.save();

    const itemJson = item.toJSON();
    itemJson._id = item._id || item.id;

    await this.brokerTransport
      .send<any>(BROKER_PATTERNS.STORE.UPDATE_ITEMS_COUNT, { store: item.store, inc: -1 })
      .toPromise();

    // this.botQueue.add(JOBS.DELETE_PRODUCT_TWEET, itemJson, {
    //   delay: 1000 * 60 * 30, // 30 minutes
    // });

    return item;
  }

  async getItemBySlugOrId(slugOrId: string) {
    let item: ItemDocument;

    if (isValidObjectId(slugOrId)) {
      item = await this.itemModel.findById(slugOrId).populate('store');
    } else {
      item = await this.itemModel.findOne({ slug: slugOrId }).populate('store');
    }

    if (!item) {
      throw new NotFoundException('Item is not found');
    }

    item = item.toJSON();
    item.category = item.store.categories.find((category) => {
      return item.category == category.id;
    });
    [item.category] = stripUnderScoreId([item.category]);

    const itemWithDiscount = await this.computeItemDiscount(item);

    if (item.info_blocks && item.info_blocks.length > 0) {
      const infoBlocksData = await this.infoBlockModel.find(
        {
          _id: { $in: item.info_blocks },
        },
        { title: 1, content_type: 1, is_visible: 1, text_content: 1, image_content: 1 },
      );

      itemWithDiscount.info_blocks = infoBlocksData.map((infoBlock) => infoBlock.toJSON());
    }

    return itemWithDiscount;
  }

  async getItemPublicBySlugOrId(slugOrId: string) {
    let item: ItemDocument;
    if (isValidObjectId(slugOrId)) {
      item = await this.itemModel
        .findOne({ id: slugOrId, is_deleted: { $ne: true } })
        .populate('store', STORE_PUBLIC_HIDDEN_INFO);
    } else {
      item = await this.itemModel
        .findOne({ slug: slugOrId, is_deleted: { $ne: true } })
        .populate('store', STORE_PUBLIC_HIDDEN_INFO);
    }

    if (!item) {
      throw new NotFoundException('Item is not found');
    }

    item = item.toJSON();
    // item.category = item.store.categories.find((category) => {
    //   return item.category == category.id;
    // });
    // [item.category] = stripUnderScoreId([item.category]);

    item.store.country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: item.store.country,
      })
      .toPromise();

    item.store.delivery_areas = await this.brokerTransport
      .send<DeliveryArea[]>(BROKER_PATTERNS.STORE.GET_DELIVERY_AREAS, item.store.delivery_areas)
      .toPromise();

    item.store.has_paid_subscription = await this.brokerTransport
      .send<boolean>(BROKER_PATTERNS.STORE.PAID_SUBSCRIPTION_STATUS, item.store)
      .toPromise();

    if (item?.store.currencies?.storefront.length > 1) {
      const rates = await this.brokerTransport
        .send<CurrencyRates>(BROKER_PATTERNS.WALLET.GET_EXCHANGE_RATES, {})
        .toPromise();
      item.store.meta = { rates } as any;
    }

    const discounts = await this.discountModel.find({
      store: item.store.id,
    });

    const parsedItem = await this.resolveCouponsAndCategories([item], discounts, item.store);
    return parsedItem[0];
  }

  async getStoreItemsCount(storeId: string) {
    const count = await this.itemModel.countDocuments({
      store: storeId as any,
      is_deleted: { $ne: true },
    });

    return count;
  }

  computeItemsDiscount(itemDocs: ItemDocument[], discounts: DiscountDocument[]) {
    const items = [...itemDocs];

    // if (discounts && discounts.length > 0) {
    const discountMap = new Map<string, DiscountDocument>();
    for (let discount of discounts) {
      discountMap.set(discount._id.toString(), discount);
    }

    for (let item of items) {
      const { price, discount, variants } = item;

      const itemDiscount = discount ? discountMap.get(discount.toString()) : undefined;

      if (itemDiscount) {
        if (variants) {
          for (let option of variants.options) {
            option.discount_price = getDiscountPrice(option.price, itemDiscount);
          }
        }

        item.discount_price = getDiscountPrice(price, itemDiscount);
      } else if (item?.discount_price && item?.discount_price > 0) {
        //add the same discount difference when a discount is added from form directly
        const discountAmount = price - item.discount_price;

        if (variants) {
          for (let option of variants.options) {
            option.discount_price = option.price - discountAmount;
          }
        }
      } else {
        delete item.discount_price;
      }

      delete item.discount;
    }
    // }
    return items;
  }

  async computeItemDiscount(itemDoc: ItemDocument) {
    const item = { ...itemDoc };
    const { price } = item;
    const discount = await this.discountModel.findOne({ _id: item.discount });

    if (discount) {
      item.discount_price = getDiscountPrice(price, discount);
      delete item.discount;
    } else {
      item.discount_price <= 0 && delete item.discount_price;
    }
    return item;
  }

  /**
   * Fetches and returns the paginated store items
   * @param storeIdOrSlug
   * @param paginationQuery
   * @param publicCall
   */
  async getPaginatedStoreItems(
    { search, ...filterQuery }: FilterQuery<Item> & { store: any },
    paginationQuery: { showUnavailableItems?: string; separateFeaturedItems?: string } & PaginatedQueryDto,
    publicCall = false,
  ): Promise<ItemsPaginatedResponse> {
    let maxPages: number;
    let featuredItems: ItemDocument[] = [];

    const separateFeaturedItems = paginationQuery?.separateFeaturedItems === 'false' ? false : true;

    const response = {
      data: { store: {} as Store, items: null },
      page: Number(paginationQuery.page),
      next_page: null,
      prev_page: null,
      total: null,
      total_pages: null,
      sort: paginationQuery.sort || '',
      per_page: Number(paginationQuery.per_page),
    };

    const store = await this.getStore({
      $or:
        filterQuery.store && isValidObjectId(filterQuery.store)
          ? [{ _id: filterQuery.store }]
          : [{ slug: filterQuery.store }],
    });

    if (publicCall) {
      const subscription = await this.brokerTransport
        .send<Subscription>(BROKER_PATTERNS.PAYMENT.GET_SUBSCRIPTION, {
          owner: store.owner,
        })
        .toPromise();

      //helps prevent stores fetching more than store limit
      const maxProducts = subscription ? PLANS.MAX_UPLOADS[(subscription.plan as Plan).type] : 10; // If they don't have a sub then just 10

      if (maxProducts < Number.POSITIVE_INFINITY) {
        response.per_page = response?.per_page > maxProducts ? maxProducts : response.per_page;
        maxPages = Math.ceil(maxProducts / response.per_page);
        response.page = response?.page > maxPages ? maxPages : response.page;
      }
    }

    const filter: FilterQuery<Item> = {
      ...filterQuery,
      is_deleted: { $ne: true },
      store: store.id,
    };

    if (search) {
      filter.$or = [{ name: new RegExp(search, 'ig') }, { description: new RegExp(search, 'ig') }];
    }

    let result;

    if (!publicCall) {
      result = await this.itemModel.paginate(filter, {
        sort: {
          sort_index: -1,
          _id: -1,
        },
        page: response.page || 1,
        limit: response.per_page || 50,
        lean: true,
      });
    } else {
      const filterUnavailableItems =
        store?.configuration?.show_unavailable_products || paginationQuery?.showUnavailableItems === 'true'
          ? { available: true }
          : {
              $and: [
                { available: true },
                { $or: [{ quantity: { $gt: 0 } }, { quantity: { $exists: false } }, { is_always_available: true }] },
              ],
            };

      const itemsWithoutFeatured = { is_featured: { $ne: true } };
      const mainFilter = {
        ...filter,
        ...filterUnavailableItems,
        ...(separateFeaturedItems ? itemsWithoutFeatured : {}),
      };

      result = await this.itemModel.paginate(mainFilter, {
        sort: {
          sort_index: -1,
          _id: -1,
        },
        page: response.page || 1,
        limit: response.per_page || 50,
        lean: true,
      });

      if (response.page === 1 && separateFeaturedItems) {
        featuredItems = await this.itemModel.find({ ...filter, ...filterUnavailableItems, is_featured: true });
      }
    }

    let itemsWithResolvedCategories = {
      featuredItems: [],
      otherItems: [],
    };

    if (result.docs.length > 0 || featuredItems.length > 0) {
      const discounts = await this.discountModel.find({
        store: store.id,
      });

      const items = await Promise.all(
        [featuredItems, result.docs as ItemDocument[]].map((iS) =>
          this.resolveCouponsAndCategories(iS, discounts, store),
        ),
      );

      itemsWithResolvedCategories = {
        featuredItems: items[0] as ItemDocument[],
        otherItems: items[1] as ItemDocument[],
      };
    }

    if (publicCall) {
      itemsWithResolvedCategories = {
        featuredItems: itemsWithResolvedCategories.featuredItems.map((item) => {
          delete item.views;
          return item;
        }),
        otherItems: itemsWithResolvedCategories.otherItems.map((item) => {
          delete item.views;
          return item;
        }),
      };
    }

    return {
      ...response,
      data: {
        store: publicCall ? <Store>store.id : store,
        items: publicCall ? itemsWithResolvedCategories : itemsWithResolvedCategories.otherItems,
      },
      page: result.page,
      next_page: maxPages && result.nextPage > maxPages ? maxPages : result.nextPage,
      prev_page: result.prevPage,
      total: result.totalDocs,
      total_pages: maxPages ?? result.totalPages,
      per_page: result.limit,
    };
  }

  async findRelatedProducts(itemId: string, limit: number = 5): Promise<ItemDocument[]> {
    const items = await this.itemRecommendationsService.findRelatedProducts(itemId, limit);

    let itemsWithComputedDiscount: ItemDocument[] = items;

    if (items.length > 0) {
      const discounts = await this.discountModel.find({
        store: items[0].store,
      });

      const storeDoc = await this.brokerTransport
        .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, {
          _id: items[0].store,
        })
        .toPromise();

      if (storeDoc) {
        itemsWithComputedDiscount = await this.resolveCouponsAndCategories(items, discounts, storeDoc);
      }
    }

    return itemsWithComputedDiscount;
  }

  async paginateItemsBySortIndex(store: string) {
    const items = await this.itemModel
      .find(
        { is_deleted: { $ne: true }, reference: { $exists: false }, store: store as any },
        { _id: 1, name: 1, images: 1, sort_index: 1, thumbnail: 1, price: 1 },
      )
      .sort({ sort_index: -1, _id: -1 });

    return {
      items,
    };
  }

  async getItemsAndPopulateRelations(itemIds: string[], storeId: string) {
    const items = await this.itemModel.find({ _id: { $in: itemIds }, store: storeId as any });
    const store = await this.getStore({ _id: storeId });

    const discounts = await this.discountModel.find({
      store: getDocId(store),
    });

    const itemsWithResolvedCategories = await this.resolveCouponsAndCategories(items, discounts, store);
    return itemsWithResolvedCategories;
  }

  async resolveCouponsAndCategories(itemDocs: ItemDocument[], discounts: DiscountDocument[], store: Store) {
    const itemsWithComputedDiscount = this.computeItemsDiscount(itemDocs, discounts);
    const itemsWithResolvedCategories = this.resolveCategories(itemsWithComputedDiscount as ItemDocument[], store);

    // Check if any items have info blocks before proceeding
    const hasInfoBlocks = itemsWithResolvedCategories.some((item) => item.info_blocks && item.info_blocks.length > 0);

    if (hasInfoBlocks) {
      try {
        // Fetch all visible info blocks for the store in a single query
        const infoBlocksData = await this.infoBlockModel
          .find(
            {
              store: getDocId(store) as any,
              is_visible: true,
            },
            { title: 1, content_type: 1, is_visible: 1, text_content: 1, image_content: 1 },
          )
          .lean();

        // Create a lookup map for quick access
        const infoBlocksMap = new Map();
        for (const block of infoBlocksData) {
          infoBlocksMap.set(block._id.toString(), {
            _id: block._id,
            title: block.title,
            content_type: block.content_type,
            text_content: block.text_content,
            image_content: block.image_content,
          });
        }

        // Assign info blocks to each item
        for (const item of itemsWithResolvedCategories) {
          const resolvedItem: any = item;
          if (resolvedItem.info_blocks && resolvedItem.info_blocks.length > 0) {
            resolvedItem.info_blocks = resolvedItem.info_blocks
              .map((blockId) => infoBlocksMap.get(blockId.toString()))
              .filter(Boolean); // Filter out any undefined values
          }
        }
      } catch (error) {
        this.logger.error(`Error resolving info blocks for store items: ${error.message}`);
      }
    }

    return itemsWithResolvedCategories;
  }

  async getTopItems(data: FilterQuery<ItemDocument>) {
    const items = await this.itemModel.find(data).sort({ total_orders: -1 }).limit(10);
    if (items.length === 0) {
      return items;
    }
    const store = await this.getStore({ _id: items[0].store });
    return this.resolveCategories(items, store);
  }

  resolveCategories(items: ItemDocument[], store: Store) {
    return items.map((item) => {
      item = item.toJSON ? item.toJSON() : item;

      item.category = store.categories?.find((category) => {
        return item.category && category.id === item.category.toString();
      });

      [item.category] = stripUnderScoreId([item.category]);
      return item;
    });
  }

  checkIfHasQuantity(quantity: number, available: boolean, variants: Item['variants']) {
    if (!(quantity > -1 || available)) return false;

    if (variants?.options?.length > 0 && available !== true) {
      for (const v of variants?.options) {
        if (!(v.quantity > -1)) return false;
      }
    }

    return true;
  }

  async getStore(filter: FilterQuery<StoreDocument>) {
    const store = await this.brokerTransport.send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, filter).toPromise();

    if (!store) {
      throw new BadRequestException('store does not exist');
    }
    return store;
  }

  async updateStore(storeId: string, update) {
    const store = await this.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
        filter: { _id: storeId },
        update,
      })
      .toPromise();

    return store;
  }

  async getItem(filter: FilterQuery<StoreDocument>) {
    return await this.itemModel.findOne(filter).populate('categories').exec();
  }

  async getItems(filter: FilterQuery<StoreDocument>) {
    return await this.itemModel.find(filter).populate('categories').exec();
  }

  async getItemsWithResolvedDiscounts(filter: FilterQuery<StoreDocument>) {
    const items = await this.itemModel.find(filter).populate('categories').exec();

    if (!items || items?.length < 1) return [];

    const discounts = await this.discountModel.find({
      store: items[0].store,
    });

    const itemsWithComputedDiscount = this.computeItemsDiscount(items, discounts);

    return itemsWithComputedDiscount;
  }

  async getSortedItems(filter: FilterQuery<StoreDocument>) {
    // return await this.itemModel.find(filter).sort({createdAt: 1}).populate('categories').exec();

    return await this.itemModel.find(filter, '_id').sort({ createdAt: 1 }).exec();
  }

  async getItemSeo() {
    const plan = await this.brokerTransport
      .send(BROKER_PATTERNS.PLAN.GET_PLAN, {
        type: { $ne: PLAN_TYPE.STARTER },
      })
      .toPromise();

    return this.itemModel.aggregate([
      {
        $lookup: {
          from: 'stores',
          localField: 'store',
          foreignField: '_id',
          as: 'store',
        },
      },

      { $unwind: '$store' },

      {
        $lookup: {
          from: 'users',
          localField: 'store.owner',
          foreignField: '_id',
          as: 'user',
        },
      },

      { $unwind: '$user' },

      {
        $lookup: {
          from: 'subscriptions',
          localField: 'user._id',
          foreignField: 'owner',
          as: 'subscription',
        },
      },

      { $unwind: '$subscription' },

      { $match: { 'subscription.plan': Types.ObjectId(plan.id) } },

      { $project: { slug: 1, _id: 0 } },
    ]);
  }

  async incrementItemView(filter: FilterQuery<ItemDocument>) {
    return this.itemModel.updateOne(filter, { $inc: { views: 1 } }).exec();
  }

  async incrementItemTotalOrder(filter: FilterQuery<ItemDocument>, total_orders: number) {
    return this.itemModel.updateOne(filter, { $inc: { total_orders } });
  }

  async countItems(data: MongooseFilterQuery<ItemDocument>) {
    return this.itemModel.countDocuments({
      ...data,
      is_deleted: { $ne: true },
    });
  }

  async getSitemapPage(page: number, per_page: number) {
    const result = await this.itemModel.paginate(
      { is_deleted: { $ne: true }, reference: { $exists: false } },
      {
        sort: {
          _id: 1,
        },
        page: page,
        limit: per_page,
        lean: true,
        projection: { name: 1, slug: 1 },
      },
    );

    return result.docs;
  }

  getItemModel() {
    return this.itemModel;
  }

  async createDiscount(req: IRequest, data: CreateDiscountDto) {
    const user = req.user;
    const { items } = data;
    if (items && items.length > 0) {
      const discount = await this.discountModel.create({
        ...data,
        store: user.store.id,
      });
      const itemData = { discount: discount.id };
      const ids = convertToObjectIds(items as string[]);

      await this.itemModel.updateMany({ _id: { $in: [...ids] } }, itemData);
      return discount;
    }
    throw new BadRequestException('Cannot create discount without item data');
  }

  async getDiscounts(store: string) {
    return this.discountModel.find({ store });
  }

  async updateDiscount(data: UpdateDiscountDto) {
    const { id: _id, items } = data;
    delete data.id;

    const prevDiscount = await this.discountModel.findOne({ _id });
    if (!prevDiscount) throw new BadRequestException('Discount does not exitst');

    if (items && items.length > 0) {
      const updatedDiscount = await this.discountModel.findOneAndUpdate({ _id }, { ...data }, { new: true });
      const prevItemsIds = convertToObjectIds(prevDiscount.items as string[]);
      const currItemsIds = convertToObjectIds(updatedDiscount.items as string[]);
      const prevItemsUpdate = { discount: undefined, discount_price: null };
      const currItemsUpdate = {
        discount: updatedDiscount.id,
        discount_price: null,
      };

      await this.itemModel.updateMany({ _id: { $in: [...prevItemsIds] } }, prevItemsUpdate);
      await this.itemModel.updateMany({ _id: { $in: [...currItemsIds] } }, currItemsUpdate);

      return updatedDiscount;
    }
    return await this.discountModel.findOneAndUpdate({ _id }, { ...data }, { new: true });
  }

  async deleteDiscount(data: DeleteDiscountDto) {
    await this.itemModel.updateMany({ discount: data.id }, { discount: undefined });
    return this.discountModel.findOneAndDelete({
      _id: data.id,
    });
  }

  async createCoupon(user: IRequest['user'], data: CreateCouponDto) {
    const current = await this.couponModel.findOne({
      coupon_code: data.coupon_code.toUpperCase(),
      store: user?.store.id,
    });
    if (current) {
      throw new BadRequestException('Coupon with code already exists');
    }

    if (data.type === 'fixed' && data.discount_amount) {
      data.percentage = null;
      data.discount_cap = null;

      const coupon = await this.couponModel.create({
        ...data,
        store: user.store.id,
      });
      return coupon;
    } else if (data.type === 'percentage' && data.percentage) {
      data.discount_amount = null;
      const coupon = await this.couponModel.create({
        ...data,
        store: user.store.id,
      });

      return coupon;
    }
    throw new BadRequestException('cannot create discount without type data');
  }

  async getCoupons(user: IRequest['user']) {
    return this.couponModel.find({ store: user.store.id });
  }

  async getCouponByCode(user: IRequest['user'], code: string) {
    return this.couponModel.findOne({
      coupon_code: code.toUpperCase(),
      store: user.store.id,
    });
  }

  async trackOrderCoupon(order: { order_id: string; coupon_code: string; store: string; shouldCancelOrder?: boolean }) {
    const couponCode = order.coupon_code.toUpperCase();
    if (order.shouldCancelOrder === true) {
      return this.couponModel.findOneAndUpdate(
        { coupon_code: couponCode, store: order.store },
        { $inc: { quantity: 1 }, $pull: { orders: order.order_id } },
        { useFindAndModify: false },
      );
    }
    return this.couponModel.findOneAndUpdate(
      { coupon_code: couponCode, store: order.store },
      { $inc: { quantity: -1 }, $push: { orders: order.order_id } },
      { useFindAndModify: false },
    );
  }
  async trackItemQuantities(data: {
    items: {
      itemId: string;
      quantity: number;
      variantIndex?: number;
      isAlwaysAvailable?: boolean;
    }[];
    isCancelledOrder?: boolean;
  }) {
    const { items, isCancelledOrder } = data;
    const session = await this.connection.startSession();
    session.startTransaction();

    for (let i of items) {
      let item;

      if (i.isAlwaysAvailable) {
        await this.itemModel.findByIdAndUpdate(
          i.itemId,
          { $inc: { total_orders: isCancelledOrder ? -i.quantity : i.quantity } },
          { session },
        );
        continue;
      }

      if (isDefined(i?.variantIndex)) {
        item = await this.itemModel.findOneAndUpdate(
          {
            available: true,
            _id: i.itemId,
            quantity: { $gte: isCancelledOrder ? -1 : i.quantity }, //get item where quantity is greater or equal to requested item quantity - don't do this is we're reverting an order
            [`variants.options.${i.variantIndex}.quantity`]: { $gte: isCancelledOrder ? -1 : i.quantity },
          },
          {
            $inc: {
              quantity: isCancelledOrder ? i.quantity : -i.quantity,
              total_orders: isCancelledOrder ? -i.quantity : i.quantity,
              [`variants.options.${i.variantIndex}.quantity`]:
                i.variantIndex >= 0 ? (isCancelledOrder ? i.quantity : -i.quantity) : 0,
            },
          },
          { new: false, session },
        );
      } else {
        item = await this.itemModel.findOneAndUpdate(
          {
            _id: i.itemId,
            available: true,
            quantity: { $gte: isCancelledOrder ? -1 : i.quantity },
          },
          {
            $inc: {
              quantity: isCancelledOrder ? i.quantity : -i.quantity,
              total_orders: isCancelledOrder ? -i.quantity : i.quantity,
            },
          },
          { new: false, session },
        );
      }

      if (!item || (item?.quantity === 0 && !isCancelledOrder)) {
        await session.abortTransaction();
        return {
          error: true,
          items,
        };
        // console.log('Throwing error!!!!');
        // throw new BadRequestException('This Item is not available');
      }
    }

    await session.commitTransaction();

    return { error: false, items };
  }

  async sendDeletedProductImagesMailBlast() {
    const storeSegments = await this.brokerTransport
      .send<{ slug: string; name: string; user: { name: string; email: string } }[]>(
        BROKER_PATTERNS.STORE.AGGREGATE_STORE,
        [
          {
            $lookup: {
              from: 'users',
              localField: 'owner',
              foreignField: '_id',
              as: 'user',
            },
          },
          { $unwind: '$user' },
          { $project: { _id: 0, name: 1, slug: 1, user: { name: 1, email: 1 } } },
        ],
      )
      .toPromise();

    for (let i = 0; i < storeSegments.length; i++) {
      const s = storeSegments[i];

      // this.deletedMailQueue.add(
      //   JOBS.SEND_DELETED_IMAGES_MAIL,
      //   {
      //     store: s.name,
      //     name: s.user.name,
      //     store_slug: s.slug,
      //     email: s.user.email,
      //   },
      //   {
      //     delay: (1000 / 4) * i,
      //   },
      // );
    }

    return storeSegments;
  }

  async importItems(user: any, data: ImportItemsDto) {
    let newCategories: IStoreCategory[] = [];
    let existingCategoriesMap = {};

    const sourceStore = await this.getStore({ _id: data.store_id });
    const targetStore = await this.getStore({ _id: user.store.id });

    if (sourceStore.owner !== targetStore.owner)
      throw new BadRequestException(`Cannot import items from another store `);

    const dbItems = await this.itemModel.find({
      _id: { $in: [...data.items] },
      store: data.store_id as any,
    });

    const targetCategoryMap = new Map(targetStore?.categories.map((cat) => [cat.name.toLowerCase(), cat]));

    const resolvedItems = dbItems.map((item) => {
      let categoryId = null;
      const category = sourceStore.categories.find((c) => getDocId(c).toString() === item?.category?.toString());

      if (category && !newCategories.includes(category)) {
        if (targetCategoryMap.has(category.name.toLowerCase())) {
          categoryId = getDocId(targetCategoryMap.get(category.name.toLowerCase()));
        } else {
          newCategories.push(category);
          categoryId = getDocId(category); //to be replaced after new categories have been created
        }
      }

      return {
        images: item.images,
        price: item.price,
        slug: item.slug,
        thumbnail: item.thumbnail,
        name: item.name,
        category: categoryId,
        price_unit: item.price_unit,
        description: item.description,
        variants: item.variants,
        upload_source: item.upload_source,
        quantity: item.quantity,
        is_always_available: item.is_always_available,
        meta: item?.meta,
      };
    });

    if (newCategories?.length > 0) {
      const storeData = await this.brokerTransport
        .send<Store>(BROKER_PATTERNS.STORE.ADD_CATEGORIES, {
          store: user.store.id,
          categories: newCategories,
        })
        .toPromise();
      const createdCategories = storeData.categories;

      const oldNewCategoryMap = {};
      newCategories.forEach((c, i) => {
        oldNewCategoryMap[c.id] = getDocId(
          createdCategories.find((cc) => cc.name.toLowerCase() === c.name.toLowerCase()),
        );
      });

      resolvedItems.map((item) => {
        const newCategoryId = oldNewCategoryMap[item.category as string] ?? item.category;
        item.category = newCategoryId;

        return item;
      });
    }

    // create items from resolvedItems
    await this.create(user, {
      store: user.store.id,
      items: resolvedItems as any,
    });
  }

  async getDeletedProductImagesMailQueue() {
    // const waiting = await this.deletedMailQueue.getWaiting();
    // const completed = await this.deletedMailQueue.getCompleted();
    // const delayed = await this.deletedMailQueue.getDelayed();
    // const parse = (jobs) => jobs.map((j) => j.toJSON());
    // const subData = {
    //   waiting: { length: waiting.length, jobs: parse(waiting) },
    //   processed: { length: completed.length, jobs: parse(completed) },
    //   delayed: { length: delayed.length, jobs: parse(delayed) },
    // };
    // return subData;
  }

  async getAllItems(storeId: string, type?: string) {
    const isCustomItems = type === 'custom';
    const isStoreFrontItems = type === 'storefront';
    const isAll = type === undefined;

    // const getStorefrontItems = async () => {
    //   return isStoreFrontItems
    //     ? await this.itemModel.find({ store: storeId as any, is_deleted: { $ne: true } }).select('')
    //     : (
    //         await this.itemModel
    //           .find({ store: storeId as any, is_deleted: { $ne: true } })
    //           .select('_id name price images thumbnail')
    //       ).map(({ _id, name, price, images, thumbnail }) => ({
    //         id: _id,
    //         name,
    //         price,
    //         image: images[thumbnail],
    //       }));
    // };

    const getStorefrontItems = async () => {
      return (
        await this.itemModel
          .find({ store: storeId as any, is_deleted: { $ne: true } })
          .select('_id name price images thumbnail variants weight is_always_available')
          .lean()
      ).map(({ _id, name, price, images, thumbnail, variants, weight, is_always_available }) => ({
        id: _id,
        name,
        price,
        image: images[thumbnail],
        variants,
        is_always_available,
        weight: weight ?? null,
      }));
    };

    const getCustomItems = async () => {
      return (await this.customItemModel.find({ store: storeId as any })).map(({ _id, name, price, weight }) => ({
        id: _id,
        name,
        price,
        image: '',
        weight,
      }));
    };

    if (isAll) return [...(await getStorefrontItems()), ...(await getCustomItems())];
    if (isStoreFrontItems) return [...(await getStorefrontItems())];
    if (isCustomItems) return [...(await getCustomItems())];
  }

  private generateItemSlug(name: string) {
    return `${sluggify(name)}-${Date.now()}-${genChars(3)}`;
  }

  async updateItemWeights(data: { id: string; weight: number; isCustomItem: boolean }[]) {
    for (let i = 0; i < data.length; i++) {
      const { id, weight, isCustomItem } = data[i];

      if (isCustomItem) {
        await this.customItemModel.updateOne({ _id: id }, { weight });
        return;
      }
      await this.itemModel.updateOne({ _id: id }, { weight });
    }
  }

  async updateItems(data: { filter: FilterQuery<Item>; payload: UpdateItemDto }[]) {
    for (const { payload, filter } of data) {
      const item = await this.itemModel.findOneAndUpdate(
        filter,
        {
          ...payload,
          ...(payload.quantity > -1 && { quantity: payload.quantity }), //cheking this because itemReq.quantity could be undefined or null. Same for is_always_available
          ...((payload.is_always_available === true || payload.is_always_available === false) && {
            is_always_available: payload.is_always_available,
          }),
        },
        {
          new: true,
        },
      );

      const itemJson = item.toJSON();
      itemJson._id = item._id || item.id;
      return item;
    }
  }

  async updateStoreItems({ store, payload }: { store: string; payload: UpdateItemDto }) {
    const items = await this.itemModel.updateMany({ store: store as any }, payload);
    return items;
  }

  async createItems(data: CreateItemDto) {
    const lastSortedItem = await this.itemModel
      .find({ store: data.store as any })
      .sort({ sort_index: -1 })
      .limit(1);
    let lastSortIndex = lastSortedItem[0]?.sort_index;
    lastSortIndex = lastSortIndex ? lastSortIndex : 0;

    const items = await Promise.all(
      data.items.map((item, index) => {
        if (!isValidObjectId(item.category)) {
          delete item.category;
        }
        if (item.variants && Array.isArray(item.variants.options)) {
          item.variants.options = item.variants.options.map((option) => {
            option._id = mongoose.Types.ObjectId();
            return option;
          });
        }
        return new this.itemModel({
          ...item,
          slug: this.generateItemSlug(item.name),
          store: data.store,
          sort_index: lastSortIndex + index + 1,
        }).save();
      }),
    );

    await this.brokerTransport
      .send<any>(BROKER_PATTERNS.STORE.UPDATE_ITEMS_COUNT, { store: data.store, inc: items.length })
      .toPromise();
    return items;
  }

  async migrateMenuItemsToItems() {
    const items = await this.itemModel.aggregate([
      [
        {
          $match: {
            is_menu_item: true,
          },
        },
        {
          $lookup: {
            from: 'stores',
            localField: 'store',
            foreignField: '_id',
            as: 'store',
          },
        },
        {
          $unwind: '$store',
        },
        {
          $match: { $or: [{ 'store.flags.uses_chowbot': false }, { 'store.flags': null }] },
        },

        {
          $group: {
            _id: null,
            stores: { $addToSet: '$store._id' },
          },
        },
      ],
    ]);

    if (items[0]?.stores?.length > 0) {
      for (let store of items[0]?.stores) {
        await this.itemModel.updateMany({ store: store }, { is_menu_item: false });
      }
    }

    /* 
         db.stores.updateMany(
          {
            'configuration.send_menu_on_initiation': true,
            $or: [{ 'flags': null }, { 'flags.uses_chowbot': false }],
          },
          {
           $set:{'configuration.send_menu_on_initiation': false,} 
          },
        ); */
  }

  async extractImageUrls(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const imageUrlSet = new Set<string>();

        console.log('Current working directory:', process.cwd());

        // Function to process an item and extract image URLs
        const processItem = (item) => {
          // Process item.images
          if (item.images && Array.isArray(item.images)) {
            for (const imageUrl of item.images) {
              if (typeof imageUrl === 'string' && imageUrl.includes('catlog-1.s3.eu-west-2.amazonaws.com')) {
                imageUrlSet.add(imageUrl);
              }
            }
          }

          // Process item.featuredImage
          if (item.featuredImage && typeof item.featuredImage === 'string') {
            if (item.featuredImage.includes('catlog-1.s3.eu-west-2.amazonaws.com')) {
              imageUrlSet.add(item.featuredImage);
            }
          }

          // Process item.variants.options[].image
          if (item.variants && item.variants.options && Array.isArray(item.variants.options)) {
            for (const option of item.variants.options) {
              if (option.image && typeof option.image === 'string') {
                if (option.image.includes('catlog-1.s3.eu-west-2.amazonaws.com')) {
                  imageUrlSet.add(option.image);
                }
              }
            }
          }
        };

        // Process items JSON file
        const processItemsStream = () => {
          return new Promise<void>((resolveItems, rejectItems) => {
            const stream = fs
              .createReadStream('staging.items.json', { encoding: 'utf8' })
              .pipe(JSONStream.parse('*')) // Parses each item in the array
              .pipe(
                es.through(function (item) {
                  processItem(item);
                  // Continue to the next item
                  this.emit('data', item);
                }),
              );

            stream.on('end', () => {
              console.log('Finished processing items.');
              resolveItems();
            });

            stream.on('error', (error) => {
              console.error('Error processing items:', error);
              rejectItems(error);
            });
          });
        };

        // Process orders JSON file
        const processOrdersStream = () => {
          return new Promise<void>((resolveOrders, rejectOrders) => {
            const stream = fs
              .createReadStream('staging.orders.json', { encoding: 'utf8' })
              .pipe(JSONStream.parse('*')) // Parses each order in the array
              .pipe(
                es.through(function (order) {
                  // Process each order
                  if (order.items && Array.isArray(order.items)) {
                    for (const orderItem of order.items) {
                      // Process orderItem.snapshot
                      if (orderItem.snapshot) {
                        processItem(orderItem.snapshot);
                      }
                      // Optionally, process orderItem.item if needed
                      // if (orderItem.item) {
                      //   processItem(orderItem.item);
                      // }
                    }
                  }
                  // Continue to the next order
                  this.emit('data', order);
                }),
              );

            stream.on('end', () => {
              console.log('Finished processing orders.');
              resolveOrders();
            });

            stream.on('error', (error) => {
              console.error('Error processing orders:', error);
              rejectOrders(error);
            });
          });
        };

        // Process both items and orders
        Promise.all([processItemsStream(), processOrdersStream()])
          .then(() => {
            // Convert the Set to an Array
            const imageUrlArray = Array.from(imageUrlSet);

            // Write the array to a new JSON file
            fs.writeFileSync('staging.items.image-urls.json', JSON.stringify(imageUrlArray, null, 2));

            console.log('Image URLs extracted successfully.');
            resolve();
          })
          .catch((error) => {
            console.error('Error extracting image URLs:', error);
            reject(error);
          });
      } catch (error) {
        console.error('Error extracting image URLs:', error);
        reject(error);
      }
    });
  }

  async updateItemImageUrls(): Promise<void> {
    const oldBaseUrl = 'https://catlog-1.s3.eu-west-2.amazonaws.com';
    const newBaseUrl = 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS';

    try {
      const batchSize = 2000; // Adjust the batch size as needed
      let processedCount = 0;
      let totalUpdated = 0;

      // Filter to find only items that contain the oldBaseUrl
      const filter = {
        $or: [
          { images: { $elemMatch: { $regex: oldBaseUrl } } },
          // { featuredImage: { $regex: oldBaseUrl } },
          { 'variants.options.image': { $regex: oldBaseUrl } },
        ],
      };

      let hasMoreDocuments = true;

      const totalDocuments = await this.itemModel.countDocuments(filter);
      this.logger.log(`Found ${totalDocuments} items to update.`);

      while (hasMoreDocuments) {
        const items = await this.itemModel.find(filter).limit(batchSize).exec();

        if (items.length === 0) {
          hasMoreDocuments = false;
          break;
        }
        console.log('Processing items:', items.length);

        const bulkOps = items
          .map((item) => {
            let updated = false;

            // Update item.images
            if (Array.isArray(item.images)) {
              const newImages = item.images.map((imageUrl) => {
                if (typeof imageUrl === 'string' && imageUrl.includes(oldBaseUrl)) {
                  updated = true;
                  return imageUrl.replace(oldBaseUrl, newBaseUrl);
                }
                return imageUrl;
              });
              item.images = newImages;
            }

            // Update item.featuredImage
            // if (typeof item.featuredImage === 'string' && item.featuredImage.includes(oldBaseUrl)) {
            //   item.featuredImage = item.featuredImage.replace(oldBaseUrl, newBaseUrl);
            //   updated = true;
            // }

            // Update item.variants.options[].image
            if (item.variants && Array.isArray(item.variants.options)) {
              const newOptions = item.variants.options.map((option) => {
                if (typeof option.image === 'string' && option.image.includes(oldBaseUrl)) {
                  updated = true;
                  option.image = option.image.replace(oldBaseUrl, newBaseUrl);
                }
                return option;
              });
              item.variants.options = newOptions;
            }

            // Prepare bulk operation if item was updated
            if (updated) {
              const updateFields: any = {
                images: item.images,
                // featuredImage: item.featuredImage,
              };

              // Ensure item.variants and item.variants.options exist before accessing
              if (item.variants && Array.isArray(item.variants.options)) {
                updateFields['variants.options'] = item.variants.options;
              }

              return {
                updateOne: {
                  filter: { _id: item._id },
                  update: {
                    $set: updateFields,
                  },
                },
              };
            }
            return null;
          })
          .filter((op) => op !== null);

        if (bulkOps.length > 0) {
          const result = await this.itemModel.bulkWrite(bulkOps);
          totalUpdated += result.modifiedCount || 0;
          this.logger.log(`Batch updated ${result.modifiedCount || 0} items.`);
        } else {
          // No bulk operations were performed, meaning no documents matched the update criteria.
          hasMoreDocuments = false;
        }

        processedCount += items.length;

        this.logger.log(`Processed ${processedCount}/${totalDocuments} items.`);
      }

      this.logger.log(`Item image URLs updated successfully. Total updated: ${totalUpdated}.`);
    } catch (error) {
      this.logger.error('Error updating item image URLs:', error);
      throw error;
    }
  }

  //makes items always available
  async addDefaultQuantities(storeId) {
    const items = await this.itemModel.updateMany(
      {
        store: storeId,
        quantity: { $exists: false },
        $or: [{ is_always_available: false }, { is_always_available: { $ne: true } }],
      },
      { is_always_available: true },
    );

    return items;
  }

  async getFirstItemForCategory(storeId: string, categoryId: string) {
    const query: any = {
      store: storeId,
      category: categoryId,
      is_deleted: false,
    };

    return await this.itemModel.findOne(query).sort({ sort_index: -1, _id: -1 }).lean().exec();
  }

  async getFeaturedOrTopItems(storeIdOrSlug: string): Promise<{ type: string; items: ItemDocument[] }> {
    // Get the store by ID or slug
    this.logger.log(`Getting featured or top items for store: ${storeIdOrSlug}`);

    const store = await this.getStore({
      $or: isValidObjectId(storeIdOrSlug) ? [{ _id: storeIdOrSlug }] : [{ slug: storeIdOrSlug }],
    });

    if (!store) {
      throw new NotFoundException('Store not found');
    }

    this.logger.log(`Found store: ${store.name} (ID: ${getDocId(store)})`);

    // Check if there are at least 4 featured items
    const featuredItems = await this.itemModel.find({
      store: getDocId(store) as any,
      is_featured: true,
      is_deleted: { $ne: true },
      available: true,
    });

    this.logger.log(`Found ${featuredItems.length} featured items for store: ${store.name}`);

    // If there are at least 4 featured items, return them
    if (featuredItems.length >= 4) {
      this.logger.log(`Returning ${featuredItems.length} featured items for store: ${store.name}`);
      const discounts = await this.discountModel.find({
        store: getDocId(store) as any,
      });

      return {
        type: 'featured',
        items: await this.resolveCouponsAndCategories(featuredItems, discounts, store),
      };
    }

    // Otherwise, return top items by total_orders
    this.logger.log(`Not enough featured items, fetching top-selling items for store: ${store.name}`);
    const topItems = await this.itemModel
      .find({
        store: getDocId(store) as any,
        is_deleted: { $ne: true },
        available: true,
      })
      .sort({ total_orders: -1 })
      .limit(10);

    this.logger.log(`Found ${topItems.length} top-selling items for store: ${store.name}`);

    const discounts = await this.discountModel.find({
      store: getDocId(store) as any,
    });

    return {
      type: 'top',
      items: await this.resolveCouponsAndCategories(topItems, discounts, store),
    };
  }

  async findById(id: string): Promise<ItemDocument> {
    return this.itemModel.findById(id).exec();
  }

  /**
   * Queues embedding generation for a single item
   * @param itemId ID of the item to generate embedding for
   */
  async queueEmbeddingGeneration(itemId: string): Promise<{ success: boolean }> {
    try {
      await this.itemQueue.add(QUEUES.ITEM, {
        type: JOBS.GENERATE_EMBEDDING,
        data: {
          itemId: itemId,
        },
      });

      return { success: true };
    } catch (error) {
      this.logger.error(`Error queuing embedding generation for item ${itemId}: ${error.message}`);
      return { success: false };
    }
  }

  async generateEmbeddingsForExistingItems(
    storeId?: string,
    batchSize: number = 100,
    limit?: number,
  ): Promise<{ processed: number; failed: number }> {
    let processed = 0;
    let skip = 0;
    let hasMore = true;

    this.logger.log(
      `Starting embedding generation for ${
        storeId ? 'store ' + storeId : 'all items'
      }... Batch size: ${batchSize}, Limit: ${limit || 'none'}`,
    );

    const query: FilterQuery<ItemDocument> = {
      embedding: { $exists: false }, // Only process items without embeddings
      is_deleted: { $ne: true },
    };
    if (storeId) {
      query.store = storeId as any;
    }

    // Count total eligible items if limit is specified
    let totalToProcess = Infinity;
    if (limit) {
      const countEligible = await this.itemModel.countDocuments(query);
      totalToProcess = Math.min(countEligible, limit);
      this.logger.log(`Found ${countEligible} items without embeddings, will process up to ${totalToProcess}`);
    }

    while (hasMore && processed < totalToProcess) {
      // Adjust batch size for the last batch if limit is specified
      const adjustedBatchSize = limit ? Math.min(batchSize, totalToProcess - processed) : batchSize;

      if (adjustedBatchSize <= 0) {
        this.logger.log(`Reached processing limit of ${limit} items`);
        break;
      }

      const items = await this.itemModel
        .find(query)
        .select('_id') // Only need IDs for queueing
        .limit(adjustedBatchSize)
        .skip(skip)
        .lean(); // Use lean for performance

      if (items.length === 0) {
        hasMore = false;
        break;
      }

      this.logger.log(`Queueing batch of ${items.length} items for embedding generation (skipped: ${skip})...`);

      // Queue embedding generation for each item
      for (const item of items) {
        try {
          await this.queueEmbeddingGeneration(getDocId(item));
          processed++;
        } catch (error) {
          this.logger.error(`Error queueing embedding generation for item ${getDocId(item)}: ${error.message}`);
        }
      }

      skip += items.length; // Move to the next batch

      if (hasMore) {
        await new Promise((resolve) => setTimeout(resolve, 100)); // 100ms delay
      }
    }

    this.logger.log(
      `Embedding generation queued for ${processed} items from ${storeId ? 'store ' + storeId : 'all stores'}`,
    );
    return { processed, failed: 0 }; // We don't track failures directly since we're just queueing
  }

  async removeItemFromInfoBlocks(itemId: string, infoBlockIds: string[]): Promise<void> {
    await this.infoBlockModel.updateMany({ _id: { $in: infoBlockIds } }, { $pull: { items: itemId } });
  }

  async addItemToInfoBlocks(itemId: string, infoBlockIds: string[]): Promise<void> {
    await this.infoBlockModel.updateMany({ _id: { $in: infoBlockIds } }, { $addToSet: { items: itemId } });
  }
}
