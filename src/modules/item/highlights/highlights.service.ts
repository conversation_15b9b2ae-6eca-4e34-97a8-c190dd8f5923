import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, PaginateModel } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { IRequest } from '../../../interfaces/request.interface';
import { Highlight, HighlightDocument } from './highlights.schema';
import { CreateHighlightDto, UpdateHighlightDto } from './dtos/highlight.dto';
import { Item, ItemDocument } from '../item.schema';
import { genChars, sluggify } from '../../../utils';
import { ItemService } from '../item.service';
import { getDocId } from '../../../utils/functions';
@Injectable()
export class HighlightsService {
  constructor(
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(Highlight.name)
    private readonly highlightModel: PaginateModel<HighlightDocument>,
    @InjectModel(Item.name)
    private readonly itemModel: PaginateModel<ItemDocument>,
    private readonly logger: Logger,
    private readonly itemService: ItemService,
  ) {
    this.logger.setContext('highlights.service.ts');
  }

  async createHighlight(data: CreateHighlightDto, store: any) {
    // Validate that all product IDs exist
    if (data.videos && data.videos.length > 0) {
      for (const video of data.videos) {
        if (video.products && video.products.length > 0) {
          const productCount = await this.itemModel.countDocuments({
            _id: { $in: video.products },
            store: store as any,
          });

          if (productCount !== video.products.length) {
            throw new BadRequestException('One or more product IDs are invalid');
          }
        }
      }
    }

    const slug = await this.generateSlug(data.title);

    const highlight = await this.highlightModel.create({
      active: true,
      slug,
      title: data.title,
      videos: data.videos as any,
      store: store,
    });

    highlight.populate({
      path: 'videos.products',
      select: 'name price images thumbnail videos thumbnail_type',
    });

    return highlight;
  }

  async getHighlights(
    storeId: string,
    filterQuery?: FilterQuery<HighlightDocument> & { search: string },
    paginationQuery?: PaginatedQueryDto,
    publicCall?: boolean,
  ) {
    let { search, ...filter } = { ...filterQuery };

    filter = {
      ...filter,
      store: storeId,
    };

    if (publicCall) {
      filter.active = true;
    }

    if (search) {
      filter.$or = [{ title: new RegExp(search, 'ig') }, { slug: new RegExp(search, 'ig') }];
    }

    if (!publicCall) {
      const result = await this.highlightModel.paginate(filter, {
        page: paginationQuery.page || 1,
        limit: paginationQuery.per_page || 50,
        lean: true,
        populate: {
          path: 'videos.products',
          select: 'name price images thumbnail videos thumbnail_type',
        },
        sort: { createdAt: paginationQuery?.sort?.toLowerCase() == 'asc' ? 1 : -1 },
      });

      return {
        items: result.docs,
        page: result.page,
        next_page: result.nextPage,
        prev_page: result.prevPage,
        total: result.totalDocs,
        total_pages: result.totalPages,
        per_page: result.limit,
      };
    } else {
      console.log('HIT HEREE AGAIN');
      const highlights = await this.highlightModel.find(filter).lean();
      const itemIds = highlights.flatMap((highlight) =>
        highlight.videos.flatMap((video) => video.products as string[]),
      );

      const items = await this.itemService.getItemsAndPopulateRelations(itemIds, storeId);

      for (let i = 0; i < highlights.length; i++) {
        highlights[i].videos = highlights[i].videos.map((video) => {
          return {
            ...video,
            products: (video.products as string[]).map((product) =>
              items.find((item) => getDocId(item).toString() === product.toString()),
            ),
          };
        });
      }

      return {
        items: highlights,
      };
    }
  }

  async getHighlightById(id: string, storeId: string) {
    const highlight = await this.highlightModel.findOne({ _id: id, store: storeId }).populate({
      path: 'videos.products',
      select: 'name price images thumbnail videos thumbnail_type',
    });

    if (!highlight) {
      throw new NotFoundException('Highlight not found');
    }

    return highlight;
  }

  async getHighlightBySlug(slug: string, storeId: string) {
    const highlight = await this.highlightModel.findOne({ slug, store: storeId, active: true }).populate({
      path: 'videos.products',
      select: 'name price images thumbnail videos',
    });

    if (!highlight) {
      throw new NotFoundException('Highlight not found');
    }

    return highlight;
  }

  async updateHighlight(id: string, updateData: UpdateHighlightDto & { slug?: string }, storeId: string) {
    const highlight = await this.highlightModel.findOne({ _id: id, store: storeId });

    if (!highlight) {
      throw new NotFoundException('Highlight not found');
    }

    // Validate product IDs if videos are being updated
    if (updateData.videos && updateData.videos.length > 0) {
      for (const video of updateData.videos) {
        if (video.products && video.products.length > 0) {
          const productCount = await this.itemModel.countDocuments({
            _id: { $in: video.products },
            store: storeId as any,
          });

          if (productCount !== video.products.length) {
            throw new BadRequestException('One or more product IDs are invalid');
          }
        }
      }
    }

    if (updateData.title && updateData.title !== highlight.title) {
      const slug = await this.generateSlug(updateData.title);
      updateData.slug = slug;
    }

    const updatedHighlight = await this.highlightModel
      .findOneAndUpdate({ _id: id, store: storeId }, updateData as any, { new: true })
      .populate({
        path: 'videos.products',
        select: 'name price images videos thumbnail thumbnail_type',
      });

    return updatedHighlight;
  }

  async deleteHighlight(id: string, storeId: string) {
    const highlight = await this.highlightModel.findOneAndDelete({ _id: id, store: storeId });

    if (!highlight) {
      throw new NotFoundException('Highlight not found');
    }

    return { success: true };
  }

  async generateSlug(title: string): Promise<string> {
    let slug = sluggify(title).toLowerCase();
    let slugExists;

    do {
      slugExists = await this.highlightModel.exists({ slug });

      if (slugExists) {
        slug = slug + genChars(2, false);
      }
    } while (slugExists);

    return slug;
  }

  async countHighlights(filter: any) {
    return this.highlightModel.countDocuments(filter);
  }
}
