import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBody,
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiExtraModels,
  ApiHeader,
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiSecurity,
  ApiTags,
  ApiOperation,
} from '@nestjs/swagger';
import { Item } from './item.schema';
import { ItemService } from './item.service';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { IRequest } from '../../interfaces/request.interface';
import {
  CreateItemDto,
  CreateItemResponse,
  GetItemsPaginatedDto,
  UpdateItemDto,
  UpdateItemQuantityDto,
  UpdateItemQuantitiesDto,
  BulkUpdateDto,
  UpdateSortIndexDto,
} from '../../models/dtos/ItemDtos';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { ImportItemsDto, ImportItemsResponseDto } from '../../models/dtos/StoreDtos';
import { checkIfUserOwnsStore, protectRoute } from '../../utils';
import { SCOPES } from '../../utils/permissions.util';
import { Store } from '../store/store.schema';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { PlanGuard } from '../../guards/plan.guard';
import { PlanPermissions, RolePermissions } from '../../decorators/permission.decorator';
import { RoleGuard } from '../../guards/role.guard';
import { ItemRecommendationsService } from './item.recommendations.service';
import { ItemInfoBlocksService } from './item-info-blocks.service';

@Controller('items')
@ApiTags('Item')
@ApiExtraModels(Item)
export class ItemController {
  constructor(
    private readonly itemService: ItemService,
    private readonly logger: Logger,
    private readonly itemInfoBlocksService: ItemInfoBlocksService,
  ) {
    this.logger.setContext('itemController');
  }

  @Post('')
  @UseGuards(JwtAuthGuard)
  @ApiCreatedResponse({
    type: CreateItemResponse,
  })
  @ApiBody({ type: CreateItemDto })
  @ApiSecurity('bearer')
  async create(@Req() req: IRequest, @Body() itemReq: CreateItemDto) {
    checkIfUserOwnsStore(req.user.store as Store, itemReq.store);
    const itemsHaveVariants = itemReq.items.some((i) => i.variants?.options?.length > 0);

    protectRoute(
      itemsHaveVariants,
      SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_OPTIONS,
      req.user,
      "You'll need switch to a paid plan to add product options",
    );

    const data = await this.itemService.create(req.user, itemReq);

    return {
      message: 'Item created successfully',
      data,
    };
  }

  async getItemsByStoreId() {
    try {
      return {};
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Get('seo')
  async getItemSeo() {
    try {
      return this.itemService.getItemSeo();
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Get('extractItemsImageUrl') // only for testing purpose
  async extractItemsImageUrl() {
    try {
      return this.itemService.extractImageUrls();
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Put('bulk-update')
  @UseGuards(JwtAuthGuard)
  @ApiBody({ type: BulkUpdateDto })
  @ApiSecurity('bearer')
  async bulkUpdate(@Req() req: IRequest, @Body() itemReq: BulkUpdateDto) {
    const itemsHaveVariants = itemReq?.items?.some((i) => i.variants?.options?.length > 0);

    // protectRoute(
    //   itemsHaveVariants,
    //   SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_OPTIONS,
    //   req.user,
    //   "You'll need switch to a paid plan to add product options",
    //   () => {
    //     throw new BadRequestException('Switch to a paid plan to update product options or delete the options');
    //   },
    // );

    const data = await this.itemService.bulkUpdate(req.user?.store?.id, itemReq);
    return {
      message: 'Updated items successfully',
      data,
    };
  }

  @Put('quantities')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse()
  @ApiSecurity('bearer')
  async updateItemQuantity(@Req() req: IRequest, @Body() itemReq: UpdateItemQuantitiesDto) {
    await this.itemService.updateItemQuantities(req.user?.store?.id, itemReq);

    return {
      message: 'Updated item quantity successfully',
    };
  }

  @Put('/sort-indexes')
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiOkResponse({
    type: CreateItemResponse,
  })
  @ApiSecurity('bearer')
  async updateSortIndexes(@Req() req: IRequest, @Body() items: UpdateSortIndexDto[]) {
    try {
      const data = await this.itemService.updateSortIndexes(req.user.store.id, items);
      return {
        message: 'Updated sort index of items',
        data,
      };
    } catch (err) {
      if (err.status) {
        throw new HttpException(err.response, err.status);
      }

      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Put(':id')
  @RolePermissions(SCOPES.PRODUCTS.EDIT_PRODUCTS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOkResponse({
    type: CreateItemResponse,
  })
  @ApiSecurity('bearer')
  async update(@Req() req: IRequest, @Param('id') id: string, @Body() itemReq: UpdateItemDto) {
    const itemsHaveVariants = itemReq?.variants?.options?.length > 0;

    protectRoute(
      itemsHaveVariants,
      SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_PRODUCT_OPTIONS,
      req.user,
      "You'll need switch to a paid plan to add product options",
      () => {
        throw new BadRequestException('Switch to a paid plan to update product options or delete the options');
      },
    );

    try {
      const data = await this.itemService.update(id, itemReq, req.user.store);
      return {
        message: 'Item updated successfully',
        data,
      };
    } catch (err) {
      if (err.status) {
        throw new HttpException(err.response, err.status);
      }

      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Put('/featured/:id')
  @PlanPermissions(SCOPES.PRODUCTS.FEATURE_PRODUCTS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiOkResponse({
    type: CreateItemResponse,
  })
  @ApiSecurity('bearer')
  async updateFeatured(@Req() req: IRequest, @Param('id') id: string, @Body() itemReq: UpdateItemDto) {
    try {
      const data = await this.itemService.updateFeaturedItem(id, itemReq, req.user.store);
      return {
        message: 'Featured item updated successfully',
        data,
      };
    } catch (err) {
      if (err.status) {
        throw new HttpException(err.response, err.status);
      }

      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(@Param('id') id: string, @Req() req: IRequest) {
    try {
      return await this.itemService.delete(id, req);
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  //GET ITEMS ON DASHBOARD //////////////////////////////////
  @Get('')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiResponse({
    status: HttpStatus.OK,
    type: GetItemsPaginatedDto,
  })
  @ApiQuery({
    name: 'filter',
    required: true,
    schema: {
      type: 'object',
      properties: {
        category: {
          type: 'string',
        },
        store: {
          type: 'string',
        },
        search: {
          type: 'string',
          description: 'Searches the name and description for the characters supplied',
        },
      },
    },
  })
  async getItems(@Query('filter') filter: any, @Query() query: PaginatedQueryDto, @Req() req: IRequest) {
    try {
      if (!filter) {
        throw new BadRequestException('filter object is missing in url query');
      }
      filter.store = req.user.store.id;
      const items = await this.itemService.getPaginatedStoreItems(filter, query);

      return {
        message: 'Items fetched successfully',
        ...items,
      };
    } catch (err) {
      this.logger.error(`error occurred fetching list of items`, err);
      if (!(err instanceof BadRequestException)) {
        throw new InternalServerErrorException();
      }
      throw err;
    }
  }

  @Get('/paginate-by-sort-index')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiResponse({
    status: HttpStatus.OK,
  })
  async getItemsBySortIndex(@Req() req: IRequest) {
    try {
      const items = await this.itemService.paginateItemsBySortIndex(req.user.store.id);

      return {
        message: 'Items fetched successfully',
        ...items,
      };
    } catch (err) {
      this.logger.error(err);
      this.logger.error(`error occurred fetching list of items`, err);
      if (!(err instanceof BadRequestException)) {
        throw new InternalServerErrorException();
      }
      throw err;
    }
  }

  @Get('all')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @ApiQuery({
    name: 'type',
    required: false,
    schema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
        },
      },
    },
  })
  async getAllItems(@Query('type') type: string, @Req() req: IRequest) {
    const items = await this.itemService.getAllItems(req.user.store.id, type);
    return {
      data: { items },
      message: 'All items retrieved successfully',
    };
  }
  @Get('variant-templates')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  async getVariantTemplates(@Req() req: IRequest) {
    const templates = await this.itemService.getVariantTemplates(req.user.store.id);
    return {
      data: { templates },
      message: 'All variants templates retrieved successfully',
    };
  }

  //GET ITEMS ON STORE FRONT
  @Get('public')
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
    type: GetItemsPaginatedDto,
  })
  @ApiQuery({
    name: 'filter',
    required: false,
    schema: {
      type: 'object',
      properties: {
        category: {
          type: 'string',
        },
        store: {
          type: 'string',
        },
        search: {
          type: 'string',
          description: 'Searches the name and description for the characters supplied',
        },
      },
    },
  })
  async getItemsPublic(
    @Query('filter') filter: any,
    @Query() query: { showUnavailableItems?: string; separateFeaturedItems?: string } & PaginatedQueryDto,
  ) {
    try {
      if (!filter) {
        throw new BadRequestException('filter object is missing in url query');
      }
      const items = await this.itemService.getPaginatedStoreItems(filter, query, true);

      return {
        message: 'Items fetched successfully',
        ...items,
      };
    } catch (err: any) {
      this.logger.error(`error occurred fetching list of items`, err);
      if (!(err instanceof BadRequestException)) {
        throw new InternalServerErrorException();
      }
      throw err;
    }
  }

  @Get('public/featured-or-top')
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
  })
  @ApiQuery({
    name: 'store',
    required: true,
    type: String,
    description: 'Store ID or slug',
  })
  async getFeaturedOrTopItems(@Query('store') storeIdOrSlug: string) {
    try {
      if (!storeIdOrSlug) {
        throw new BadRequestException('Store ID or slug is required');
      }

      const items = await this.itemService.getFeaturedOrTopItems(storeIdOrSlug);

      return {
        message: 'Items fetched successfully',
        data: items,
      };
    } catch (err) {
      this.logger.error(`Error fetching featured or top items`, err);
      if (!(err instanceof BadRequestException)) {
        throw new InternalServerErrorException();
      }
      throw err;
    }
  }

  @Get('public/:id')
  @ApiParam({ name: 'id', description: 'can be the id or slug of the item' })
  @ApiOkResponse({
    type: Item,
  })
  async getItemPublic(@Param('id') id: string) {
    try {
      const item = await this.itemService.getItemPublicBySlugOrId(id);
      return {
        message: 'Item fetched successfully',
        data: item,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException();
    }
  }

  @Get('deleted-products-mail')
  async getJobs() {
    return {
      data: await this.itemService.getDeletedProductImagesMailQueue(),
    };
  }

  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiExcludeEndpoint()
  @Post('/deleted-products-mail')
  async mail() {
    return await this.itemService.sendDeletedProductImagesMailBlast();
  }

  @Get(':id')
  @ApiParam({ name: 'id', description: 'can be the id or slug of the item' })
  @ApiOkResponse({
    type: Item,
  })
  async getItem(@Param('id') id: string) {
    try {
      const item = await this.itemService.getItemBySlugOrId(id);
      return {
        message: 'Item fetched successfully',
        data: item,
      };
    } catch (err) {
      this.logger.error('ITEM:', err);
      throw new BadRequestException();
    }
  }

  @Get(':id/related')
  @ApiOperation({ summary: 'Get related products for an item' })
  @ApiParam({ name: 'id', type: String })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  async getRelatedProducts(@Param('id') id: string, @Query('limit') limit?: number) {
    const sourceItem = await this.itemService.findById(id);
    if (!sourceItem) {
      throw new NotFoundException('Item not found');
    }

    const relatedItems = await this.itemService.findRelatedProducts(id, limit);

    return {
      message: 'Related products retrieved successfully',
      data: relatedItems,
    };
  }

  // Add a new endpoint for migration
  @Post('migrate-embeddings')
  @ApiExcludeEndpoint() // Hide from public Swagger docs
  @UseGuards(InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key', description: 'API key for internal operations' })
  async migrateEmbeddings(
    @Query('storeId') storeId?: string,
    @Query('batchSize') batchSize?: number,
    @Query('limit') limit?: number,
  ) {
    this.logger.log(
      `Received request to migrate embeddings for store: ${storeId || 'all'}, batchSize: ${batchSize || 100}`,
    );

    // Validate parameters
    const batchSizeNum = batchSize ? Math.min(Math.max(Number(batchSize), 10), 200) : 100; // Between 10 and 200
    const limitNum = limit ? Number(limit) : undefined; // Limit total items to process

    // Create a unique job ID for tracking
    const jobId = `embedding-migration-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;

    // Run as a background process
    process.nextTick(async () => {
      try {
        this.logger.log(`Starting embedding migration job ${jobId}`);
        const result = await this.itemService.generateEmbeddingsForExistingItems(storeId, batchSizeNum, limitNum);
        this.logger.log(`Embedding migration job ${jobId} completed: ${JSON.stringify(result)}`);
      } catch (err) {
        this.logger.error(`Embedding migration job ${jobId} failed: ${err.message}`, err.stack);
      }
    });

    return {
      message: `Embedding migration process started in the background (job: ${jobId}). Check logs for progress.`,
      jobId,
      parameters: {
        storeId: storeId || 'all',
        batchSize: batchSizeNum,
        limit: limitNum,
      },
    };
  }

  @Get(':id/info-blocks')
  @ApiOperation({ summary: 'Get all info blocks for a specific item' })
  @ApiResponse({ status: 200, description: 'Returns all info blocks associated with the item' })
  @ApiParam({ name: 'id', description: 'Item ID' })
  async getInfoBlocksForItem(@Param('id') id: string) {
    return this.itemInfoBlocksService.getInfoBlocksForItem(id);
  }

  @Post(':id/info-blocks/:infoBlockId')
  @ApiOperation({ summary: 'Assign an info block to an item' })
  @ApiResponse({ status: 200, description: 'Returns the updated item with the assigned info block' })
  @ApiParam({ name: 'id', description: 'Item ID' })
  @ApiParam({ name: 'infoBlockId', description: 'Info Block ID' })
  async assignInfoBlockToItem(@Param('id') id: string, @Param('infoBlockId') infoBlockId: string) {
    return this.itemInfoBlocksService.assignInfoBlockToItem(infoBlockId, id);
  }

  @Delete(':id/info-blocks/:infoBlockId')
  @ApiOperation({ summary: 'Remove an info block from an item' })
  @ApiResponse({ status: 200, description: 'Returns the updated item without the removed info block' })
  @ApiParam({ name: 'id', description: 'Item ID' })
  @ApiParam({ name: 'infoBlockId', description: 'Info Block ID' })
  async removeInfoBlockFromItem(@Param('id') id: string, @Param('infoBlockId') infoBlockId: string) {
    return this.itemInfoBlocksService.removeInfoBlockFromItem(infoBlockId, id);
  }
}
