import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { AdminConfigService } from './adminconfig.service';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { AddAdminConfigDto } from '../../models/dtos/admincfg.dto';
import { ApiExcludeEndpoint } from '@nestjs/swagger';

@Controller('/admin/')
export class AdminConfigController {
  constructor(private adminConfigService: AdminConfigService) {}

  @Get('/configs')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async getConfigs() {
    return this.adminConfigService.getConfigs();
  }

  @Post('/configs')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async addConfig(@Body() req: AddAdminConfigDto) {
    return this.adminConfigService.addConfg(req.name, req.value);
  }
}
