import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { AdminConfig, AdminConfigDocument } from "./adminconfig.schema";
import { FilterQuery, Model } from "mongoose";

@Injectable()
export class AdminConfigService {
  constructor(
    @InjectModel(AdminConfig.name)
    private adminConfigModel: Model<AdminConfigDocument>
  ) {}

  async getConfig(name: string): Promise<AdminConfig> {
    return this.adminConfigModel.findOne({key: name});
  }

  async getConfigs(): Promise<AdminConfig[]> {
    return this.adminConfigModel.find();
  }
 
  async setConfig(config: Partial<AdminConfig>) {
    return (await this.adminConfigModel.findOne({enabled: true})).updateOne(config);
  }

  async addConfg(name: string, value: string) {
    return this.adminConfigModel.create({
      key: name,
      value
    });
  }

  async deleteConfig(config: FilterQuery<AdminConfig>) {
    return this.adminConfigModel.deleteOne(config);
  }
}