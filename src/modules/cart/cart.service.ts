import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateCartDto, UpdateCartDto } from './dto/cart.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Cart, CartDocument } from './cart.schema';
import { isValidObjectId, Model, MongooseFilterQuery, PaginateModel } from 'mongoose';
import {
  genChars,
  getDiscountPrice,
  mapPaginateQuery,
  resolveCategories,
  serializeAllObjectIdsToString,
  stripUnderScoreId,
} from '../../utils';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Country } from '../country/country.schema';
import { DiscountDocument } from '../item/discount-coupons/discounts-coupons.schema';
import { STORE_PUBLIC_HIDDEN_INFO } from '../../utils/constants';
import axios from 'axios';
import { IpAddress, VisitorLocation } from '../ip-address/ip-address.schema';
import { PaginatedQueryDto } from '../wallets/dtos/search.dto';

@Injectable()
export class CartService {
  constructor(
    @InjectModel(Cart.name) private readonly cartModel: PaginateModel<CartDocument>,
    private readonly brokerTransport: BrokerTransportService,
  ) {}

  async create(cartReq: CreateCartDto) {
    cartReq.items = cartReq.items.map((item: any) => {
      item.object = item.item_id;
      return item;
    });

    delete cartReq.id;

    let resolvedIp: IpAddress = null;

    if (cartReq.ip) {
      resolvedIp = await this.brokerTransport
        .send<IpAddress>(BROKER_PATTERNS.IP_ADDRESS.RESOLVE_IP, cartReq.ip)
        .toPromise();
    }

    let cart = await new this.cartModel({
      ...cartReq,
      location: resolvedIp?.location,
      ip_address: cartReq?.ip,
      hash: this.generateHash(),
    }).save();

    await cart.populate('items.object').populate('store').execPopulate();
    cart = cart.toJSON();
    let serializedCart = serializeAllObjectIdsToString(cart);

    cart = serializedCart;

    cart.items = await this.resolveItems(cart.items, true);
    return cart;
  }

  async update(id: string, cartReq: UpdateCartDto) {
    if (cartReq.items) {
      cartReq.items = cartReq.items.map((item: any) => {
        item.object = item.item_id;
        return item;
      });
    }

    let resolvedIp: IpAddress = null;

    if (cartReq.ip) {
      resolvedIp = await this.brokerTransport
        .send<IpAddress>(BROKER_PATTERNS.IP_ADDRESS.RESOLVE_IP, cartReq.ip)
        .toPromise();
    }

    let cart = await this.cartModel
      .findByIdAndUpdate(id, { ...cartReq, location: resolvedIp?.location, ip_address: cartReq?.ip } as any, {
        new: true,
      })
      .populate('items.object')
      .populate('store', STORE_PUBLIC_HIDDEN_INFO)
      .lean<Cart>(true);

    if (!cart) {
      throw new NotFoundException('Cart with id does not exist');
    }

    [cart] = serializeAllObjectIdsToString([cart]);
    cart.items = await this.resolveItems(cart.items, true);
    return cart;
  }

  async findAll(storeId: string) {
    const carts = await this.cartModel
      .find({ store: storeId as any })
      .populate('items.object')
      .populate('store', STORE_PUBLIC_HIDDEN_INFO)
      .lean<Cart>({ virtuals: true });

    return Promise.all(
      carts.map(async (cart) => {
        cart = serializeAllObjectIdsToString(cart);
        cart.items = await this.resolveItems(cart.items, true);
        return cart;
      }),
    );
  }

  async paginateCarts(storeId: string, query: PaginatedQueryDto) {
    const carts = await this.cartModel.paginate(
      { store: storeId as any, customer: { $exists: true } },
      {
        limit: query.per_page,
        page: query.page,
        sort: { updated_at: query.sort.toLocaleLowerCase() === 'desc' ? -1 : 1 },
        populate: ['customer', 'items.object'],
      },
    );

    const data = await Promise.all(
      carts.docs.map(async (cart) => {
        cart = cart.toJSON();
        cart = serializeAllObjectIdsToString(cart);
        cart.items = await this.resolveItems(cart.items, true);
        return cart;
      }),
    );

    return {
      data: data,
      page: carts.page,
      next_page: carts.nextPage,
      prev_page: carts.prevPage,
      total: carts.totalDocs,
      total_pages: carts.totalPages,
      per_page: carts.limit,
    };
  }

  async resolveItems(items: Cart['items'], computeDiscount: boolean = false) {
    let discounts =
      computeDiscount &&
      (await this.brokerTransport
        .send<DiscountDocument[]>(BROKER_PATTERNS.ITEM.GET_DISCOUNTS, {
          store: items[0]?.object?.store,
        })
        .toPromise());

    const discountMap = new Map<string, DiscountDocument>();
    if (discounts && discounts.length > 0) {
      for (let discount of discounts) {
        discountMap.set(discount?.id?.toString(), discount);
      }
    }

    return items.map((item) => {
      if (computeDiscount) {
        const itemDiscount = discountMap.get(item.object?.discount?.toString());
        if (itemDiscount) {
          const discountPrice = getDiscountPrice(item.object.price, itemDiscount);
          discountPrice && (item.object.discount_price = discountPrice);
          if (item.object.variants) {
            for (let option of item.object.variants.options) {
              const optionDiscountPrice = getDiscountPrice(option.price, itemDiscount);
              optionDiscountPrice && (option.discount_price = optionDiscountPrice);
            }
          }

          if (item.variant) {
            const variantDiscountPrice = getDiscountPrice(item.variant.price, itemDiscount);
            variantDiscountPrice && (item.variant.discount_price = variantDiscountPrice);
          }
        } else if (item?.object.discount_price && item?.object?.discount_price > 0) {
          //add the same discount difference when a discount is added from form directly
          const discountAmount = item?.object.price - item?.object.discount_price;

          if (item.object.variants) {
            for (let option of item.object.variants.options) {
              option.discount_price = option.price - discountAmount;
            }
          }

          if (item.variant) {
            item.variant.discount_price = item.variant.price - discountAmount;
          }
        }
      }

      [item] = stripUnderScoreId([item as any]);

      if (!item.object) {
        item.is_deleted = true;
        return item;
      }

      item.object = stripUnderScoreId(item.object);
      item.variant = (item as any).object.variants?.options?.find(
        (v) => String(v._id || (v as any)?.id) === String(item.variant_id),
      );

      if ((!item.variant && item.object.variants?.options.length > 0) || item.object.is_deleted) {
        item.is_deleted = true;
      }
      return item;
    });
  }

  generateHash() {
    return genChars(10);
  }

  // Finds a cart using either id or hash
  async findOne(id: string) {
    let cart = await this.cartModel
      .findOne(isValidObjectId(id) ? { _id: id } : { hash: id })
      .populate('items.object')
      .populate('store', STORE_PUBLIC_HIDDEN_INFO)
      .lean<Cart>({ virtuals: true });
    if (!cart) {
      throw new NotFoundException('Cart with id does not exist');
    }
    cart = serializeAllObjectIdsToString(cart);
    cart.items = await this.resolveItems(cart.items, true);

    // Resolve items category
    cart.items = cart.items.map(({ object, ...item }) => {
      object.category = cart.store.categories?.find((category) => {
        return object.category && category._id === object.category.toString();
      });

      [object.category] = stripUnderScoreId([object.category]);

      return { ...item, object, snapshot: object };
    });

    cart.store.country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: cart.store.country,
      })
      .toPromise();

    return cart;
  }

  remove(id: string) {
    return this.cartModel.findByIdAndDelete(id);
  }

  getTotal(data: MongooseFilterQuery<CartDocument>) {
    return this.cartModel.countDocuments(data);
  }
}
