import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { Store } from '../store/store.schema';

export type AffiliateDocument = Affiliate & Document;

export enum AFFILIATE_TYPES {
  INFLUENCER = 'INFLUENCER',
  FRIEND = 'FRIEND',
  AFFILIATE = 'AFFILIATE',
}

@Schema({ timestamps: true })
export class Affiliate {
  _id: any;

  @ApiProperty()
  @Prop({ type: String, required: true })
  name: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  email: string;

  @ApiProperty()
  @Prop({ type: String, required: true })
  phone: string;

  @ApiProperty()
  @Prop({ type: String, required: true, unique: true })
  slug: string;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  total_orders: number;

  @ApiProperty()
  @Prop({ type: Number, default: 0 })
  total_customers: number;

  @ApiProperty()
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: true })
  store: string | Store;

  @ApiProperty()
  @Prop({
    type: String,
    enum: Object.values(AFFILIATE_TYPES),
    default: AFFILIATE_TYPES.AFFILIATE,
  })
  type: AFFILIATE_TYPES = AFFILIATE_TYPES.AFFILIATE;

  @ApiProperty()
  @Prop({ type: Number, default: 0, required: false })
  commission_rate?: number;

  @ApiProperty()
  @Prop({ type: Number, default: 0, required: false })
  total_commission?: number;
}

export const AffiliateSchema = SchemaFactory.createForClass(Affiliate);
