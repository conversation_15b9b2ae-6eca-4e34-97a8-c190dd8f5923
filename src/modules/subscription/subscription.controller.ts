import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Query,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import {
  ApiBadRequestResponse,
  ApiExcludeEndpoint,
  ApiHeader,
  ApiOkResponse,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { IRequest } from '../../interfaces/request.interface';
import { CreateFreeSubscriptionDto } from './dtos/create';
import { PlanPermissions, RolePermissions } from '../../decorators/permission.decorator';
import { SCOPES } from '../../utils/permissions.util';
import { RoleGuard } from '../../guards/role.guard';
import { CancelSubscriptionToggleDto, ChangePlanDto } from '../../models/dtos/PlanDtos';
import { SubscriptionService } from './subscription.service';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { TokenHistoryQueryDTO } from './dtos/token-history.dto';
import { TokenPurchaseHistoryService } from './tokens-purchase-history/token-purchase-history.service';
import { PlanGuard } from '../../guards/plan.guard';
import { UpdateSubscriptionDto } from './dtos/update-subscription.dto';

@Controller('subscriptions')
@ApiTags('Subscriptions')
export class SubscriptionController {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly tokenPurchaseHistoryService: TokenPurchaseHistoryService,
  ) {}

  @Post('')
  @RolePermissions(SCOPES.SUBSCRIPTIONS.MANAGE_SUBSCRIPTIONS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @HttpCode(HttpStatus.OK)
  @ApiSecurity('bearer')
  @ApiOkResponse({
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
        },
      },
    },
  })
  async createFreeSubscription(@Req() req: IRequest, @Body() body: CreateFreeSubscriptionDto) {
    body.owner = req.user.id;
    body.store = req.user?.store.id;
    const data = await this.subscriptionService.createNewSubscription(body, req?.user);

    return {
      message: 'Subscription created successfully',
      data,
    };
  }

  @Post('/update-expired-subscriptions')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  @ApiOkResponse()
  async updateExpiredSubscriptions(@Req() req: IRequest) {
    const data = await this.subscriptionService.updateExpiredSubscriptions();

    return {
      message: 'Updated Subscriptions',
      data,
    };
  }

  @Post('/switch-to-free-plan')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SUBSCRIPTIONS.MANAGE_SUBSCRIPTIONS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOkResponse()
  async switchToFreePlan(@Req() req: IRequest, @Body() planReq: ChangePlanDto) {
    const data = await this.subscriptionService.switchToFreePlan(req.user?.store.id, planReq);
    return {
      message: 'Plan changed successfully',
      data,
    };
  }

  @Post('/cancel-subscription')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SUBSCRIPTIONS.MANAGE_SUBSCRIPTIONS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOkResponse()
  async cancelSubscription(@Req() req: IRequest, @Body() cancelReq: CancelSubscriptionToggleDto) {
    const data = await this.subscriptionService.cancelSubscription(req.user?.store.id, cancelReq);
    return {
      message: 'Subscription will be canceled at the end of the billing period',
      data,
    };
  }

  @Post('/update-user-subscription')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  @ApiOkResponse()
  async updateUserSubscription(@Body() updateSubscriptionReq: UpdateSubscriptionDto) {
    const data = await this.subscriptionService.updateUserSubscription(updateSubscriptionReq);

    return {
      message: 'Subscription updated successfully',
      data,
    };
  }

  @Post('/migrate-first-payment-date')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateFirstPaymentDate() {
    return await this.subscriptionService.migrateFirstPaymentDate();
  }

  @Post('/migrate-subscription')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateSubscription() {
    return await this.subscriptionService.migrateSubscriptions();
  }

  @Post('/migrate-to-bigin')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateSubsToBigin() {
    return await this.subscriptionService.migrateToBigin();
  }

  @Post('/update-bigin-users')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async updateBiginUsers(@Query() paginatedQuery: PaginatedQueryDto) {
    console.log('pagination', paginatedQuery);
    return await this.subscriptionService.updateExistingBiginUsers(paginatedQuery.page, paginatedQuery.per_page);
  }

  @Post('/migrate-expired')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateExpiredSubs(@Query() paginatedQuery: PaginatedQueryDto) {
    console.log('pagination', paginatedQuery);
    return await this.subscriptionService.migrateExpiredSubsUsersOnBigin(paginatedQuery.page, paginatedQuery.per_page);
  }

  @Post('/migrate-subscription-history')
  @ApiSecurity('bearer')
  // @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateSubscriptionHistory() {
    return await this.subscriptionService.migrateSubscriptionHistory();
  }

  @Get('/debtors')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async getDebtors(@Query('pagination') pagination: PaginatedQueryDto) {
    const data = await this.subscriptionService.paginateDebtors(pagination);
    return data;
  }

  @Get('token-history')
  @ApiSecurity('bearer')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_ACCESS_TOKENS)
  @RolePermissions(SCOPES.CHOWBOT.CAN_VIEW_TOKEN_BALANCE)
  @UseGuards(JwtAuthGuard, PlanGuard, RoleGuard)
  @ApiBadRequestResponse({ description: 'Invalid request parameters.' })
  async getTokenHistory(
    @Req() req: IRequest,
    @Query() query: TokenHistoryQueryDTO,
    @Query() paginatedQuery: PaginatedQueryDto,
  ) {
    const data = await this.tokenPurchaseHistoryService.getTokenUsageByDate(
      { ...query, ...paginatedQuery },
      req?.user?.store?.subscription?.id,
    );
    return {
      ...data,
      message: 'Successfully retrieved token history',
    };
  }

  @Get('token-balance')
  @ApiSecurity('bearer')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_ACCESS_TOKENS)
  @RolePermissions(SCOPES.CHOWBOT.CAN_VIEW_TOKEN_BALANCE)
  @UseGuards(JwtAuthGuard, PlanGuard, RoleGuard)
  @ApiBadRequestResponse({ description: 'Invalid request parameters.' })
  async getTokenBalance(@Req() req: IRequest) {
    const data = await this.tokenPurchaseHistoryService.getTokenBalance(req.user.store.id);
    return {
      data,
      message: 'Successfully retrieved token balance',
    };
  }
}
