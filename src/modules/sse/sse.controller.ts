import { Body, Controller, Get, Header, Post, Query, Res, Sse } from '@nestjs/common';
import { Response } from 'express';
import { SSEService } from './sse.service';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { EventEmitterService } from './sse.bridge';
import axios from 'axios';

@Controller('events')
export class SseController {
  constructor(private readonly sseService: SSEService, private readonly eventEmitter: EventEmitterService) {}

  @Sse('')
  @Header('Content-Type', 'text/event-stream')
  @Header('Cache-Control', 'no-cache')
  @Header('Connection', 'keep-alive')
  connect(@Query('client_id') clientId: string, @Res() res: Response) {
    console.log(`Client connected: ${clientId}`);
    return this.sseService.addClient(clientId, res);
  }

  @Post('')
  async testSend(@Body() data: any) {
    this.sseService.sendToClient(data.clientId, data.data);
    return { success: true };
  }

  @MessagePattern(BROKER_PATTERNS.SSE.TARGETTED_MESSAGE)
  async sendToClient(data: { id: string; data: any }) {
    console.log('<===== HIT SSE BROKER =====>');
    console.log({ data });

    try {
      // Use internal loopback since both services are in the same container
      const url = `http://127.0.0.1:${process.env.PORT || 4000}/events`;

      await axios.post(url, {
        clientId: data.id,
        data: data.data,
      });

      console.log(`Successfully forwarded SSE message to HTTP server for client ${data.id}`);
    } catch (error) {
      console.error(`Failed to forward SSE message to HTTP server:`, error);
      console.error(`Error details:`, error.response?.data || error.message);
    }
  }
}
