import {
  BadRequestException,
  HttpException,
  Injectable,
  Logger,
  NotFoundException,
  NotImplementedException,
  PreconditionFailedException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, PaginateModel } from 'mongoose';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { IRequest } from '../../interfaces/request.interface';
import { InvoiceCreateDto, InvoiceDraftDto, SimpleInvoiceCreateDto } from '../../models/dtos/invoice.dto';
import { PaystackRepository } from '../../repositories/paystack.repository';
import { S3Repository } from '../../repositories/s3.repositories';
import { checkIfUserOwnsStore, genChars, generateNumber, toCurrency } from '../../utils';
import { Customer } from '../orders/customers/customer.schema';
import { Order, ORDER_STATUSES } from '../orders/order.schema';
import { Payment } from '../payment/payment.schema';
import { Receipt } from '../receipts/receipts.schema';
import { Store, StoreDocument } from '../store/store.schema';
import { Wallet } from '../wallets/wallet.schema';
import { PaginatedQueryDto } from './dtos/get-invoices';
import { Invoice, InvoiceDocument, INVOICE_STATUSES, INVOICE_TYPE } from './invoice.schema';
import { COUNTRIES_WITH_PAYMENTS } from '../../utils/permissions.util';
import { COUNTRY_CODE, CURRENCIES } from '../country/country.schema';
import { generatePDFFromWebpage } from '../../utils/generate-pdfs';
import { send, title } from 'process';
import { getDocId, getExchangeRates } from '../../utils/functions';
import { CurrencyRates } from '../wallets/currency-conversion/currency-rate.schema';
import { CurrencyConversionRepository } from '../../repositories/currency-conversions.repository';
import { ResendRepository } from '../../repositories/resend.repository';

@Injectable()
export class InvoiceService {
  constructor(
    private logger: Logger,
    private readonly s3: S3Repository,
    private readonly paystack: PaystackRepository,
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(Invoice.name)
    private readonly invoiceModel: PaginateModel<InvoiceDocument>,
    private readonly currencyConversion: CurrencyConversionRepository,
    private readonly resend: ResendRepository,
  ) {}

  private async create(data: InvoiceDraftDto, fromOrder: boolean = false) {
    let sender: Store, customer: Customer;

    sender = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: data.store })
      .toPromise();

    if (!sender.payments_enabled) throw new PreconditionFailedException('Store does not have payments enabled');

    if (data?.customer) {
      customer = await this.brokerTransport
        .send<Customer>(BROKER_PATTERNS.ORDER.GET_CUSTOMER, {
          _id: data.customer,
        })
        .toPromise();
    }

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    const exchangeRates =
      data.currency !== sender.currencies.products
        ? await this.currencyConversion.getExchangeRates(null, [data.currency], sender.currencies.products)
        : { [data.currency]: 1 };

    if (!exchangeRates) {
      throw new BadRequestException("Couldn't get exchange rates");
    }

    const convertCurrency = (amount: number) =>
      this.currencyConversion.convertToCurrency(amount, data.currency, exchangeRates, sender?.currencies?.rates);

    const convertedItems = data.items.map((i) => ({
      ...i,
      price: fromOrder ? i.price : convertCurrency(i.price),
    }));

    const itemsTotal =
      convertedItems?.length > 0
        ? convertedItems.map((i) => Number(i.price) * i.quantity).reduce((a, b) => a + b, 0)
        : 0;

    //Fees are provided in the currency of the invoice
    const feesTotal = data?.fees?.length > 0 ? data?.fees.map((f) => f.amount).reduce((a, b) => a + b, 0) : 0;

    const paymentEnabled = !!sender?.wallets?.find((w) => w.currency === data.currency);

    let invoice = new this.invoiceModel({
      date_due: data?.date_due ?? '',
      date_created: data?.date_created ?? '',
      title: data.title,
      status: INVOICE_STATUSES.PENDING,
      items: convertedItems ?? [],
      fees:
        data?.fees.map((f) => ({
          type: f.type,
          amount: f.amount,
          title: f?.label || f.type,
        })) ?? [],
      payments_enabled: paymentEnabled,
      total_amount: itemsTotal + feesTotal,
      receiver: {
        name: customer?.name ?? '',
        phone: customer?.phone ?? '',
        email: customer?.email ?? '',
        id: customer?.id ?? '',
      },
      sender: {
        name: sender.name,
        image: data?.store_logo ?? sender.logo ?? '',
        phone: sender.phone,
        address: data?.store_address ?? sender.address ?? '',
      },
      store: data.store ?? '',
      invoice_id: await this.generateInvoiceID(),
      currency: data?.currency ?? 'NGN',
      type: INVOICE_TYPE.REGULAR,
    });

    // this.saveInvoicePdf(invoice.id).then(u => {
    //   console.log('S3!!', u)
    //   invoice.pdf_link = u.Location
    // }).finally(() => {
    //   invoice.save();
    // })
    await invoice.save();
    return invoice;
  }

  async newInvoice(data: InvoiceCreateDto) {
    const invoice = await this.create(data);
    invoice.save();
    return invoice.toJSON();
  }

  async createPaymentLink(data: SimpleInvoiceCreateDto) {
    let sender: Store, customer: Customer;

    sender = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: data.store })
      .toPromise();

    customer = await this.brokerTransport
      .send<Customer>(BROKER_PATTERNS.ORDER.GET_CUSTOMER, {
        _id: data.customer,
      })
      .toPromise();

    if (!customer) {
      throw new NotFoundException('Invalid customer selected');
    }

    const paymentEnabled = !!sender?.wallets?.find((w) => w.currency === data.currency);

    const invoice = new this.invoiceModel({
      title: data.narration,
      status: INVOICE_STATUSES.PENDING,
      items: [
        {
          name: data.narration,
          price: data.amount,
          quantity: 1,
        },
      ],
      total_amount: data.amount,
      receiver: {
        name: customer?.name ?? '',
        phone: customer?.phone ?? '',
        email: customer?.email ?? '',
        id: customer?.id ?? '',
      },
      sender: {
        name: sender.name,
        image: data?.store_logo ?? sender.logo ?? '',
        phone: sender.phone,
        address: data?.store_address ?? sender.address ?? '',
      },
      store: data.store ?? '',
      invoice_id: await this.generateInvoiceID(),
      currency: data?.currency ?? 'NGN',
      type: INVOICE_TYPE.PAYMENT_LINK,
      payments_enabled: paymentEnabled,
    });

    await invoice.save();
    return invoice.toJSON();
  }

  async updatePaymentLink(req: IRequest, invoiceId: string, data: SimpleInvoiceCreateDto) {
    let customer: Customer;

    const invoice = await this.invoiceModel.findOne({ _id: invoiceId });

    if (!invoice) {
      throw new NotFoundException('Invoice not found');
    }

    if (invoice.status !== INVOICE_STATUSES.PENDING)
      throw new BadRequestException('You can only edit pending invoices');

    checkIfUserOwnsStore(req.user.store as Store, invoice.store);

    customer = await this.brokerTransport
      .send<Customer>(BROKER_PATTERNS.ORDER.GET_CUSTOMER, {
        _id: data.customer,
      })
      .toPromise();

    if (!customer) {
      throw new NotFoundException('Invalid customer selected');
    }

    invoice.title = data.narration;
    invoice.items = [
      {
        name: data.narration,
        price: data.amount,
        quantity: 1,
        id: '',
      },
    ];
    invoice.total_amount = data.amount;
    invoice.currency = data.currency;
    invoice.receiver = {
      name: customer?.name ?? '',
      phone: customer?.phone ?? '',
      email: customer?.email ?? '',
      id: customer?.id ?? '',
    };

    await invoice.save();
    return invoice.toJSON();
  }

  async fromOrder(order: Order) {
    const invoice = await this.create(
      {
        customer: (order.customer as any).id,
        store: order.store.toString(),
        title: 'Payment for Order #' + order.id,
        date_created: new Date(),
        date_due: new Date(Date.now() + 7 * 24 * 36e5),
        items: order.items.map((i) => ({
          id: i.item_id,
          name: i.snapshot.name,
          price: i.variant ? i.variant.price : i.snapshot.price, //check needed to check for variant price
          quantity: i.quantity,
        })),
        fees: order.fees as any,
        currency: order?.currency,
      },
      true,
    );

    invoice._id = invoice.id;
    invoice.invoice_id = await this.generateInvoiceID();
    invoice.order = order.id;
    await invoice.save();
    return invoice.toJSON();
  }

  async updateFromOrder(order: Order) {
    const invoice = await this.invoiceModel.findOne({ order: order.id });

    if (!invoice) return; // Order has no corresponding invoice. Do nothing

    if ([ORDER_STATUSES.CANCELLED].includes(order.status)) {
      invoice.status = INVOICE_STATUSES.EXPIRED;
      await invoice.save();

      return invoice.toJSON() as Invoice;
    }

    if (invoice.status !== INVOICE_STATUSES.PENDING) return invoice.toJSON();

    await invoice.update(
      {
        fees: (order.fees.map((f) => ({ ...f, title: f.label })) as any) ?? [],
        items: order.items.map((i) => ({
          id: i.item_id,
          quantity: i.quantity,
          name: i.snapshot.name,
          price: i.variant ? i.variant.price : i.snapshot.price,
        })),
        total_amount:
          order.items
            .map((i) => Number(i.variant ? i.variant.price : i.snapshot.price) * i.quantity)
            .reduce((a, b) => a + b, 0) + order.fees.map((f) => f.amount).reduce((a, b) => a + b, 0),
      },
      { new: true },
    );

    return invoice.toJSON() as Invoice;
  }

  async addReceiptToInvoice(filter: FilterQuery<Invoice>, receipt_id: string) {
    const invoice = await this.invoiceModel.findOne(filter);

    if (!invoice) return; // Order has no corresponding invoice. Do nothing

    invoice.receipt = receipt_id;
    invoice.save();

    return invoice.toJSON() as Invoice;
  }

  async getInvoice(filter: FilterQuery<Invoice>) {
    return await this.invoiceModel.findOne(filter);
  }

  async getStatistics(storeId: string) {
    const invoices = await this.invoiceModel.find({ store: storeId });
    const currencies = [];
    const groupedData = {};

    for (let index = 0; index < invoices.length; index++) {
      const invoice = invoices[index];
      const currency = invoice.currency;

      if (!currencies.includes(currency)) currencies.push(currency);

      const data = groupedData[currency]
        ? { ...groupedData[currency] }
        : {
            total_invoices: 0,
            total_amount_received: 0,
            total_paid_invoices: 0,
            invoices_unpaid: 0,
          };

      data.total_invoices += 1;

      if (invoice.status === INVOICE_STATUSES.PAID) {
        data.total_paid_invoices += 1;
        data.total_amount_received += invoice.total_amount;
      } else {
        data.invoices_unpaid += 1;
      }

      groupedData[currency] = data;
    }

    // const paidInvoices = invoices.filter((i) => i.status === INVOICE_STATUSES.PAID);

    // const totalInvoices = invoices.length;
    // const totalAmountRecieved =
    //   paidInvoices.length == 0 ? 0 : paidInvoices.map((i) => i.total_amount).reduce((a, b) => a + b, 0);
    // const totalPaidInvoices = paidInvoices.length;
    // const invoicesUnpaid = totalInvoices - totalPaidInvoices;

    return {
      // total_invoices: totalInvoices,
      // total_amount_received: totalAmountRecieved,
      // total_paid_invoices: totalPaidInvoices,
      // invoices_unpaid: invoicesUnpaid,
      currencies,
      groupedData,
    };
  }

  async draft(data: InvoiceDraftDto) {
    const invoice = await this.create(data);
    invoice.is_draft = true;
    await invoice.save();

    return invoice;
  }

  async update(req: IRequest, invoiceId: string, data: InvoiceCreateDto) {
    const invoice = await this.invoiceModel.findOne({ _id: invoiceId });

    if (!invoice) {
      throw new NotFoundException('Invoice not found');
    }

    checkIfUserOwnsStore(req.user.store as Store, invoice.store);

    if (invoice.status !== INVOICE_STATUSES.PENDING) {
      throw new BadRequestException('You can only update a pending invoice or draft');
    }

    let sender: Store, customer: Customer;

    sender = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: data.store })
      .toPromise();

    if (data?.customer) {
      customer = await this.brokerTransport
        .send<Customer>(BROKER_PATTERNS.ORDER.GET_CUSTOMER, {
          _id: data.customer,
        })
        .toPromise();
    }

    if (!customer) {
      throw new NotFoundException('Customer not found');
    }

    const itemsTotal =
      data?.items?.length > 0 ? data?.items.map((i) => Number(i.price) * i.quantity).reduce((a, b) => a + b) : 0;

    const feesTotal = data?.fees?.length > 0 ? data?.fees.map((f) => f.amount).reduce((a, b) => a + b) : 0;

    const response = await this.invoiceModel
      .findOneAndUpdate(
        { _id: invoice.id },
        {
          date_due: data?.date_due ?? invoice?.date_due,
          date_created: data?.date_created ?? invoice?.date_created,
          title: data.title ?? invoice?.title,
          items: data?.items,
          fees: data?.fees.map((f) => ({
            type: f.type,
            amount: f.amount,
            title: f.type,
          })),
          payments_enabled: true,
          total_amount: itemsTotal + feesTotal,
          receiver: {
            name: customer?.name,
            phone: customer?.phone,
            email: customer?.email,
            id: customer?.id,
          },
          sender: {
            name: sender.name,
            image: data?.store_logo,
            phone: sender.phone,
            address: data?.store_address,
          },
          store: data.store,
          is_draft: false,
        },
        { new: true },
      )
      .exec();

    return response.toJSON();
  }

  async updateDraft(id: string, data: any) {
    const invoice = await this.invoiceModel.findOneAndUpdate({ _id: id }, { ...data }, { new: true });

    return invoice.toJSON();
  }

  async updateInvoiceToExpired(storeId: string, invoiceId: string) {
    const invoice = await this.invoiceModel.findOne({ _id: invoiceId });

    if (!invoice) {
      throw new BadRequestException('Invoice not found');
    }

    if (storeId !== invoice.store) {
      throw new BadRequestException('Invoice does not belong to store ');
    }

    invoice.status = INVOICE_STATUSES.EXPIRED;
    await invoice.save();
    return invoice;
  }

  async upadateInvoiceToPaid(user: IRequest['user'], invoiceId: string) {
    const invoice = await this.invoiceModel.findOne({ _id: invoiceId });

    if (!invoice) {
      throw new BadRequestException('Invoice not found');
    }

    if (user.store.id !== invoice.store) {
      throw new BadRequestException('Invoice does not belong to store ');
    }

    const result = await this.invoicePaid(invoice.invoice_id, PAYMENT_METHODS.UNKNOWN);

    if ((result as any).error) {
      throw new BadRequestException((result as any).error);
    }
    return result;

    // invoice.status = INVOICE_STATUSES.PAID;
    // invoice.paid_at = new Date();
    // await invoice.save();

    // const receipt = await this.brokerTransport
    //   .send<Receipt>(BROKER_PATTERNS.RECEIPT.GENERATE_FROM_INVOICE, {
    //     invoice,
    //     payment_method: PAYMENT_METHODS.UNKNOWN,
    //   })
    //   .toPromise();

    // invoice.receipt = receipt.receipt_id;
    // invoice.save();

    // return invoice;
  }

  async invoicePaid(id: string, payment_method: PAYMENT_METHODS, fromOrder?: boolean, paymentId?: string) {
    const invoice = await this.invoiceModel.findOne({ invoice_id: id });

    if (invoice.is_draft) {
      return {
        error: 'Cannot mark a draft invoice as paid',
      };
    }

    invoice.status = INVOICE_STATUSES.PAID;
    invoice.paid_at = new Date();
    invoice.payment_id = paymentId;
    await invoice.save();

    const receipt = await this.brokerTransport
      .send<Receipt>(BROKER_PATTERNS.RECEIPT.GENERATE_FROM_INVOICE, {
        invoice,
        payment_method,
      })
      .toPromise();

    invoice.receipt = receipt.receipt_id;
    invoice.save();

    if (invoice.order && !fromOrder) {
      await this.brokerTransport
        .send(BROKER_PATTERNS.ORDER.MARK_ORDER_AS_PAID, {
          filter: { _id: invoice.order },
          receipt: receipt.receipt_id,
          paymentId,
        })
        .toPromise();
    }

    return invoice.toJSON() as Invoice;
  }

  async getInvoicePublic(id: string) {
    const invoice = await this.invoiceModel.findOne({
      $and: [{ $or: [{ invoice_id: id }, { invoiceId: id }] }, { is_draft: { $ne: true } }],
    });

    if (!invoice) {
      throw new NotFoundException('Invoice not found');
    }

    const wallet = await this.brokerTransport
      .send<Wallet>(BROKER_PATTERNS.WALLET.GET_WALLET, { storeId: invoice.store })
      .toPromise();

    return { ...invoice.toJSON(), settlement_account: wallet?.accounts[1] ?? wallet?.accounts[0] };
  }

  async getInvoices(
    storeId: string,
    { search, ...filter }: FilterQuery<InvoiceDocument> & { search: string },
    paginationQuery: PaginatedQueryDto,
  ) {
    filter = {
      ...filter,
      is_deleted: {
        $ne: true,
      },
      store: storeId,
    };

    if (search) {
      filter.$or = [
        { title: new RegExp(search, 'ig') },
        { 'receiver.name': new RegExp(search, 'ig') },
        { invoice_id: new RegExp(search, 'ig') },
      ];
    }

    const result = await this.invoiceModel.paginate(filter, {
      sort: { createdAt: -1 },
      page: paginationQuery.page || 1,
      limit: paginationQuery.per_page || 50,
      lean: true,
    });

    return {
      data: {
        store: storeId,
        invoices: result.docs,
      },
      page: result.page,
      next_page: result.nextPage,
      prev_page: result.prevPage,
      total: result.totalDocs,
      total_pages: result.totalPages,
      per_page: result.limit,
    };
  }

  async generateInvoicePdf(invoiceId: string) {
    const invoiceUrl = process.env.CATLOG_WWW + '/invoices/pdf/' + invoiceId;
    const data = await generatePDFFromWebpage(invoiceUrl, 800, 800 * 1.414, { bottom: 15 });

    if (data.pdf) {
      return data.pdf;
    } else {
      throw new BadRequestException("Couldn't generate invoice PDF");
    }
  }

  async mailInvoice(user: IRequest['user'], message: string, email: string, invoiceId: string) {
    const invoice = await this.invoiceModel.findOne({ _id: invoiceId });

    if (!invoice) {
      throw new BadRequestException('Invoice not found');
    }

    checkIfUserOwnsStore(user.store as Store, invoice.store);

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: invoice.store })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store not found');
    }

    this.resend.sendEmail(BROKER_PATTERNS.MAIL.INVOICE_RECEIVED, {
      to: email,
      subject: `You have an Invoice,  #${invoice.invoice_id} from ${store?.name} 🧾`,
      data: {
        name: invoice.receiver.name.split(' ')[0],
        invoice_amount: toCurrency(invoice.total_amount.toString(), invoice.currency, 2),
        store_name: store.name,
        message: message,
        invoice_link: process.env.CATLOG_WWW + '/invoices/' + invoice.invoice_id,
      },
      attachment: process.env.CATLOG_API + '/invoices/pdf/' + invoice.invoice_id,
      attachmentName: 'invoice-' + invoice.invoice_id + '.pdf',
    });

    return invoice;
  }

  async deleteInvoice(user: IRequest['user'], invoiceId: string) {
    const invoice = await this.invoiceModel.findOne({ _id: invoiceId });

    if (!invoice) {
      throw new BadRequestException('Invoice not found');
    }

    if (user.store.id !== invoice.store) {
      throw new BadRequestException('Invoice does not belong to store ');
    }

    return invoice.deleteOne();
  }

  async getPdfFile(invoiceId: string) {
    const invoice = await this.invoiceModel.findOne({ invoice_id: invoiceId });

    if (!invoice) {
      throw new BadRequestException('Invalid Invoice');
    }

    return await this.generateInvoicePdf(invoiceId);
  }

  async getSetupProgress(storeId: string) {
    const store = await this.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (store && store?.wallet) {
      const walletSetupProgress = await this.brokerTransport
        .send<{ has_made_test_payment: boolean; has_withdrawal_accounts: boolean }>(
          BROKER_PATTERNS.WALLET.GET_SETUP_PROGRESS,
          store.wallet,
        )
        .toPromise();

      const deliveryAreaCount = await this.brokerTransport
        .send<number>(BROKER_PATTERNS.STORE.COUNT_DELIVERY_AREAS, storeId)
        .toPromise();

      const data = {
        ...walletSetupProgress,
        has_made_test_payment: store?.onboarding_steps?.test_payment_made,
        security_pin_added: store?.onboarding_steps?.security_pin_added,
        has_enabled_direct_checkout: store.configuration.direct_checkout_enabled,
        has_delivery_areas: deliveryAreaCount > 0,
      };

      return data;
    }

    throw new UnauthorizedException('No wallet found for this store');
  }

  async migratePaymentsEnabled() {
    const invoices = await this.invoiceModel.find({}).select('currency').lean();

    const updates: Promise<InvoiceDocument>[] = [];

    for (const invoice of invoices) {
      updates.push(
        this.invoiceModel
          .findByIdAndUpdate(invoice._id, {
            payments_enabled: invoice.currency === CURRENCIES.NGN,
          })
          .exec(),
      );
    }

    const res = await Promise.all(updates);

    return res;
  }

  async migrateOrderPayments() {
    const BATCH_SIZE = 50;
    const DELAY_BETWEEN_BATCHES_MS = 5000; // 5 seconds delay between batches

    let processedCount = 0;
    let hasMoreInvoices = true;

    while (hasMoreInvoices) {
      // Find a batch of invoices
      const invoicesWithPossiblePayments = await this.invoiceModel
        .find({ status: INVOICE_STATUSES.PAID, payment_id: { $exists: false } })
        .skip(processedCount)
        .limit(BATCH_SIZE)
        .lean();

      // Check if this is the last batch
      if (invoicesWithPossiblePayments.length < BATCH_SIZE) {
        hasMoreInvoices = false;
      }

      // Process this batch only if we have invoices
      if (invoicesWithPossiblePayments.length > 0) {
        const updateFunctions = [];

        // Prepare update functions for each invoice in this batch
        for (const invoice of invoicesWithPossiblePayments) {
          const payment = await this.brokerTransport
            .send<Payment>(BROKER_PATTERNS.PAYMENT.GET_PAYMENT, { 'meta.invoice': invoice.invoice_id })
            .toPromise();

          if (payment) {
            const paymentId = getDocId(payment);
            updateFunctions.push(async () => {
              await this.invoiceModel.findByIdAndUpdate(getDocId(invoice), {
                payment_id: paymentId,
              });

              if (invoice.order) {
                await this.brokerTransport
                  .send(BROKER_PATTERNS.ORDER.UPDATE_ORDER, {
                    filter: { _id: invoice?.order },
                    payload: { payment_id: paymentId },
                  })
                  .toPromise();
              }
            });
          }
        }

        // Execute all functions in this batch concurrently
        const promises = updateFunctions.map((fn) => fn());
        await Promise.all(promises);

        // Update the processed count and log progress
        processedCount += invoicesWithPossiblePayments.length;
        console.log(`Processed ${processedCount} invoices with ${updateFunctions.length} updates`);

        // Wait before processing next batch (unless this was the last batch)
        if (hasMoreInvoices) {
          await new Promise((resolve) => setTimeout(resolve, DELAY_BETWEEN_BATCHES_MS));
        }
      } else {
        // No invoices in this batch, we're done
        hasMoreInvoices = false;
      }
    }

    return { processedCount };
  }

  private async generateInvoiceID() {
    let id = 'CLGI-' + genChars(8, false, true);
    while (await this.invoiceModel.exists({ invoice_id: id })) {
      id = 'CLGI-' + genChars(8, false, true);
    }

    return id;
    // return id;
  }
}
