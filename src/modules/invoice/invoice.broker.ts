import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { FilterQuery } from 'mongoose';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { Customer } from '../orders/customers/customer.schema';
import { Order } from '../orders/order.schema';
import { Invoice, InvoiceDocument } from './invoice.schema';
import { InvoiceService } from './invoice.service';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class InvoiceBroker {
  constructor(private readonly invoiceService: InvoiceService) {}

  @MessagePattern(BROKER_PATTERNS.INVOICE.GET_INVOICE)
  async getInvoice(filter: FilterQuery<Invoice>) {
    return this.invoiceService.getInvoice(filter);
  }

  @MessagePattern(BROKER_PATTERNS.INVOICE.FROM_ORDER)
  async invoiceFromOrder(order: Order) {
    return this.invoiceService.fromOrder(order);
  }
  @MessagePattern(BROKER_PATTERNS.INVOICE.UPDATE_TO_EXPIRED)
  async updateToExpired(data: { invoice_id: string; store_id: string }) {
    return this.invoiceService.updateInvoiceToExpired(data.store_id, data.invoice_id);
  }

  @MessagePattern(BROKER_PATTERNS.INVOICE.UPDATE_FROM_ORDER)
  async invoiceUpdateFromOrder(order: Order) {
    return this.invoiceService.updateFromOrder(order);
  }

  @MessagePattern(BROKER_PATTERNS.INVOICE.GET_STATISTICS)
  async getStatistics(storeId: string) {
    return await this.invoiceService.getStatistics(storeId);
  }

  @MessagePattern(BROKER_PATTERNS.INVOICE.INVOICE_PAID)
  async invoicePaid({
    id,
    payment_method,
    fromOrder = false,
    paymentId = null,
  }: {
    id: string;
    payment_method: PAYMENT_METHODS;
    fromOrder?: boolean;
    paymentId?: string;
  }) {
    return await this.invoiceService.invoicePaid(id, payment_method, fromOrder, paymentId);
  }
}
