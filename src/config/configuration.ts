import { registerAs } from '@nestjs/config';
import { MongodbConfig } from './types/mongodb.config';
import { NatsOptions, Transport } from '@nestjs/microservices';
import { RedisConfig } from './types/redis.config';
import { JwtConfig } from './types/jwt.config';
import { S3Config } from './types/s3.config';
import { PaystackConfig } from './types/paystack.config';
import { ZillaConfig } from './types/zilla.config';
import { MailchimpConfig } from './types/mailchimp.config';
import { MonoConfig } from './types/mono.config';
import { DojahConfig } from './types/dojah.config';
import { MonnifyConfig } from './types/monnify.config';
import { TwitterApiConfig } from './types/twitter.config';
import { ApiGuardConfig } from './types/api-guard.config';
import { YouVerifyApiConfig } from './types/youverify.config';
import { SquadConfig } from './types/squad.config';
import { PushNotificationConfig } from './types/pushnotification.config';
import { ThepeerConfig } from './types/thepeer.config';
import { SlackConfig } from './types/slack.config';
import { ShipbubbleConfig } from './types/shipbubble.config';
import { BlocHQConfig } from './types/bloc.config';
import { WhatsappConfig } from './types/whatsapp.config';
import { InstagramConfig } from './types/instagram.config';
import { OpenaiConfig } from './types/openai.config';
import { ReCaptchaConfig } from './types/recaptcha.config';
import { BrevoConfig } from './types/brevo.config';
import { FlutterWaveConfig } from './types/flutterwave.config';
import { PosthogConfig } from './types/posthog.config';
import { ChowdeckConfig } from './types/chowdeck.config';
import { GoogleMapsConfig } from './types/maps.google.config';
import { KoraPayConfig } from './types/korapay.config';
import { ZeepayConfig } from './types/zeepay.config';
import { ThrottleConfig } from './types/throttle.config';
import { ONE_MINUTE } from '../utils/constants';
import { ResendConfig } from './types/resend.config';
import { PayazaConfig } from './types/payaza.config';
import { FincraConfig } from './types/fincra.config';
import { StartbuttonConfig } from './types/startbutton.config';
import { FezDeliveryConfig } from './types/fez-delivery.config';
import { StripeConfig } from './types/stripe.config';
import { CustomerIoConfig } from './types/customer-io.config';
import { ZohoConfig } from './types/zoho.config';
import { ShaqExpressConfig } from './types/shaq-express.config';
import { HostApiConfig } from './types/host-api.config';
import { MetaConfig } from './types/meta.config';
import { Go54Config } from './types/go54.config';

/**
 * ESSENTIAL APP CONFIGURATIONS
 */

const BrokerTransportConfiguration = registerAs(
  'brokerTransportConfiguration',
  (): NatsOptions => ({
    transport: Transport.NATS,
    options: {
      url: process.env.NATS_URL || 'nats://localhost:4222',
      user: process.env.NATS_USERNAME || '',
      pass: process.env.NATS_PASSWORD || '',
      queue: 'stream',
    },
  }),
);

const JwtConfiguration = registerAs(
  'jwtConfiguration',
  (): JwtConfig => ({
    secret: process.env.JWT_SECRET || '1234567',
    expiresIn: Number(process.env.JWT_EXPIRY) || 3600000, // seconds 1
  }),
);

const MongoDbConfiguration = registerAs(
  'mongodbConfiguration',
  (): MongodbConfig => ({
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/catlog',
  }),
);

const ThrottleConfiguration = registerAs(
  'throttleConfiguration',
  (): ThrottleConfig => ({
    ttl: Number(process.env.THROTTLE_TTL) || ONE_MINUTE,
    limit: Number(process.env.THROTTLE_LIMIT) || 30,
  }),
);

const RedisConfiguration = registerAs(
  'redisConfiguration',
  (): RedisConfig => ({
    redisHost: process.env.REDIS_HOST || 'localhost',
    redisTTL: process.env.REDIS_TTL || '90',
    redisPort: process.env.REDIS_PORT || '6379',
    redisDb: Number(process.env.REDIS_DB) || 0,
    redisPass: process.env.REDIS_PASSWORD || '',
  }),
);

const S3Configuration = registerAs(
  's3Configuration',
  (): S3Config => ({
    S3_BUCKET_NAME: process.env.S3_BUCKET_NAME || '',
    S3_ACCESS_KEY: process.env.S3_ACCESS_KEY || '',
    S3_REGION: process.env.S3_REGION || '',
    S3_SECRET_KEY: process.env.S3_SECRET_KEY || '',
  }),
);

const ApiGuardConfiguration = registerAs(
  'apiGuardConfig',
  (): ApiGuardConfig => ({
    rn:
      process.env.NODE_ENV === 'PRODUCTION'
        ? (() =>
            Array.from(Array(256).keys()).map(() =>
              String.fromCharCode(0x20 + Math.round(Math.random() * 0x60)),
            ))().join('')
        : process.env.LOCAL_SALT_KEY ||
          `%D3p/wX]V=+ix-6hp muxF2[;ds-NP8hx#|Y+N\\7r*k(,! =nE0L,Z%s]B?kYsp]Mn.TzQ*J$dv^U"BxI)>k$,HZw<:/[7X$\\?:Rwgo')Vg;zbitN*2W$OZ-i4I!zcry&C<O_1~6|A;Th?&9SGFj"vq|OOfZHVJ3H)Rty$liguw{B}[4W{pqqJa?he, :(5CziNN\\D}qf)^Kcw'K|sJ6neuA\`u8xj]WhONiG@i<W*?y\\2>/n/V&93aTx/`,
    username: process.env.INTERNAL_USERNAME,
    password: process.env.INTERNAL_PASSWORD,
    otpSalt: process.env.OTP_SALT,
  }),
);

/**
 * PAYMENT THIRD PARTIES
 */

const BlockHQConfiguration = registerAs(
  'blochqConfiguration',
  (): BlocHQConfig => ({
    publicKey: process.env.BLOCHQ_PUBLIC_KEY,
    privateKey: process.env.BLOCHQ_PRIVATE_KEY,
    webhookKey: process.env.BLOCHQ_WEBHOOK_KEY,
  }),
);

const PaystackConfiguration = registerAs(
  'paystackConfiguration',
  (): PaystackConfig => ({
    privateKey: process.env.PAYSTACK_PRIVATE_KEY,
    publicKey: process.env.PAYSTACK_PUBLIC_KEY,
    ghPrivatekey: process.env.PAYSTACK_GH_PRIVATE_KEY,
    ghPublickey: process.env.PAYSTACK_GH_PUBLIC_KEY,
    zaPrivatekey: process.env.PAYSTACK_ZA_PRIVATE_KEY,
    zaPublickey: process.env.PAYSTACK_ZA_PUBLIC_KEY,
  }),
);

const FlutterWaveConfiguration = registerAs(
  'flutterwaveConfiguration',
  (): FlutterWaveConfig => ({
    privateKey: process.env.FW_SECRET_KEY,
    publicKey: process.env.FW_PUBLIC_KEY,
    secretHash: process.env.FW_SECRET_HASH,
    encryptionKey: process.env.FW_ENCRYPTION_KEY,
  }),
);

const KoraPayConfiguration = registerAs(
  'korapayConfiguration',
  (): KoraPayConfig => ({
    privateKey: process.env.KP_SECRET_KEY,
    publicKey: process.env.KP_PUBLIC_KEY,
    encryptionKey: process.env.KP_ENCRYPTION_KEY,
  }),
);

const ZeepayConfiguration = registerAs(
  'zeepayConfiguration',
  (): ZeepayConfig => ({
    client_id: process.env.ZEEPAY_CLIENT_ID,
    client_secret: process.env.ZEEPAY_CLIENT_SECRET,
    username: process.env.ZEEPAY_USERNAME,
    password: process.env.ZEEPAY_PASSWORD,
    ip_address: process.env.ZEEPAY_IP_ADDRESS,
    base_url: process.env.ZEEPAY_BASE_URL,
  }),
);

const MonoConfiguration = registerAs(
  'monoConfiguration',
  (): MonoConfig => ({
    apikey: process.env.MONO_SECRET_KEY,
    publicKey: process.env.MONO_PUBLIC_KEY,
    webhookKey: process.env.MONO_WEBHOOK_KEY,
  }),
);

const MonnifyConfiguration = registerAs(
  'monnifyConfiguration',
  (): MonnifyConfig => ({
    apiUrl: process.env.MONNIFY_API_URL,
    apiKey: process.env.MONNIFY_API_KEY,
    secretKey: process.env.MONNIFY_SECRET_KEY,
    contractCode: process.env.MONNIFY_CONTRACT_CODE,
  }),
);

const ThepeerConfiguration = registerAs(
  'thepeerConfiguration',
  (): ThepeerConfig => ({
    apiUrl: process.env.THEPEER_API_URL,
    publicKey: process.env.THEPEER_PUBLIC_KEY,
    secretKey: process.env.THEPEER_PRIVATE_KEY,
  }),
);

const ZillaConfiguration = registerAs(
  'zillaConfiguration',
  (): ZillaConfig => ({
    secretKey: process.env.ZILLA_SECRET_KEY,
    publicKey: process.env.ZILLA_PUBLIC_KEY,
    hashKey: process.env.ZILLA_HASH_KEY,
    merchantId: process.env.ZILLA_MERCHANT_ID,
    apiUrl: process.env.ZILLA_API_URL,
  }),
);

const SquadConfiguration = registerAs(
  'squadConfiguration',
  (): SquadConfig => ({
    privateKey: process.env.SQUAD_PRIVATE_KEY,
    publicKey: process.env.SQUAD_PUBLIC_KEY,
    apiUrl: process.env.SQUAD_API_URL,
    merchantID: process.env.SQUAD_MERCHANT_ID,
    convoyWebhookUrl: 'https://dashboard.getconvoy.io/ingest/8lXRxHjllzE3Qb9h',
  }),
);

const PayazaConfiguration = registerAs(
  'payazaConfiguration',
  (): PayazaConfig => ({
    publicKey: process.env.PAYAZA_PUBLIC_KEY,
    secretKey: process.env.PAYAZA_SECRET_KEY,
  }),
);

const StartbuttonConfiguration = registerAs(
  'startbuttonConfiguration',
  (): StartbuttonConfig => ({
    publicKey: process.env.SB_PUBLIC_KEY,
    privateKey: process.env.SB_PRIVATE_KEY,
    baseUrl: process.env.SB_BASE_URL,
  }),
);

const FincraConfiguration = registerAs(
  'fincraConfiguration',
  (): FincraConfig => ({
    baseUrl: process.env.FINCRA_BASE_URL,
    secretKey: process.env.FINCRA_SECRET_KEY,
    publicKey: process.env.FINCRA_PUBLIC_KEY,
    webhookSecretKey: process.env.FINCRA_WEBHOOK_SECRET_KEY,
    businessId: process.env.FINCRA_BUSINESS_ID,
  }),
);

const StripeConfiguration = registerAs(
  'stripeConfiguration',
  (): StripeConfig => ({
    secretKey: process.env.STRIPE_SECRET_KEY,
    publicKey: process.env.STRIPE_PUBLIC_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
  }),
);

const LeatherbackConfiguration = registerAs(
  'leatherbackConfiguration',
  (): StripeConfig => ({
    secretKey: process.env.LEATHERBACK_SECRET_KEY,
    publicKey: process.env.LEATHERBACK_PUBLIC_KEY,
    webhookSecret: process.env.LEATHERBACK_WEBHOOK_SECRET,
  }),
);

/**
 * KYC CONFIGURATIONS
 */

const DojahConfiguration = registerAs(
  'dojahConfiguration',
  (): DojahConfig => ({
    apiUrl: process.env.DOJAH_API_URL,
    apiKey: process.env.DOJAH_SECRET_KEY,
    appId: process.env.DOJAH_APP_ID,
  }),
);

const YouVerifyApiConfiguration = registerAs(
  'youVerifyConfiguration',
  (): YouVerifyApiConfig => ({
    apiKey: process.env.YOUVERIFY_API_KEY,
    apiUrl: process.env.YOUVERIFY_API_URL,
  }),
);

/**
 * MAILING CONFIGURATIONS
 */

const MailchimpConfiguiration = registerAs(
  'mailchimpConfiguration',
  (): MailchimpConfig => ({
    apiKey: process.env.MAILCHIMP_APIKEY,
    listId: process.env.MAILCHIMP_LISTID,
    server: process.env.MAILCHIMP_SERVER_PREFIX || 'us6',
    isTesting: process.env.USE_MAILCHIMP_API == 'false',
  }),
);

const BrevoConfiguiration = registerAs(
  'brevoConfiguration',
  (): BrevoConfig => ({
    apiKey: process.env.BREVO_APIKEY,
    listId: process.env.BREVO_LISTID,
    isTesting: process.env.USE_MAILCHIMP_API == 'false',
  }),
);

const ResendConfiguiration = registerAs(
  'resendConfiguration',
  (): ResendConfig => ({
    apiKey: process.env.RESEND_API_KEY,
    isTesting: process.env.USE_MAILCHIMP_API == 'false',
  }),
);

const CustomerIoConfiguration = registerAs(
  'customerIoConfiguration',
  (): CustomerIoConfig => ({
    apiKey: process.env.CUSTOMER_IO_API_KEY,
    siteId: process.env.CUSTOMER_IO_SITE_ID,
    isTesting: process.env.USE_MAILCHIMP_API == 'false',
  }),
);

/**
 * SOCIAL PLATFORMS CONFIGURATIONS
 */

const TwitterApiConfiguration = registerAs(
  'twitterConfiguration',
  (): TwitterApiConfig => ({
    appKey: process.env.TWITTER_API_KEY,
    appSecret: process.env.TWITTER_API_SECRET,

    accessToken: process.env.TWITTER_API_ACCESS_TOKEN,
    accessSecret: process.env.TWITTER_API_ACCESS_SECRET,

    verifier: process.env.TWITTER_API_AUTH_VERIFIER,
  }),
);

const WhatsappConfiguration = registerAs(
  'whatsappConfiguration',
  (): WhatsappConfig => ({
    token: process.env.WHATSAPP_API_TOKEN,
    key: process.env.WHATSAPP_API_KEY,
    // botPhones: {
    //   production: [process.env.WHATSAPP_BOT_NG_PHONE, process.env.WHATSAPP_BOT_GH_PHONE],
    //   development: [process.env.WHATSAPP_BOT_TEST_PHONE],
    // },
  }),
);

const InstagramConfiguration = registerAs(
  'instagramConfiguration',
  (): InstagramConfig => ({
    app_key: process.env.INSTAGRAM_APP_KEY,
    app_secret: process.env.INSTAGRAM_APP_SECRET,
  }),
);

const MetaConfiguration = registerAs(
  'metaConfiguration',
  (): MetaConfig => ({
    pixelId: process.env.META_PIXEL_ID,
    conversionApiKey: process.env.META_CONVERSION_API_KEY,
  }),
);

/**
 * NOTIFICATION CONFIGURATIONS
 */

const PushNotificationConfiguration = registerAs(
  'pushNotificationConfiguration',
  (): PushNotificationConfig => ({
    push_email: process.env.WEB_PUSH_EMAIL,
    public_key: process.env.WEB_PUSH_PUBLIC_KEY,
    private_key: process.env.WEB_PUSH_PRIVATE_KEY,
  }),
);

const SlackWebhookConfiguration = registerAs(
  'slackConfiguration',
  (): SlackConfig => ({
    api_url: process.env?.SLACK_WEBHOOK ?? '',
    isTesting: process.env?.USE_MAILCHIMP_API == 'false',
  }),
);

/**
 * DELIVERIES CONFIGURATIONS
 */

const ShipBubbleConfiguration = registerAs(
  'shipbubbleConfiguration',
  (): ShipbubbleConfig => ({
    apiUrl: process.env?.SHIPBUBBLE_API_URL ?? '',
    apiKey: process.env?.SHIPBUBBLE_KEY ?? '',
    // secretKey: process.env.SHIPBUBBLE_SECRET_KEY,
  }),
);

const ChowdeckConfiguration = registerAs(
  'chowdeckConfiguration',
  (): ChowdeckConfig => ({
    apiToken: process.env.CHOWDECK_API_TOKEN,
    dashboardToken: process.env.CHOWDECK_DASHBOARD_TOKEN,
    catlog_merchant_reference: process.env.CATLOG_CHOWDECK_MERCHANT_REFERENCE,
  }),
);

const FezDeliveryConfiguration = registerAs(
  'fezDeliveryConfiguration',
  (): FezDeliveryConfig => ({
    secret_key: process.env.FEZ_DELIVERY_SECRET_KEY,
    api_url: process.env.FEZ_DELIVERY_API_URL,
    user_id: process.env.FEZ_DELIVERY_USER_ID,
    password: process.env.FEZ_DELIVERY_PASSWORD,
  }),
);

const ShaqExpressConfiguration = registerAs(
  'shaqExpressConfiguration',
  (): ShaqExpressConfig => ({
    api_url: process.env.SHAQ_EXPRESS_API_URL,
    api_token: process.env.SHAQ_EXPRESS_API_KEY,
  }),
);

/**
 * OTHERS
 */

const PosthogConfiguration = registerAs(
  'posthogConfiguration',
  (): PosthogConfig => ({
    publicKeys: {
      chowbot: process.env.POSTHOG_CHOWBOT_PUBLIC_KEY,
    },
    privateKeys: {
      chowbot: process.env.POSTHOG_CHOWBOT_PRIVATE_KEY,
    },
    projectIds: {
      chowbot: process.env.POSTHOG_CHOWBOT_PROJECT_ID,
    },
  }),
);

const OpenaiConfiguration = registerAs(
  'openaiConfiguration',
  (): OpenaiConfig => ({
    api_key: process.env.OPENAI_API_KEY,
  }),
);

const ReCaptchaConfiguration = registerAs(
  'reCaptchaConfiguration',
  (): ReCaptchaConfig => ({
    secretKey: process.env.RECAPTCHA_SECRET_KEY,
    apiUrl: process.env.RECAPTCHA_API_URL,
  }),
);

const GoogleMapsConfig = registerAs(
  'googleMapsConfiguration',
  (): GoogleMapsConfig => ({
    publicKey: process.env.GOOGLE_MAPS_API_KEY,
  }),
);

const ZohoConfiguration = registerAs(
  'zohoConfiguration',
  (): ZohoConfig => ({
    clientId: process.env.ZOHO_CLIENT_ID,
    clientSecret: process.env.ZOHO_CLIENT_SECRET,
    refreshToken: process.env.ZOHO_REFRESH_TOKEN,
  }),
);

const HostApiConfiguration = registerAs(
  'hostApiConfiguration',
  (): HostApiConfig => ({
    baseUrl: process.env.HOST_API_URL || 'https://server.stx.catlog.shop',
    apiKey: process.env.HOST_API_KEY || 'test-key',
  }),
);

/**
 * Domain API Services
 */

const Go54Configuration = registerAs(
  'go54Configuration',
  (): Go54Config => ({
    apiUrl: process.env.GO54_API_URL,
    email: process.env.GO54_EMAIL,
    password: process.env.GO54_PASSWORD,

    // Whogohost Domains Reseller API configuration
    whogohostEndpoint: process.env.WHOGOHOST_ENDPOINT,
    whogohostUsername: process.env.WHOGOHOST_USERNAME,
    whogohostSecretKey: process.env.WHOGOHOST_SECRET_KEY,
    // whogohostTlds: (process.env.WHOGOHOST_TLDS || '.com,.com.ng,.shop,.africa,.co.za,.co.ke,.online').split(','),
  }),
);

export {
  MongoDbConfiguration,
  BrokerTransportConfiguration,
  RedisConfiguration,
  JwtConfiguration,
  S3Configuration,
  PaystackConfiguration,
  ZillaConfiguration,
  MailchimpConfiguiration,
  MonoConfiguration,
  DojahConfiguration,
  MonnifyConfiguration,
  YouVerifyApiConfiguration,
  TwitterApiConfiguration,
  ApiGuardConfiguration,
  SquadConfiguration,
  PushNotificationConfiguration,
  ThepeerConfiguration,
  SlackWebhookConfiguration,
  ShipBubbleConfiguration,
  BlockHQConfiguration,
  WhatsappConfiguration,
  InstagramConfiguration,
  OpenaiConfiguration,
  ReCaptchaConfiguration,
  BrevoConfiguiration,
  FlutterWaveConfiguration,
  KoraPayConfiguration,
  PosthogConfiguration,
  ChowdeckConfiguration,
  GoogleMapsConfig,
  ZeepayConfiguration,
  ThrottleConfiguration,
  ResendConfiguiration,
  PayazaConfiguration,
  FincraConfiguration,
  StartbuttonConfiguration,
  FezDeliveryConfiguration,
  ShaqExpressConfiguration,
  StripeConfiguration,
  LeatherbackConfiguration,
  CustomerIoConfiguration,
  ZohoConfiguration,
  HostApiConfiguration,
  MetaConfiguration,
  Go54Configuration,
};
