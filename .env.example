MONGODB_URI=

INTERNAL_API_KEY=
PORT=4000

PAYSTACK_PRIVATE_KEY=
PAYSTACK_PUBLIC_KEY=

#[<PERSON>IL<PERSON>  CONFIG]
ZILLA_SECRET_KEY=
ZILLA_PUBLIC_KEY=
ZILLA_SECRET_HASH=
ZILLA_MERCHANT_ID=

#[MONNIFY CONFIG]
MONIFFY_API_KEY=
MONIFFIY_SECRET_KEY=
MONIFFY_CONTRACT_CODE=

#SENTRY_DSN=
NATS_URL=nats://localhost:4222
NATS_USERNAME=
NATS_PASSWORD=

S3_BUCKET_NAME=
S3_REGION=
S3_ACCESS_KEY=
S3_SECRET_KEY=


REDIS_HOST=localhost
REDIS_TTL=90
REDIS_PORT=6379
REDIS_DB=0

JWT_SECRET=name
JWT_EXPIRY=3600000

TEST_MONGODB_URI=mongodb://localhost:27017/test_catlog